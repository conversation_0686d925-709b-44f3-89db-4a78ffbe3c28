<div class="flex h-full">
  <!-- SUB MENU -->
  <div
    class="w-10 flex items-center justify-center bg-gray-200 dark:bg-gray-800">
    <button
      mat-icon-button
      [matMenuTriggerFor]="menu"
      aria-label="Example icon-button with a menu">
      <mat-icon class="text-black dark:text-white">more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      @if (this.plotData()?.favorite === false) {
        <button
          mat-menu-item
          (click)="visualDataStore.markUnmarkPlotFavPlot(this.plotId, true)">
          <mat-icon class="text-black dark:text-white">star_border</mat-icon>
          <span class="dark:text-white">Mark as Favorite</span>
        </button>
      } @else {
        <button
          mat-menu-item
          (click)="visualDataStore.markUnmarkPlotFavPlot(this.plotId, false)">
          <mat-icon class="text-black dark:text-white">star</mat-icon>
          <span class="dark:text-white">Remove as Favorite</span>
        </button>
      }

      <button
        mat-menu-item
        (click)="visualDataStore.removePlotFromDb(this.plotId)">
        <mat-icon class="text-black dark:text-white">delete</mat-icon>
        <span class="dark:text-white">Remove Plot</span>
      </button>

      <button mat-menu-item (click)="openPlotDataFilterModal()">
        <mat-icon class="text-black dark:text-white">filter_list</mat-icon>
        <span class="dark:text-white">Filter Data</span>
      </button>
    </mat-menu>
  </div>

  <!-- DETAILS SECTION -->
  <div
    class="flex justify-center flex-col flex-1 min-w-0 bg-gray-200 dark:bg-gray-800 dark:text-white px-2">
    <div class="truncate overflow-hidden whitespace-nowrap">
      <span
        [matTooltip]="this.plotData()?.name"
        matTooltipPosition="right"
        matTooltipShowDelay="300"
        >{{ this.plotData()?.name }}</span
      >
    </div>
    <div class="truncate overflow-hidden whitespace-nowrap">
      {{ this.plotData()?.file_name }}
    </div>
  </div>

  <!-- FULL SCREEN BUTTON -->
  <div
    class="w-10 flex items-center justify-center bg-gray-200 dark:bg-gray-800 dark:text-white">
    <button (click)="toggleFullScreenMode()">
      <mat-icon class="screen">fullscreen_exit </mat-icon>
    </button>
  </div>
</div>
