<div class="w-full">
  <div class="flex justify-between items-center">
    <div class="flex items-center space-x-4">
      <button mat-icon-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <span class="font-medium text-lg">
        {{ capitalizeFirstLetter(fileName) }}
      </span>
    </div>
    <button
      *ngIf="activeSection === 'data'"
      (click)="handleRandomImageData()"
      class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-white !text-txt-color">
      <mat-icon>shuffle</mat-icon>
    </button>

    <div class="flex items-center space-x-1">
      <mat-button-toggle-group
        name="options"
        (change)="changeSection($event.value)"
        aria-label="Segmented button group"
        class="segmented-button-group rounded-md bg-white border-gray-100 h-[48px]">
        <mat-button-toggle checked value="data" class="min-w-[90px]"
          >Data</mat-button-toggle
        >
        <mat-button-toggle value="statistics" class="min-w-[90px]"
          >Statistics</mat-button-toggle
        >
      </mat-button-toggle-group>
    </div>
  </div>

  <div *ngIf="activeSection === 'data'" class="flex flex-col items-center">
    <section
      class="m-6 flex-grow flex items-center justify-center"
      tabindex="0">
      <img
        [src]="safeFileUrl"
        alt="File Preview"
        class="max-h-[70vh] w-[70vw] object-contain" />
    </section>
  </div>

  <app-images-statistics
    *ngIf="activeSection === 'statistics'"
    [fileID]="fileID"
    [isStatisticsLoading]="isStatisticsLoading"></app-images-statistics>
  <div class="flex justify-end w-full">
    <button mat-icon-button (click)="downloadData()">
      <mat-icon>file_download</mat-icon>
    </button>
  </div>
</div>
