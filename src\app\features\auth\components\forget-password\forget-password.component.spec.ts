import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ForgetPasswordComponent } from './forget-password.component';
import { Router } from '@angular/router';
import { AuthService } from '../../../../services/auth.services';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

describe('ForgetPasswordComponent', () => {
  let component: ForgetPasswordComponent;
  let fixture: ComponentFixture<ForgetPasswordComponent>;
  let authService: jest.Mocked<AuthService>;
  let router: jest.Mocked<Router>;
  let toastrService: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    const authServiceMock = {
      forgetPassword: jest.fn(),
    } as unknown as jest.Mocked<AuthService>;
    const routerMock = {
      navigateByUrl: jest.fn(),
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, ForgetPasswordComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ForgetPasswordComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;

    fixture.detectChanges(); // Initial binding and render
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with email control', () => {
    const emailControl = component.forgotPassword.get('email');
    expect(emailControl).toBeTruthy();
    expect(emailControl?.value).toBe('');
  });

  describe('Email validation', () => {
    it('should require email', () => {
      const emailControl = component.forgotPassword.get('email');
      emailControl?.setValue('');
      expect(emailControl?.hasError('required')).toBe(true);
    });

    it('should validate email format', () => {
      const emailControl = component.forgotPassword.get('email');
      emailControl?.setValue('invalid-email');
      expect(emailControl?.hasError('email')).toBe(true);
    });

    it('should accept valid email', () => {
      const emailControl = component.forgotPassword.get('email');
      emailControl?.setValue('<EMAIL>');
      expect(emailControl?.valid).toBe(true);
    });
  });

  describe('onSubmit', () => {
    it('should call forgetPassword on authService with form data', () => {
      const mockResponse = { message: 'Success', reset_url: '/reset/key123' };
      authService.forgetPassword.mockReturnValue(of(mockResponse));
      component.forgotPassword.get('email')?.setValue('<EMAIL>');

      component.onSubmit();

      expect(authService.forgetPassword).toHaveBeenCalledWith(
        component.forgotPassword.value,
      );
    });

    it('should navigate to new password page on success', () => {
      const mockResponse = { message: 'Success', reset_url: '/reset/key123' };
      authService.forgetPassword.mockReturnValue(of(mockResponse));

      component.onSubmit();

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message);
      expect(router.navigate).toHaveBeenCalledWith([
        '/auth/new-password',
        'key123',
      ]);
    });

    it('should show error message on failure', () => {
      const mockError = { error: { error: 'An error occurred' } };
      authService.forgetPassword.mockReturnValue(throwError(mockError));

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith(mockError.error.error);
    });
  });

  it('should navigate to sign-up page', () => {
    component.navigateTosignUp();
    expect(router.navigateByUrl).toHaveBeenCalledWith('/auth/register');
  });
});
