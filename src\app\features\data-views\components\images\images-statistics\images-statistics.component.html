<div
  #scrollContainer
  class="overflow-x-hidden overflow-y-auto m-4 max-h-[80vh]">
  <app-loader [loading]="isStatisticsLoading"></app-loader>

  <ng-container *ngFor="let graph of graphData; let i = index">
    <mat-card
      class="mat-elevation-z8 p-4 bg-white rounded-lg shadow-md mb-4"
      [id]="'graph-' + i">
      <div class="flex border-b border-gray-300 items-center justify-between">
        <div class="flex flex-col">
          <p class="font-semibold text-lg m-0">
            Image Distribution {{ i + 1 }}
          </p>
          <p class="text-gray-500">
            The images in the folder show this pixel intensity.
          </p>
        </div>
        <div class="flex items-center space-x-2">
          <button mat-icon-button>
            <mat-icon class="text-btn-color">short_text</mat-icon>
          </button>
          <p class="text-sm m-0 text-txt-color">Show Information</p>
          <button mat-icon-button (click)="downloadSinglePlot(graph.plot_name)">
            <mat-icon>download</mat-icon>
          </button>
          <button
            mat-icon-button
            (click)="openFullscreen(i)"
            *ngIf="!isFullScreen">
            <mat-icon>fullscreen</mat-icon>
          </button>
          <mat-icon
            class="screen"
            (click)="closeFullscreen()"
            *ngIf="isFullScreen"
            >fullscreen_exit</mat-icon
          >
        </div>
      </div>

      <mat-card-content>
        <div>
          <plotly-plot
            [layout]="graph.json_data.layout"
            [useResizeHandler]="true"
            [data]="graph.json_data.data"
            [style]="{
              position: 'relative',
              width: '100%',
              height: '100%',
            }"></plotly-plot>
        </div>
        <div class="flex justify-between items-center mt-4">
          <p class="text-sm">
            {{
              graph.title || 'Cohort 1 Train Image Pixel Intensity Distribution'
            }}
          </p>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-container>

  <div *ngIf="isStatisticsLoading">Loading more statistics...</div>
</div>
