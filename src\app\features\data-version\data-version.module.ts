import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataVersionComponent } from './data-version/data-version.component';
import { DataVersionRoutingModule } from './data-version-routing.module';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Combined imports
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon'; // Fixed import
import { MatTabsModule } from '@angular/material/tabs';
import { SharedModule } from '../../shared/shared.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatOptgroup, MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input'; // Fixed import
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';

@NgModule({
  declarations: [DataVersionComponent],
  imports: [
    CommonModule,
    DataVersionRoutingModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    FormsModule,
    MatButtonModule,
    MatDividerModule,
    MatListModule,
    ReactiveFormsModule,
    SharedModule,
    MatChipsModule,
    MatExpansionModule,
    MatOptgroup,
    MatOptionModule,
    MatTabsModule,
    MatCheckboxModule,
    MatTooltipModule,
  ],
  exports: [DataVersionComponent],
})
export class DataVersionModule {}
