td,
th {
  text-align: center;
  border-right: 1px solid #e5e5f4;
  border: 1px solid #ecebf8;
}

td:last-child,
th:last-child {
  border-right: none;
}

table tr:nth-child(odd) {
  background-color: #f1f3f9;
}
table {
  border: 1px solid #ecebf8;
}

table th {
  background-color: #f7f9ff;
}

td {
  color: #82868e;
}

.failed {
  color: red;
  text-transform: capitalize;
}

.completed {
  color: green;
  text-transform: capitalize;
}
