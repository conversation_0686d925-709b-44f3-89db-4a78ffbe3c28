import { inject, Injectable } from '@angular/core';
import {
  ColorPalette,
  PlotStyle,
  PlotStyleOption,
} from '../../../_models/visual-data/visual-data.model';
import { FormBuilder, FormGroup } from '@angular/forms';

@Injectable({
  providedIn: 'root',
})
export class PlotDataSettingsService {
  private fb = inject(FormBuilder);

  getFormGroups(chartContent: PlotStyleOption | null) {
    const children = chartContent?.children ?? [];
    return this.fb.group(
      children.reduce((group: Record<string, string[] | FormGroup>, field) => {
        if (field.children.length === 0) {
          group[field.name] = [field.value];
        } else {
          group[field.name] = this.fb.group(
            field.children.reduce(
              (group: Record<string, string[] | FormGroup>, field) => {
                group[field.name] = [field.value];
                return group;
              },
              {},
            ),
          );
        }
        return group;
      }, {}),
    );
  }

  convertFormToPlotStyle(
    projectId: number | undefined,
    formValues: PlotStyleForm,
  ): PlotStyle {
    return {
      ...formValues.selectedPlotStyle,
      project: projectId,
      name: formValues.selectedPlotStyle.name,
      color_palette: formValues.selectedColorPalette.id ?? null,
      id: formValues.selectedPlotStyle.id,
      options: [...formValues.selectedPlotStyle.options],
    };
  }
}

export interface PlotStyleForm {
  selectedPlotStyle: PlotStyle;
  selectedColorPalette: ColorPalette;
}
