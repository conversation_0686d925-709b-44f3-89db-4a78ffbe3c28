import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ElementRef,
  NgZone,
} from '@angular/core';
import {
  FileSystemDirectoryEntry,
  FileSystemFileEntry,
  NgxFileDropEntry,
} from 'ngx-file-drop';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import JSZip from 'jszip';
import { DataviewService } from '../../services/data-view.service';
import { ToastrService } from 'ngx-toastr';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { environment } from '../../../../env/env';
import {
  ColumnDataType,
  ColumnIDSetData,
  DataViewBackendResponse,
  DirectoryEntry,
  FolderStructure,
  FolderStructureDetail,
  FormInfo,
  ProcessFileStatusInterface,
  SuccessfulFile,
} from '../../models/data-view.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-file-upload-modal',
  templateUrl: './file-upload-modal.component.html',
  styleUrls: ['./file-upload-modal.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class FileUploadModalComponent implements OnInit, OnDestroy {
  @Input() isModalOpen = false;
  @Input() modalTitle = '';
  @Output() isModalClose = false;
  @Output() isuploadModalEvent = new EventEmitter<boolean>();
  addOnStorageModal = false;
  isuploadModal = false;
  imageName = '';
  openFromInfoModal = false;
  formInfoArray: FormInfo[] = [];
  data: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  columns: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  dataTypesArray: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  isImageUpload = false;
  loading = false;
  formData: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  folder_id = localStorage.getItem('folder_id');
  rootFolderId = '';
  fileForm!: FormGroup;
  filePanels: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  isDataLoaded: boolean[] = [];
  fileStatusSubscription: Subscription = new Subscription();
  public files: NgxFileDropEntry[] = [];
  private directoryId = 1;
  private rootDirectory: DirectoryEntry = {
    id: this.directoryId++,
    type: 'directory',
    name: 'root',
    children: [],
  };
  successfullUploadMessage: string | null = null;

  // New properties for multiple file upload
  uploadedFilesList: {
    file: File;
    progress: number;
    uploading: boolean;
  }[] = [];

  // Flag to prevent multiple toast notifications
  private toastShown = false;

  constructor(
    private http: HttpClient,
    private dataViewService: DataviewService,
    private toastrService: ToastrService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
  ) {}

  ngOnInit() {
    this.fileForm = this.fb.group({
      uploadedFiles: this.fb.array([]),
    });

    this.filePanels.forEach(file => {
      this.addFileForm(file);
    });
  }

  private resetModalState() {
    // Reset all state variables
    this.uploadedFilesList = [];
    this.rootDirectory.children = [];
    this.loading = false;
    this.filePanels = [];
    this.isImageUpload = false;
    this.selectedColumnIds = [];
    this.successfullUploadMessage = '';

    // Reset the form
    if (this.fileForm) {
      this.fileForm.reset();
      while (this.uploadedFiles.length) {
        this.uploadedFiles.removeAt(0);
      }
    }
  }
  get uploadedFiles(): FormArray {
    return this.fileForm.get('uploadedFiles') as FormArray;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  addFileForm(file: any) {
    const fileGroup = this.fb.group({
      file_id: [file.fileID, Validators.required],
      fileName: [file.fileName],
      header_row: [file.fileMetaData?.header_row],
      delimiter: [file.fileMetaData?.delimiter, Validators.required],
      data_type_rows: [file.fileMetaData?.data_type_rows],
      columns: this.fb.array(file.columns || []),
    });
    this.uploadedFiles.push(fileGroup);
  }

  // Format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Check file types
  isImageFile(file: File): boolean {
    return file.type.startsWith('image/');
  }

  isCsvFile(file: File): boolean {
    return file.name.endsWith('.csv');
  }

  isParquetFile(file: File): boolean {
    return file.name.endsWith('.parquet');
  }

  // Remove a file from the list
  removeFile(index: number) {
    this.ngZone.run(() => {
      this.uploadedFilesList = [
        ...this.uploadedFilesList.slice(0, index),
        ...this.uploadedFilesList.slice(index + 1),
      ];
    });
  }

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  openFileSelector() {
    this.ngZone.run(() => {
      if (this.fileInput && this.fileInput.nativeElement) {
        this.fileInput.nativeElement.click();
      } else {
        console.warn('File input element not available');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.multiple = true;
        fileInput.accept = '.csv,.parquet,.jpg,.jpeg,.png';

        fileInput.onchange = event => {
          const files = (event.target as HTMLInputElement).files;
          if (files && files.length > 0) {
            this.addFilesToList(files);
          }
        };
        fileInput.click();
      }
    });
  }

  onFileInputChange(event: Event) {
    const element = event.target as HTMLInputElement;
    const files = element?.files;

    if (files?.length) {
      this.addFilesToList(files);

      // Force UI update
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      });
    }

    // Reset the input to allow selecting the same file again
    element.value = '';
  }

  // Add files to the list - handles both single files and collections
  addFilesToList(files: File | File[] | FileList) {
    // Convert the FileList or single file to an array if needed
    const fileArray: File[] =
      files instanceof FileList
        ? Array.from(files)
        : Array.isArray(files)
          ? files
          : [files];

    // Handle each file
    fileArray.forEach(file => {
      // Skip if file is already in the list (avoid duplicates)
      const fileAlreadyExists = this.uploadedFilesList.some(
        item => item.file.name === file.name && item.file.size === file.size,
      );

      if (fileAlreadyExists) {
        return;
      }

      // Add the file to the list with initial progress of 0
      this.uploadedFilesList.push({
        file,
        progress: 0,
        uploading: false,
      });

      // Add file form if this is a CSV file to collect metadata
      if (this.isCsvFile(file)) {
        this.addFileForm({
          fileName: file.name,
          fileID: null,
        });
      }
    });
    this.uploadedFilesList = [...this.uploadedFilesList];
  }

  // Process all files when user clicks the upload button
  processFiles() {
    if (this.uploadedFilesList.length === 0) {
      return;
    }

    // Reset state for new upload session
    this.loading = true;
    this.rootDirectory.children = [];
    this.toastShown = false;

    // Separate zip files from regular files
    const zipFiles = this.uploadedFilesList.filter(item =>
      item.file.name.toLowerCase().endsWith('.zip'),
    );

    const regularFiles = this.uploadedFilesList.filter(
      item => !item.file.name.toLowerCase().endsWith('.zip'),
    );

    // Process both types of files
    const processPromises: Promise<void>[] = [];

    // CASE 1: Process ZIP files if any exist
    if (zipFiles.length > 0) {
      zipFiles.forEach(zipFileItem => {
        zipFileItem.uploading = true;
        zipFileItem.progress = 10;

        // Create a promise for this ZIP file upload
        const zipPromise = new Promise<void>(resolve => {
          this.uploadDirectoryStructureOrZipFile(zipFileItem.file)
            .then(() => {
              zipFileItem.uploading = false;
              zipFileItem.progress = 100;
              resolve();
            })
            .catch(error => {
              console.error(
                `DEBUG: Error uploading ZIP file ${zipFileItem.file.name}:`,
                error,
              );
              zipFileItem.uploading = false;
              zipFileItem.progress = 0;
              resolve();
            });
        });

        processPromises.push(zipPromise);
      });
    }

    // CASE 2: Process regular files if any exist
    if (regularFiles.length > 0) {
      const fileSize = regularFiles[0]?.file.size || 0;

      // Process regular files - convert all to base64 and add to rootDirectory
      const regularFilesPromise = new Promise<void>(resolveAllRegular => {
        // Convert each regular file to base64 and add to rootDirectory
        const filePromises = regularFiles.map(fileItem => {
          return new Promise<void>((resolve, reject) => {
            const file = fileItem.file;
            fileItem.uploading = true;
            fileItem.progress = 10;

            this.fileToBase64(file)
              .then(base64 => {
                fileItem.progress = 50;

                // Add file to root directory structure for upload
                const fileEntry = {
                  id: this.directoryId++,
                  type: 'file' as const,
                  name: file.name,
                  path: base64,
                };

                this.rootDirectory.children.push(fileEntry);
                fileItem.progress = 70;
                resolve();
              })
              .catch(error => {
                console.error(
                  `DEBUG: Error processing file ${file.name}:`,
                  error,
                );
                fileItem.progress = 0;
                reject(error);
              });
          });
        });

        // After all regular files are processed, prepare to upload them
        Promise.all(filePromises)
          .then(() => {
            if (this.rootDirectory.children.length > 0) {
              regularFiles.forEach(item => (item.progress = 80));
              this.uploadFileStructure(fileSize)
                .then(() => {
                  regularFiles.forEach(item => (item.progress = 100));
                  resolveAllRegular();
                })
                .catch(error => {
                  console.error('DEBUG: Error uploading regular files:', error);
                  resolveAllRegular();
                });
            } else {
              resolveAllRegular();
            }
          })
          .catch(error => {
            console.error(
              'DEBUG: Error in batch processing regular files:',
              error,
            );
            resolveAllRegular();
          });
      });

      processPromises.push(regularFilesPromise);
    }

    Promise.all(processPromises)
      .then(() => {
        if (this.uploadedFilesList.length === 0) {
          this.loading = false;
        }
      })
      .catch(error => {
        console.error('DEBUG: Error in batch file processing:', error);
        this.loading = false;
        this.toastrService.error('Error processing files');
      });
  }

  private async uploadFileStructure(fileSize: number) {
    // Check if we have files to process
    if (this.rootDirectory.children.length === 0) {
      this.loading = false;
      return;
    }

    // Process each file
    for (const fileEntry of this.rootDirectory.children) {
      if (fileEntry.type === 'file') {
        const fileBlob = await this.getFileBlob(fileEntry);
        if (!fileBlob) {
          console.error('Failed to get file blob for', fileEntry.name);
          this.toastrService.error('Error processing file', fileEntry.name);
          continue;
        }

        // Get folder ID from localStorage
        const folder_id = localStorage.getItem('folder_id');
        if (!folder_id) {
          console.error('No folder_id found in localStorage');
          continue;
        }

        // Get signed URL for upload
        this.dataViewService
          .uploadFileData(folder_id, { filename: fileEntry.name }, fileSize)
          .subscribe(
            uploadUrl => {
              if (uploadUrl.status === 'success') {
                const signedUrl = uploadUrl.data.signed_url;
                const signedKey = uploadUrl.data.file_key;

                // Upload to the signed URL
                this.dataViewService
                  .signedUrlUpload(signedUrl, fileBlob)
                  .subscribe(
                    () => {
                      // Check upload status
                      this.dataViewService
                        .checkFileUploadStatus(folder_id, {
                          filename: signedKey,
                        })
                        .subscribe(data => {
                          if (data?.data.id) {
                            // Check file processing status
                            this.dataViewService
                              .checkFileProcess(data.data.id)
                              .subscribe(progress => {
                                const { task_id } = progress.data;
                                if (task_id) {
                                  this.checkFileStatus(task_id);
                                }
                              });
                          } else {
                            this.loading = false;
                          }
                        });
                    },
                    error => {
                      console.error('Error uploading file:', error);
                      this.loading = false;
                      this.cdr.detectChanges();
                    },
                  );
              }
            },
            error => {
              console.error('Error fetching signed URL:', error);
              this.loading = false;
              this.cdr.detectChanges();
            },
          );
      }
    }
  }

  public dropped(files: NgxFileDropEntry[]) {
    // Reset the root directory to ensure clean structure
    this.rootDirectory.children = [];
    this.toastShown = false;

    // Check if this is a directory drop by seeing if files have relativePath with directory structure
    const hasDirectoryStructure = files.some(
      file =>
        file.relativePath &&
        file.relativePath.includes('/') &&
        file.fileEntry.isFile,
    );

    const isZipFile = files.some(file => /\.zip$/i.test(file.relativePath));

    // CASE 1: Folder upload -> preserve directory structure and immediately upload
    if (hasDirectoryStructure) {
      // This is a directory drop - process the files and preserve the directory structure
      this.loading = true;

      let processedCount = 0;
      const totalCount = files.length;

      for (const droppedFile of files) {
        if (droppedFile.fileEntry.isFile) {
          const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
          fileEntry.file((file: File) => {
            this.fileToBase64(file)
              .then(base64 => {
                // Use relativePath to maintain directory structure
                const relativePath = droppedFile.relativePath || file.name;
                this.addFileToDirectory(
                  this.rootDirectory,
                  relativePath,
                  base64,
                );

                processedCount++;
                if (processedCount === totalCount) {
                  setTimeout(() => {
                    if (this.rootDirectory.children.length > 0) {
                      this.uploadDirectoryStructureOrZipFile();
                    } else {
                      this.loading = false;
                    }
                  }, 500);
                }
              })
              .catch(error => {
                console.error(
                  `DEBUG: Error processing file ${file.name}:`,
                  error,
                );
                processedCount++;
              });
          });
        } else if (droppedFile.fileEntry.isDirectory) {
          processedCount++;
        }
      }
    }
    // CASE 2: ZIP file upload -> immediately upload without additional processing
    else if (isZipFile) {
      const fileEntry = files[0].fileEntry as FileSystemFileEntry;
      this.loading = true;
      this.ngZone.run(() => {
        setTimeout(() => {
          fileEntry.file((file: File) => {
            this.uploadDirectoryStructureOrZipFile(file);
          });
        }, 100);
      });
    }
    // CASE 3: Individual file upload -> add to list for later processing
    else {
      const fileEntries = files.map(
        droppedFile => droppedFile.fileEntry as FileSystemFileEntry,
      );

      // Convert FileSystemFileEntry objects to File objects
      Promise.all(
        fileEntries.map(
          fileEntry =>
            new Promise<File>((resolve, reject) => {
              try {
                fileEntry.file(file => {
                  resolve(file);
                });
              } catch (error) {
                console.error('DEBUG: Error converting file entry:', error);
                reject(error);
              }
            }),
        ),
      )
        .then(convertedFiles => {
          this.addFilesToList(convertedFiles);

          this.ngZone.run(() => {
            this.cdr.markForCheck();
          });
        })
        .catch(error => {
          console.error('DEBUG: Error processing dropped files:', error);
          this.toastrService.error('Error processing dropped files');
        });
    }
  }

  // This function is now deprecated as we're using traverseDirectory for proper structure
  // It remains for compatibility with older code paths
  private processDirectory(
    directoryEntry: FileSystemDirectoryEntry,
    fileCollection: File[],
  ) {
    // Process all entries in the directory
    directoryEntry.createReader().readEntries(entries => {
      let processedEntries = 0;
      const totalEntries = entries.length;

      if (totalEntries === 0) return;

      for (const entry of entries) {
        if (entry.isFile) {
          const fileEntry = entry as FileSystemFileEntry;
          fileEntry.file((file: File) => {
            fileCollection.push(file);

            // Count processed entries
            processedEntries++;

            // If all entries in this directory are processed
            if (processedEntries === totalEntries) {
              this.ngZone.run(() => {
                this.addFilesToList(fileCollection);
              });
            }
          });
        } else if (entry.isDirectory) {
          // Recursively process subdirectories
          this.processDirectory(
            entry as FileSystemDirectoryEntry,
            fileCollection,
          );

          // Count directories as processed
          processedEntries++;
        }
      }
    });
  }

  private async uploadDirectoryStructureOrZipFile(zipFile?: File) {
    // Ensure loading state is visible in the UI
    this.ngZone.run(() => {
      this.loading = true;
      this.cdr.detectChanges();
    });

    const formData = new FormData();
    let fileSize: number | string = 0;

    if (zipFile) {
      // Direct zip file upload case
      formData.append('file', zipFile, zipFile.name);
      fileSize = zipFile.size;
    } else {
      // Directory structure case - create zip from rootDirectory
      const zip = new JSZip();

      // Make sure we have something to zip
      if (this.rootDirectory.children.length === 0) {
        console.error('No directory structure to upload');
        this.loading = false;
        return;
      }

      // Use the first directory/file as the root for the zip
      this.addFolderToZip(zip, this.rootDirectory.children[0]);

      const zipBlob = await zip.generateAsync({ type: 'blob' });
      formData.append(
        'file',
        zipBlob,
        `${this.rootDirectory.children[0].name}.zip`,
      );
      fileSize = zipBlob.size;
    }

    // Add the size to the form data
    formData.append('size', fileSize.toString());
    const folder_id = localStorage.getItem('folder_id');
    if (!folder_id) {
      console.error('No folder_id found in localStorage');
      return;
    }

    const headers = new HttpHeaders({
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    });

    this.http
      .post<
        DataViewBackendResponse<FolderStructure>
      >(`${environment.apiUrl}files/files/upload-folder/${folder_id}/`, formData, { headers })
      .subscribe(
        (response: DataViewBackendResponse<FolderStructure>) => {
          this.ngZone.run(() => {
            if (!this.toastShown) {
              this.toastrService.success(
                response.message || 'Files uploaded successfully',
              );
              this.toastShown = true;
            }
            if (response?.data?.folder_structure) {
              const folderStructure = response.data.folder_structure;
              this.checkEntireFolderStatus(folderStructure);
            } else {
              console.error(
                'Error: Folder structure not found in the upload response',
              );
              this.loading = false;
              this.cdr.detectChanges();
            }
          });
        },
        error => {
          this.ngZone.run(() => {
            // Only show toast if one hasn't been shown yet
            if (!this.toastShown) {
              this.toastrService.error(
                error.message || 'Error uploading files',
              );
              this.toastShown = true;
            }
            this.loading = false;
            this.isModalOpen = false;
            console.error('Error uploading directory structure:', error);
            if (
              error.error &&
              error.error.message &&
              error.error.message.toLowerCase().includes('storage')
            )
              this.addOnStorageModal = true;
            this.cdr.detectChanges();
          });
        },
      );
  }

  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
      reader.readAsDataURL(file);
    });
  }

  private traverseDirectory(
    directoryEntry: FileSystemDirectoryEntry,
    parentDirectory: DirectoryEntry,
  ) {
    const reader = directoryEntry.createReader();
    reader.readEntries(entries => {
      const directory: DirectoryEntry = {
        id: this.directoryId++,
        type: 'directory',
        name: directoryEntry.name,
        children: [],
      };

      parentDirectory.children.push(directory);

      for (const entry of entries) {
        if (entry.isFile) {
          const fileEntry = entry as FileSystemFileEntry;
          fileEntry.file((file: File) => {
            this.fileToBase64(file).then(base64 => {
              const relativePath = `${directory.name}/${file.name}`;
              this.addFileToDirectory(parentDirectory, relativePath, base64);
            });
          });
        } else if (entry.isDirectory) {
          const subDirEntry = entry as FileSystemDirectoryEntry;
          this.traverseDirectory(subDirEntry, directory);
        }
      }
    });
  }

  private addFileToDirectory(
    directory: DirectoryEntry,
    relativePath: string,
    base64: string,
  ) {
    const parts = relativePath.split('/');
    let currentDirectory = directory;

    for (const part of parts.slice(0, -1)) {
      let subDir = currentDirectory.children.find(
        child => child.type === 'directory' && child.name === part,
      ) as DirectoryEntry;
      if (!subDir) {
        subDir = {
          id: this.directoryId++,
          type: 'directory',
          name: part,
          children: [],
        };
        currentDirectory.children.push(subDir);
      }
      currentDirectory = subDir;
    }

    currentDirectory.children.push({
      id: this.directoryId++,
      type: 'file',
      name: parts[parts.length - 1],
      path: base64,
    });
  }

  private checkFileStatus(taskId: string) {
    this.fileStatusSubscription.add(
      this.dataViewService.checkProcessFileStatus(taskId).subscribe({
        next: (res: ProcessFileStatusInterface) => {
          if (res.data.status === 'SUCCESS') {
            this.loading = false;
            this.isModalOpen = false;
            this.closeFileUploadModal(false);
            if (!this.toastShown) {
              this.toastrService.success(
                'File processing completed successfully',
              );
              this.toastShown = true;
            }
            this.cdr.markForCheck();
          } else {
            // Wait for 5 seconds, then check again
            setTimeout(() => this.checkFileStatus(taskId), 500);
          }
        },
        error: () => {
          this.isModalOpen = false;
          this.loading = false;
          this.cdr.markForCheck();
        },
      }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private async getFileBlob(fileEntry: any): Promise<Blob | null> {
    if (fileEntry && fileEntry.path) {
      const base64Data = fileEntry.path;
      const fileName = fileEntry.name;
      try {
        const fileExtension = fileName.split('.').pop()?.toLowerCase();
        let mimeType = 'application/octet-stream';
        switch (fileExtension) {
          case 'csv':
            mimeType = 'text/csv';
            break;
          case 'parquet':
            mimeType = 'application/json';
            break;
          case 'xml':
            mimeType = 'application/xml';
            break;
          case 'yaml':
          case 'yml':
            mimeType = 'application/x-yaml';
            break;
          case 'tsv':
            mimeType = 'text/tab-separated-values';
            break;
          case 'txt':
            mimeType = 'text/plain';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'jpeg':
          case 'jpg':
            mimeType = 'image/jpeg';
            break;
          default:
            mimeType = 'application/octet-stream';
        }
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mimeType });

        return blob;
      } catch (error) {
        console.error('Error creating Blob from base64:', error);
        this.loading = false;

        return null;
      }
    }
    return null;
  }

  // The uploadDirectoryStructure function has been consolidated into uploadDirectoryStructureOrZipFile
  // to avoid code duplication

  private checkEntireFolderStatus(folder: FolderStructureDetail) {
    this.CheckFolderStatus(folder);
  }
  SavedColumnsID(file_id: number, columnName: string) {
    this.dataViewService.updateColumnID(file_id, columnName).subscribe(
      (response: DataViewBackendResponse<ColumnIDSetData>) => {
        if (response && response.data && response.data.columns) {
          this.filePanels.push({
            file_id,
            fileName: response.data.file_name || `File ${file_id}`,
            fileId: file_id,
            columns: response.data.columns,
          });
        }
      },
      (error: Error) => {
        console.error('Error fetching columns for file:', error);
        this.loading = false;
      },
    );
  }
  CheckFolderStatus(folderStructure: FolderStructureDetail) {
    this.dataViewService
      .toCheckTheFolderStatus({ folder_structure: folderStructure })
      .subscribe(
        response => {
          this.successfullUploadMessage =
            response.data.upload_statistics.message;
          // this.isuploadModal = true;
          this.isModalOpen = false;

          if (!this.toastShown) {
            this.toastrService.success(
              response.message || 'Files processed successfully',
            );
            this.toastShown = true;
          }
          this.isModalOpen = false;

          const fileIds = response.data.processing_files;

          if (
            response &&
            response.data &&
            response.data.processing_files &&
            response.data.processing_files.length > 0
          ) {
            const processingFiles = response.data.successful_files;
            this.dataViewService.processBatch(fileIds).subscribe(res => {
              if (res.status === 'success') {
                processingFiles.forEach((eachData: SuccessfulFile) => {
                  if (!eachData.further_processing) {
                    this.fetchProcessedFileData(eachData.file_id);
                  } else {
                    this.fetchProcessedFileData(eachData.file_id);
                    this.closeFileUploadModal(false);
                    // commenting it, becauase this can be used for future reference

                    // this.fetchSavedColumns(eachData.file_id);
                    // this.fetchColoumnsIds(eachData.file_id, index);

                    // this.addFileForm({
                    //   fileID: eachData.file_id,
                    //   fileMetaData: {
                    //     fileName: '',
                    //     header_row: '',
                    //     delimiter: '',
                    //     data_type_rows: '',
                    //   },
                    //   columns: [],
                    // });
                    this.isDataLoaded[eachData.file_id] = true;
                    // this.fetchColoumnsIds(eachData.file_id);
                  }
                });
              }
            });
          } else {
            console.error('No processing files found in the response.');
            this.loading = false;
          }
        },
        (error: Error) => {
          if (!this.toastShown) {
            this.toastrService.error(
              error.message || 'Error checking folder status',
            );
            this.toastShown = true;
          }
          console.error('Error checking folder status:', error);
          this.loading = false;
        },
      );
  }

  trackByIndex(index: number): number {
    return index;
  }

  fetchSavedColumns(id: number): void {
    this.dataViewService.toGetColumnsData(id).subscribe(
      response => {
        if (response?.data.columns) {
          this.filePanels.push({
            id,
            fileName: response.data.file_name || `File ${id}`,
            fileId: id,
            fileMetaData: response.data.file_metadata,
            columns: response.data.columns,
          });
        }
      },
      (error: Error) => {
        console.error(`Error fetching columns for file ID ${id}:`, error);
        this.loading = false;
      },
    );
  }

  fetchProcessedFileData(id: number) {
    this.dataViewService.toGetFileData(id).subscribe(
      response => {
        if (response) {
          if (response && response.data && response.data.json_data) {
            this.data = Object.values(response.data.json_data.data);
            this.columns = Object.values(response.data.columns);
            this.fetchDataTypes();
          }
          this.loading = false;
          this.cdr.markForCheck();
        }
      },
      (error: Error) => {
        console.error('Error uploading file:', error);
        this.loading = false;
        this.cdr.markForCheck();
      },
    );
  }

  SetFileMetaData(fileId: number, body: unknown) {
    this.dataViewService.setFileMetaData(fileId, body).subscribe(
      response => {
        if (response && response.data && response.data.json_data) {
          this.data = response.data.json_data.data;
          this.columns = Object.values(response.data.columns);
          this.fetchDataTypes();
          if (!this.toastShown) {
            this.toastrService.success('Data retrieved successfully');
            this.toastShown = true;
          }
        }
      },
      (error: Error) => {
        console.error('Error uploading file:', error);
        this.loading = false;
      },
    );
  }

  fetchDataTypes() {
    this.dataViewService.toGetColumnDataTypes().subscribe(
      (response: ColumnDataType) => {
        const typedResponse = response as unknown as Record<string, FormInfo>;
        this.dataTypesArray = Object.values(typedResponse);
        this.formInfoArray = this.dataTypesArray.map((item: FormInfo) => ({
          name: item.name,
          description: item.description,
          id: item.id,
        }));
        this.cdr.detectChanges();
      },
      error => {
        console.error('Error uploading file:', error);
      },
    );
  }

  openInfoModal() {
    if (this.formInfoArray) this.openFromInfoModal = !this.openFromInfoModal;
  }
  toggleActive(panelIndex: number, columnIndex: number) {
    const panel = this.filePanels[panelIndex];
    const column = panel.columns[columnIndex];
    column.isActive = !column.isActive;
  }

  selectedColumnIds: string[][] = [];

  fetchColoumnsIds(id: number, index?: number) {
    this.dataViewService.getColumnID(id).subscribe(
      (response: DataViewBackendResponse<Record<string, string[]>>) => {
        if (
          response &&
          response.data &&
          response.data['Choices for ID_Columns']
        ) {
          const choicesForIdColumns = response.data['Choices for ID_Columns'];
          if (!Array.isArray(this.selectedColumnIds)) {
            this.selectedColumnIds = [];
          }

          const initialColumnId: string = choicesForIdColumns[0];
          this.selectedColumns.push({
            columnName: initialColumnId,
            panelIndex: index,
          });
          this.selectedColumnIds.push(choicesForIdColumns);
        }
      },
      (error: Error) => {
        console.error('Error fetching column IDs:', error);
        this.loading = false;
      },
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private addFolderToZip(zip: JSZip, directory: any, parentPath = '') {
    const folder = zip.folder(directory.name);

    for (const child of directory.children) {
      if (child.type === 'file') {
        folder?.file(child.name, child.path, { base64: true });
      } else if (child.type === 'directory') {
        this.addFolderToZip(folder!, child, `${parentPath}/${child.name}`);
      }
    }
  }

  closeModal() {
    this.isModalOpen = false;
    this.openFromInfoModal = false;
    this.isuploadModalEvent.emit(this.isModalOpen);

    // Reset state when modal is closed
    this.resetModalState();
  }

  openAddOnStorage(data: {
    buyPlanModel: boolean;
    reOpenUploadModel?: boolean;
  }) {
    this.addOnStorageModal = data.buyPlanModel;
    if (!this.addOnStorageModal)
      this.isModalOpen = data.reOpenUploadModel || false;
    this.cdr.detectChanges();
  }

  openFileUploadModal() {
    this.isuploadModal = true;
    this.isModalOpen = false;
  }
  selectedColumns: { columnName: string; panelIndex?: number }[] = [];

  initializeColumnSelection(columnName: string, panelIndex?: number) {
    const existingIndex = this.selectedColumns.findIndex(
      item => item.panelIndex === panelIndex,
    );

    if (existingIndex === -1) {
      this.selectedColumns.push({ columnName, panelIndex });
    }
  }

  onRadioClick(columnName: string, panelIndex?: number) {
    const existingIndex = this.selectedColumns.findIndex(
      item => item.panelIndex === panelIndex,
    );

    if (existingIndex !== -1) {
      this.selectedColumns[existingIndex].columnName = columnName;
    } else {
      this.selectedColumns.push({ columnName, panelIndex });
    }
  }

  closeFileUploadModal(check: boolean) {
    if (!check) {
      const formData = this.uploadedFiles.controls.map(
        (fileGroup: AbstractControl) => ({
          file_id: fileGroup.get('file_id')?.value,
          fileName: fileGroup.get('fileName')?.value,
        }),
      );
      formData.forEach((fileData, index) => {
        this.selectedColumns.forEach(eachselectedColumns => {
          if (eachselectedColumns.panelIndex === index) {
            this.SavedColumnsID(
              fileData.file_id,
              eachselectedColumns.columnName,
            );
          }
        });
      });
    }
    this.isuploadModal = false;
    this.isModalOpen = false;

    // Reset all state when modal is closed
    this.resetModalState();

    this.isuploadModalEvent.emit(this.isuploadModal);
  }

  saveChanges() {
    const formData = this.uploadedFiles.controls.map(
      (fileGroup: AbstractControl) => ({
        file_id: fileGroup.get('file_id')?.value,
        fileName: fileGroup.get('fileName')?.value,
        header_row: fileGroup.get('header_row')?.value,
        delimiter: fileGroup.get('delimiter')?.value,
        data_type_rows: fileGroup.get('data_type_rows')?.value,
      }),
    );

    formData.forEach((fileData, index) => {
      if (fileData.fileName !== null) {
        this.dataViewService
          .updateFileName(fileData.fileName, fileData.file_id)
          .subscribe(res => {
            if (res.status === 'success') {
              if (!this.toastShown) {
                this.toastrService.success(
                  res.message || 'Operation completed successfully',
                );
                this.toastShown = true;
              }
            }
          });
      }
      this.SetFileMetaData(fileData.file_id, fileData);
      this.selectedColumns.forEach(eachselectedColumns => {
        if (eachselectedColumns.panelIndex === index) {
          this.SavedColumnsID(fileData.file_id, eachselectedColumns.columnName);
        }
      });
    });
    this.closeFileUploadModal(true);
    this.dataViewService
      .getAllDataView(String(localStorage.getItem('rootFolderId')), 1, 1, true)
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      .subscribe(() => {});
  }

  discardChanges() {
    this.fileForm.reset();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.fileStatusSubscription.unsubscribe();

    // Reset state
    this.resetModalState();
  }
}
