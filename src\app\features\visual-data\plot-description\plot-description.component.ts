import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PlotService } from '../../../services/plot.services';
import { ToastrService } from 'ngx-toastr';
import { UserPlotRequest } from '../../../_models/visual-data/plot.model';

@Component({
  selector: 'app-plot-description',
  templateUrl: './plot-description.component.html',
  styleUrls: ['./plot-description.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class PlotDescriptionComponent implements OnInit {
  @Output() closeModal = new EventEmitter<boolean>();
  @Input() plotId = 0;
  data: UserPlotRequest | null = null;
  loading = false;
  @Output() cancelEvent = new EventEmitter<boolean>();

  constructor(
    private plotService: PlotService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.getDescriptionValues();
  }

  getDescriptionValues(): void {
    this.plotService.getPlotDescription(this.plotId).subscribe(
      response => {
        this.data = response.data;
        this.loading = false;
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }
  cancel(): void {
    this.cancelEvent.emit(true);
  }
}
