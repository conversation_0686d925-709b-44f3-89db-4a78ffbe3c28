import {
  AfterViewInit,
  Component,
  computed,
  effect,
  ElementRef,
  HostListener,
  Inject,
  inject,
  <PERSON><PERSON>one,
  OnInit,
  signal,
  ViewChild,
  viewChild,
} from '@angular/core';
import { g_const } from '../../../_utility/global_const';
import {
  PlotLayoutPayload,
  PlotService,
} from '../../../services/plot.services';
import { ToastrService } from 'ngx-toastr';
import {
  bufferTime,
  combineLatest,
  filter,
  fromEvent,
  map,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { SearchOptions } from '../../../_models/common.model';
import { Data, Layout } from 'plotly.js';
import { ActivatedRoute } from '@angular/router';
import { ProjectDataDetails } from '../../dashborad/models/project.model';
import {
  CompactType,
  DisplayGrid,
  GridsterComponent,
  GridsterConfig,
  GridsterItem,
  GridsterItemComponentInterface,
  GridsterPush,
  GridType,
} from 'angular-gridster2';
import {
  MatPaginator,
  MatPaginatorIntl,
  PageEvent,
} from '@angular/material/paginator';
import {
  BackendResponse,
  PlotResponse,
  PlotResponseAll,
  UserPlot,
} from '../../../_models/visual-data/visual-data.model';
import { PlotTypeValue } from '../../../_models/visual-data/explore-view.model';
import { IntroService } from '../../../introjs.service';

interface PlotDataUI {
  id: number;
  name: string;
  plot_type_name: string;
  file_name: string;
  fileId: number;
  filter_active: boolean;
  filter_instance_id?: number | null;
  json_data: { data: Data[]; layout: Layout };
  display_layout: GridsterItem | null;
  favorite?: boolean;
}

export type PlotDataMode = 'edit' | 'new' | 'closed';

interface PlotLayoutSaveEvent {
  graph: PlotDataUI;
  $event: { item: GridsterItem; itemComponent: GridsterItemComponentInterface };
}

class LoadMorePaginatorIntl extends MatPaginatorIntl {
  override getRangeLabel = (page: number, pageSize: number, length: number) => {
    const pageEnd = (page + 1) * pageSize;
    const end = pageEnd < length ? (page + 1) * pageSize : length;
    return `1 - ${end} of ${length}`;
  };

  override nextPageLabel = 'load more';
}

@Component({
  selector: 'app-visual-data',
  templateUrl: './visual-data.component.html',
  styleUrls: ['./visual-data.component.css'],
  providers: [{ provide: MatPaginatorIntl, useClass: LoadMorePaginatorIntl }],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class VisualDataComponent implements OnInit, AfterViewInit {
  gridsterComp = viewChild(GridsterComponent);
  showTooltip = false;
  @ViewChild('titleElement', { static: false }) titleElement!: ElementRef;

  //gridster layout exeperiment
  x = 0;
  y = 0;
  cols = 1;
  rows = 1;
  front = false;

  addToGrid(x: number, y: number, cols: number, rows: number, front: boolean) {
    const indexOfGraphCopy = Math.floor(
      Math.random() * this.graphsDataAll.length,
    );
    const display_layout = { x, y, cols, rows };
    const graphCopy = {
      ...this.graphsDataAll[indexOfGraphCopy],
      id: this.graphsDataAll[indexOfGraphCopy].id * 100,
      display_layout,
    };
    console.log('graphCopy %o, displayLayout', graphCopy, display_layout);
    this.graphsDataAll = front
      ? [graphCopy, ...this.graphsDataAll]
      : [...this.graphsDataAll, graphCopy];
    this.forcePlaceItems();
  }

  forcePlaceItems() {
    setTimeout(() => {
      const notPlacedItem = this.gridsterComp()?.grid?.find(i => i.notPlaced);
      console.log('notPlacedItem %o', notPlacedItem);
      if (notPlacedItem) {
        notPlacedItem.notPlaced = false;
        const push = new GridsterPush(notPlacedItem);
        push.pushItems(push.fromNorth);

        push.setPushedItems();
        push.destroy();
      }
    });
  }

  plotLayoutEvents$ = new Subject<PlotLayoutSaveEvent>();

  togglefavplot = true;
  g_const = g_const;
  plotPopupMode = signal<PlotDataMode>('closed');
  plotPopupOpen = computed(() => this.plotPopupMode() !== 'closed');
  plotSettingsPopup = signal<boolean>(false);
  isFullScreen = false;
  isFilterDataModalOpen = false;
  plotDescriptionModal = false;
  _unsubscribe = new Subject();
  selectedGraphId = 0;
  filter_active = false;
  filter_instance_id: number | null = null;
  fileID: number | null = null;
  totalPages = 0;
  graphsDataAll: PlotDataUI[] = [];

  selectedPlotTypeId: number | null = null;
  plotTypes: { CompleteList: PlotTypeValue[] } = { CompleteList: [] };

  selectedPlotTypeName: string | null = null;
  selectedPlotId: number | null = null;
  graphsData: PlotResponse['response'][] = [];
  plottedGraphData: PlotResponse['response'][] = [];
  dropdownGraphData: PlotResponse['response'][] = [];
  hasMore = true;
  currentpage = 0;

  constructor(
    private plotService: PlotService,
    private toastrService: ToastrService,
    @Inject(DOCUMENT) private document: Document,
    private ngZone: NgZone,
    private introJsService: IntroService,
  ) {}
  ngAfterViewInit(): void {
    setTimeout(() => {
      const element = this.titleElement.nativeElement;
      this.showTooltip = element.scrollWidth > 450;
    }, 500);
  }

  // Example data to pass to GraphWorkComponent
  isComponentLoaded = false;

  projectDetails$: Observable<ProjectDataDetails> = inject(
    ActivatedRoute,
  ).data.pipe(map(data => data['projectDetails']));

  ngOnInit(): void {
    this.introJsService.stopOnboardingProcess(true);
    this.isComponentLoaded = true;
    this.graphsDataAll = [];
    this.loadCustomPlots(this.currentpage);

    const saveEvents$ = this.plotLayoutEvents$.pipe(
      bufferTime(300),
      filter(events => events.length > 0),
      // skip(1),
      takeUntil(this._unsubscribe),
    );
    const projectId$ = this.projectDetails$.pipe(
      map(projectDetails => projectDetails.data.id),
    );

    combineLatest({ projectId: projectId$, saveEvents: saveEvents$ })
      .pipe(
        switchMap(({ projectId, saveEvents }) => {
          const payload: PlotLayoutPayload[] = saveEvents.map(
            ({ graph, $event }) => ({
              plot_id: graph.id,
              ...$event.item,
            }),
          );
          return this.plotService.savePlotLayoutData(projectId, payload);
        }),
      )
      .subscribe(res => {
        console.log(res);
      });
    this.ngZone.runOutsideAngular(() => {
      fromEvent(window, 'scroll')
        .pipe(takeUntil(this._unsubscribe))
        .subscribe(event => {
          console.log('scrolling %o', event);
        });
    });
  }

  pageSize = signal(7);
  paginator = viewChild(MatPaginator);

  changePage(event: PageEvent) {
    if (this.graphsDataAll.length <= event.pageIndex * this.pageSize()) {
      this.loadCustomPlots(event.pageIndex * this.pageSize());
    }
  }

  gridsterContainer = viewChild('container', { read: ElementRef });
  scrollBinding = effect(() => {
    const container = this.gridsterContainer()?.nativeElement;
    if (container) {
      container.addEventListener('scroll', this.onScroll.bind(this));
    }
  });

  gridsterConfig = computed<GridsterConfig>(() => ({
    gridType: GridType.VerticalFixed,
    displayGrid: DisplayGrid.OnDragAndResize,
    compactType:
      CompactType.CompactUp /* does compact makes unpredictable behavior worse? */,
    dragHandleClass: 'drag-handler',
    // disableAutoPositionOnConflict: true,
    fixedRowHeight:
      this.gridsterContainer()?.nativeElement.clientHeight / 2 - 2 * 10,
    minCols: 3,
    maxCols: 3,
    minRows: 1,
    pushItems: true,
    pushResizeItems: true,
    swapWhileDragging: true,
    swap: true,
    resizable: {
      enabled: true,
      delayStart: 0,
    },
    draggable: {
      enabled: true,
      dragHandleClass: 'drag-handler',
    },
  }));

  onScroll(event: Event): void {
    console.log('scrolling %o', event);
    const container = this.gridsterContainer()?.nativeElement;
    if (container) {
      const scrollPosition = container.scrollTop + container.clientHeight;
      const threshold = container.scrollHeight;

      if (scrollPosition >= threshold - 50) {
        if (
          this.hasMore &&
          !this.isComponentLoaded &&
          this.currentpage <= this.totalPages - 1
        ) {
          this.loadCustomPlots(++this.currentpage);
        }
      }
    }
  }

  searchFilters(searchOptions: SearchOptions) {
    console.log('searchOptions: %o', searchOptions);
    this.loadCustomPlots((this.currentpage = 0), searchOptions);
  }

  loadCustomPlots(currentpage = 0, searchoptions?: SearchOptions): void {
    console.log('load plots');
    const project_id = Number(localStorage.getItem('project_id'));
    let payload = `cursor=${currentpage}&limit=${this.pageSize()}`;

    if (searchoptions?.title) {
      payload += `&search_text=${searchoptions.title}`;
    }

    if (searchoptions?.minDate) {
      payload += `&min_selection_date=${searchoptions.minDate}`;
    }

    if (searchoptions?.maxDate) {
      payload += `&max_selection_date=${searchoptions.maxDate}`;
    }

    this.plotService
      .getCustomPlots(project_id, payload)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe(
        response => {
          this.totalPages = response.data.meta_data.max_page - 1;
          this.paginator()!.length = response.data.meta_data.total;
          this.paginator()!.pageSize = this.pageSize();
          const newGraphsData = response.data.plots.map(
            (plot: UserPlot): PlotDataUI => ({
              filter_active: plot.filter_active,
              filter_instance_id: plot.filter_instance_id,
              fileId: plot.file_id,
              favorite: plot.favorite,
              id: plot.plot_id,
              name: plot.plot_name,
              plot_type_name: plot.plot_name,
              file_name: plot.file_name,
              json_data: {
                data: plot.plot?.data,
                layout: plot.plot?.layout,
              },
              display_layout: plot.display_layout as GridsterItem,
            }),
          );

          this.graphsDataAll = searchoptions
            ? [...newGraphsData]
            : [...this.graphsDataAll, ...newGraphsData];
          // Append new data to existing graphsDataAll

          this.isComponentLoaded = !this.graphsDataAll;
        },
        error => {
          this.isComponentLoaded = false;
          console.error('Error loading custom plots', error);
        },
      );
  }

  openFullscreen(index: number, graph: PlotDataUI): void {
    const elem = document.getElementById(`graph-${index}`);
    if (elem?.requestFullscreen) {
      elem.requestFullscreen().then(() => {
        this.isFullScreen = true;
        setTimeout(() => {
          graph.json_data.layout.width = window.innerWidth;
          graph.json_data.layout.height = window.innerHeight - 2 * 30;
        }, 500);
      });
    }
  }

  closeFullscreen(): void {
    if (this.document.exitFullscreen) {
      this.document.exitFullscreen();
    }
    this.isFullScreen = false;
  }

  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  onFullscreenChange() {
    if (document.fullscreenElement) {
      this.isFullScreen = true;
      console.log('fullscreen entered');
    } else {
      this.isFullScreen = false;
      console.log('fullscreen exited');
    }
  }

  loadMoreData(currentPage = 0, searchOptions?: string): void {
    const project_id = Number(localStorage.getItem('project_id'));
    let payload = `cursor=${currentPage}&limit=${this.pageSize()}`;
    if (searchOptions) {
      payload = `cursor=${currentPage}&limit=${this.pageSize()}&search_text=${searchOptions}`;
    }

    this.plotService
      .getCustomPlots(project_id, payload)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe(
        response => {
          this.totalPages = response.data.meta_data.max_page - 1;
          const newGraphsData = response.data.plots.map(
            (plot: UserPlot): PlotDataUI => ({
              file_name: plot.file_name,
              filter_active: plot.filter_active,
              filter_instance_id: plot.filter_instance_id,
              fileId: plot.file_id,
              id: plot.plot_id,
              name: plot.plot_name,
              plot_type_name: plot.plot_name,
              json_data: {
                data: plot.plot?.data,
                layout: plot.plot?.layout,
              },
              display_layout: plot.display_layout as GridsterItem,
            }),
          );

          // Append new data to existing graphsDataAll
          if (!searchOptions) {
            this.graphsDataAll = [...this.graphsDataAll, ...newGraphsData];
          }
          if (searchOptions) this.graphsDataAll = [...newGraphsData];

          if (this.graphsDataAll) {
            console.log(this.graphsDataAll);
            this.isComponentLoaded = false;
          }
        },
        error => {
          this.isComponentLoaded = false;
          console.error('Error loading custom plots', error);
        },
      );
  }

  getFavPlots(togglefavplot: boolean): void {
    console.log([togglefavplot]);
    const project_id = Number(localStorage.getItem('project_id'));
    const payload = `favorite=${togglefavplot}`;

    if (!this.togglefavplot) {
      this.graphsDataAll = [];
      this.loadCustomPlots(0);
      this.togglefavplot = !this.togglefavplot;

      return;
    }

    this.togglefavplot = !this.togglefavplot;

    this.plotService
      .getCustomPlots(project_id, payload)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe(
        (response: BackendResponse<PlotResponseAll>) => {
          const { meta_data, plots } = response.data;
          this.totalPages = (meta_data?.max_page || 1) - 1;

          const newGraphsData = plots.map(
            (plot: UserPlot): PlotDataUI => ({
              filter_active: plot.filter_active,
              filter_instance_id: plot.filter_instance_id,
              fileId: plot.file_id,
              id: plot.plot_id,
              name: plot.plot_name,
              plot_type_name: plot.plot_name,
              json_data: {
                data: plot.plot?.data,
                layout: plot.plot?.layout,
              },
              display_layout: plot.display_layout as GridsterItem,
              file_name: plot.file_name,
            }),
          );

          // Append the new data to the existing graphsDataAll
          this.graphsDataAll = [...newGraphsData];

          this.isComponentLoaded = false;
          console.log(this.graphsDataAll);
        },
        error => {
          this.isComponentLoaded = false;
          console.error('Error loading custom plots', error);
        },
      );
  }

  openDescriptionModal(plotId: number): void {
    this.plotDescriptionModal = true;
    this.selectedGraphId = plotId;
  }

  openAddForm(): void {
    this.plotPopupMode.set('new');
  }

  openPlotSettings(): void {
    this.plotSettingsPopup.set(true);
  }

  openFilterModal(
    plotId: number,
    filter_active: boolean,
    filter_instance_id: number | null,
    fileID: number,
  ): void {
    console.log(plotId);
    this.isFilterDataModalOpen = true;
    this.filter_active = filter_active;
    this.filter_instance_id = filter_instance_id;
    this.selectedGraphId = plotId;
    this.fileID = fileID;
  }

  setFavPlot(plotId: number, setPlotAsFavorite: boolean): void {
    this.plotService.favPlot(plotId, setPlotAsFavorite).subscribe({
      next: response => {
        this.toastrService.success(response.message);
      },
      error: e => {
        this.toastrService.error(e.error.message);
      },
      complete: () => {
        // JUST MAKE CHANGES IN THE LOCAL ARRAY TO AVOID RELOADING THE WHOLE DATA, FRONTEND CAN DETECH THE CHANGES.

        if (this.togglefavplot) {
          // When we are NOT in favourite mode
          this.graphsDataAll.forEach(plot => {
            if (plot.id === plotId) {
              plot.favorite = setPlotAsFavorite;
            }
          });
        } else {
          // When we are favourite mode and we remove the plot from favourite, then that plot should not be visible in the list as it is removed as favourite
          if (setPlotAsFavorite == false) {
            this.graphsDataAll = this.graphsDataAll.filter(
              plot => plot.id !== plotId,
            );
          } else {
            // REDUNDANT : As when we are in favourite mode, we are already fetching the favourite plots, there will no case when we setPlotAsFavorite == True
            this.toastrService.error('Error in setting plot as favourite');
          }
        }
      },
    });
  }

  removePlot(id: number): void {
    this.plotService.removePlot(id).subscribe(
      response => {
        this.toastrService.success(response.message);
        this.graphsDataAll = [];

        this.loadCustomPlots();
      },
      error => {
        console.error('Error fetching plot data', error);
      },
    );
  }

  openPlotEditPopup(plot: PlotDataUI, plotId: number): void {
    this.selectedPlotId = plotId;
    this.selectedPlotTypeName = plot.plot_type_name;
    this.plotPopupMode.set('edit');
  }

  setSelectedPlotType(): void {
    if (this.selectedPlotTypeId) {
      console.log('Selected Plot Type:', this.selectedPlotTypeId);
    }
  }

  saveNewPlot(): void {
    // graph: PlotResponse['response']
    this.graphsDataAll = [];
    this.loadCustomPlots(0);
    // this.plottedGraphData.push(graph);
    // this.graphsData = [...this.plottedGraphData, ...this.dropdownGraphData];
    this.plotPopupMode.set('closed');
  }

  closeFilterModal() {
    this.isFilterDataModalOpen = false;
    this.graphsDataAll = [];

    this.loadCustomPlots(0);
  }

  saveEditedPlot(graph: PlotResponse['response']): void {
    if (this.selectedPlotId !== null) {
      const index = this.graphsDataAll.findIndex(
        plot => plot.id === this.selectedPlotId,
      );
      if (index > -1) {
        this.plottedGraphData[index] = graph;
        this.graphsData = [...this.plottedGraphData, ...this.dropdownGraphData];
        this.plotPopupMode.set('closed');
      }
    }
  }

  closeNewPlot() {
    this.plotPopupMode.set('closed');
  }

  closePlotSettings() {
    this.plotSettingsPopup.set(false);
  }

  closeDesPlot() {
    this.plotDescriptionModal = false;
  }

  onItemResize(
    graph: PlotDataUI,
    $event: {
      item: GridsterItem;
      itemComponent: GridsterItemComponentInterface;
    },
  ) {
    if (!this.isFullScreen) {
      // console.log('resize gridster item', $event);
      graph.json_data.layout.width = $event.itemComponent.width;
      graph.json_data.layout.height = $event.itemComponent.height - 2 * 30;
    }
  }

  onItemChange(
    graph: PlotDataUI,
    $event: {
      item: GridsterItem;
      itemComponent: GridsterItemComponentInterface;
    },
  ) {
    this.plotLayoutEvents$.next({ graph, $event });
  }
}
