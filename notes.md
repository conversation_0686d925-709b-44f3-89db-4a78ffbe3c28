# Angular fixes

- Implement Angular Routing the right way - optimize it

# Enhancements

- Add Error Interceptor
- Add full Error Handling in Code
- switch css to scss

- option_name from BE should be keys for Languages
- Add State Management of some sort so the plots dont have to be reloaded at every change.

# Logix Fixes

- scrolling loads all graphes again an appends them to first loaded graphs
-
