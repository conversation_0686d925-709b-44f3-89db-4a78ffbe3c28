import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DataViewComponent } from './data-view.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, TOAST_CONFIG } from 'ngx-toastr';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DataviewService } from '../../services/data-view.service';

describe('DataViewComponent', () => {
  let component: DataViewComponent;
  let fixture: ComponentFixture<DataViewComponent>;

  beforeEach(async () => {
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
    };

    const routerMock = {
      navigate: jest.fn(),
    };

    const dataViewServiceMock = {
      getprojectname: jest.fn(),
      getFilterData: jest.fn(),
      PreviewFiles: jest.fn(),
      DeleteFiles: jest.fn(),
      getAllDataView: jest.fn(),
      getFilteredDataByDate: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [DataViewComponent],
      imports: [
        HttpClientTestingModule,
        ReactiveFormsModule,
        MatTableModule,
        ToastrModule.forRoot(), // Import ToastrModule
      ],
      providers: [
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: DataviewService, useValue: dataViewServiceMock },
        {
          provide: TOAST_CONFIG, // Provide a mock ToastConfig
          useValue: {
            toastComponent: null,
            iconClasses: {},
            positionClass: '',
            preventDuplicates: false,
            newestOnTop: false,
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DataViewComponent);
    component = fixture.componentInstance;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    dataViewService = TestBed.inject(DataviewService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Additional test cases go here...
});
