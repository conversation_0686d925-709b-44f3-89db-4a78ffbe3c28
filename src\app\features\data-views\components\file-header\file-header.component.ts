import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Location } from '@angular/common';

@Component({
  selector: 'app-file-header',
  templateUrl: './file-header.component.html',
  styleUrl: './file-header.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class FileHeaderComponent {
  @Output() buttonClicked = new EventEmitter<void>();
  @Output() filterDataClicked = new EventEmitter<void>();
  @Output() AddColumnClicked = new EventEmitter<void>();
  @Output() sectionChanged = new EventEmitter<string>();
  @Output() sectionTypeChanged = new EventEmitter<string>();
  @Output() sectionProcessedChanged = new EventEmitter<string>();
  @Input() filter_active = false;
  @Input() filename = '';

  isAddColumnModalOpen = false;
  isFilterDataModalOpen = false;

  constructor(
    public dialog: MatDialog,
    private location: Location,
  ) {}

  openAddColumnModal(): void {
    this.isAddColumnModalOpen = true;
    this.AddColumnClicked.emit();
  }
  goBack() {
    this.location.back();
  }
  closeAddColumnModal(): void {
    this.isAddColumnModalOpen = false;
  }
  onButtonClick(): void {
    this.buttonClicked.emit();
  }
  toggleFilterDataModal(): void {
    this.filterDataClicked.emit();
  }

  closeFilterDataModal(): void {
    this.isFilterDataModalOpen = false;
  }

  saveColumn(): void {
    this.isAddColumnModalOpen = false;
  }

  saveFilterData(): void {
    this.isFilterDataModalOpen = false;
  }

  capitalizeFirstLetter(value: string): string {
    if (!value) return '';
    return value.charAt(0).toUpperCase() + value.slice(1);
  }
  activeSection = 'data';
  changeSection(section: string) {
    this.activeSection = section;
    this.sectionChanged.emit(this.activeSection);
  }
  activeTypeSection = 'numericalColumns';
  changeTypeSection(section: string) {
    this.activeTypeSection = section;
    this.sectionTypeChanged.emit(this.activeTypeSection);
  }

  activeProcessedSection = 'original';

  changeProcessedSection(section: string) {
    this.activeProcessedSection = section;
    this.sectionProcessedChanged.emit(this.activeProcessedSection);
  }
}
