<div class="flex gap-2 flex-wrap">
  @for (color of colorPaletteColorsWithId(); track color.id) {
    @if (color.id === colorIdInEdit()?.id) {
      <app-color-tile
        [colorHexFill]="colorSettingsService.currentlySelectedColorInPicker()">
        <mat-icon>colorize</mat-icon>
      </app-color-tile>
    } @else {
      <app-color-tile
        [colorHexFill]="color.colorHex"
        (tileClicked)="
          existingColorClicked($event, color.id, color.index, color.colorHex)
        ">
      </app-color-tile>
    }
  }

  @if (colorPaletteSelected() && addingColorTileVisible()) {
    @if (colorIdInEdit()?.id === -1) {
      <app-color-tile
        [colorHexFill]="colorSettingsService.currentlySelectedColorInPicker()">
        <mat-icon>colorize</mat-icon>
      </app-color-tile>
    } @else {
      <app-color-tile
        [colorHexFill]="'#FFFFF'"
        (tileClicked)="addButtonClicked($event, -1)">
        <mat-icon>add</mat-icon>
      </app-color-tile>
    }
  }
</div>
