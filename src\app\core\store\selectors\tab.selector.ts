import { createFeatureSelector, createSelector } from '@ngrx/store';
import { TabState } from '../states/tab.state';

export const selectedTabState = createFeatureSelector<TabState>('tab');

export const selectSelectedTab = createSelector(
  selectedTabState,
  state => state.tab.selectedTab,
);

export const selectShowSidebar = createSelector(
  selectedTabState,
  state => state.tab.showSidebar,
);
