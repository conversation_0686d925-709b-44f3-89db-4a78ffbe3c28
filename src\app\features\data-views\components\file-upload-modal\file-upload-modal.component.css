.upload-file-container {
  height: 160px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #6750a41f;
  border-radius: 8px;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.upload-file-container:hover {
  border-color: #296197;
  /* background-color: #f5f5ff; */
}

ngx-file-drop {
  width: 100%;
  height: 100%;
}
::ng-deep ng-template {
  border: none !important; /* Remove the default border */
  box-shadow: none !important; /* Remove any box shadow if present */
}

mat-expansion-panel {
  border: none !important;
  box-shadow: none !important;
}
.tooltip {
  background-color: #f7fafc; /* Custom background color */
  color: #4a5568; /* Custom text color */
  border-radius: 8px; /* Rounded corners */
  padding: 8px; /* Padding */
  font-size: 0.875rem; /* Font size */
}

.button-container {
  width: 40px;
  height: 48px;
  border: 1px solid transparent;
  background-color: white;
}

.expansion-bg .file_name {
  border-radius: 8px !important;
  height: 48px;
  line-height: 10px !important;
}
.input-dele {
  border: 1px solid #6750a41f;
  background: #6750a40a;
  border-radius: 4px;
}

input,
select {
  width: 250px;
  background: #f7f9ff;
}
select {
  margin-left: 40px;
}

::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f3f9; /* Background color of the track */
  border-radius: 10px; /* Rounding the track */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background-color: #b8c4ce; /* Scrollbar thumb color */
  border-radius: 10px; /* Rounding the thumb */
  border: 3px solid #f1f3f9; /* Space between track and thumb */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #d3cccc; /* Darker color when hovered */
}

/* Properly scoped deep selectors for ngx-file-drop */
:host ::ng-deep .ngx-file-drop__drop-zone {
  border: none !important;
  border-radius: 8px !important;
  padding: 0 !important;
  min-height: auto !important;
  height: 100% !important;
  overflow: visible !important;
  background-color: transparent !important;
}

:host ::ng-deep .ngx-file-drop__content {
  display: block !important;
  height: 100% !important;
  width: 100% !important;
  overflow: visible !important;
}

:host ::ng-deep .ngx-file-drop__drop-zone--over {
  border: none !important;
  background-color: transparent !important;
}

.content-container {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  display: block;
  color: #000000;
}

mat-expansion-panel {
  border: none !important;
  box-shadow: none !important;
}

/* Custom modal styling */
.file-upload-modal {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.file-upload-modal > .flex-grow {
  min-height: 0;
}

/* Cloud upload icon styling */
.icon-container {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.upload-file-container mat-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48px;
  width: 48px;
  font-size: 48px;
  line-height: 1;
}
