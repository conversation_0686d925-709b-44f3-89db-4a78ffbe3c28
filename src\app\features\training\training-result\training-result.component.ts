import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule, MatNavList } from '@angular/material/list';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { g_const } from '../../../_utility/global_const';
import { TrainingHeaderComponent } from '../training-header/training-header.component';

@Component({
  selector: 'app-training-result',
  imports: [
    MatListModule,
    MatTabsModule,
    MatIconModule,
    MatButtonModule,
    MatTabsModule,
    MatNavList,
    TrainingHeaderComponent,
  ],
  templateUrl: './training-result.component.html',
  styleUrl: './training-result.component.css',
})
export class TrainingResultComponent {
  activeItem: number | null = null;
  openAddForm(itemIndex: number): void {
    this.activeItem = itemIndex;
  }

  activeTabIndex = 0;
  totalTabs = 4;

  onTabChange(event: MatTabChangeEvent): void {
    this.activeTabIndex = event.index;
  }

  nextStep(): void {
    if (this.activeTabIndex < this.totalTabs - 1) {
      this.activeTabIndex++;
    }
  }

  // Handle "Back" button click
  previousStep(): void {
    if (this.activeTabIndex > 0) {
      this.activeTabIndex--;
    }
  }

  isTrainingModal = false;
  isModalOpen = false;
  g_const = g_const;
  files: File[] = [];
  openModal() {
    this.isModalOpen = true;
  }
  closeModal() {
    this.isModalOpen = false;
    this.isTrainingModal = false;
  }

  trainingModal() {
    this.isTrainingModal = true;
  }
}
