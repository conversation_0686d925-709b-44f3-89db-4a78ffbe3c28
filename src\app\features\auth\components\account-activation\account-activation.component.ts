import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { g_const } from '../../../../_utility/global_const';

@Component({
  selector: 'app-account-activation',
  templateUrl: './account-activation.component.html',
  styleUrl: './account-activation.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class AccountActivationComponent implements OnInit, OnDestroy {
  uid = '';
  token = '';
  isActivationFailed = false;
  _unsubscribe = new Subject();
  email = '';
  processing = '...Activating';
  loading = true;
  g_const = g_const;

  constructor(
    private route: ActivatedRoute,
    private auth: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private toastrService: ToastrService,
  ) {}

  ngOnInit() {
    this.activateUser();
  }

  // should call the activate user api and redirected to login after account activation
  activateUser(): void {
    this.uid = this.route.snapshot.params['uid'] || this.uid;
    this.token = this.route.snapshot.params['token'] || this.token;
    this.loading = true;
    if (this.uid && this.token) {
      const queryParams = { uid: this.uid, token: this.token };
      this.auth.activateUser(queryParams).subscribe({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        next: (response: any) => {
          this.isActivationFailed = false;
          this.toastrService.success(response.message);
          this.loading = false;
          localStorage.setItem('activated', 'true');
          this.processing = 'Successful Acccount Activation';
          setTimeout(() => {
            this.router.navigate(['/auth/login']);
          }, 3000);
          this.cdr.detectChanges();
        },
        error: response => {
          this.isActivationFailed = true;
          console.log(response, 'response log');
          this.toastrService.error(
            response.error.error || response.error.message,
          );
          this.loading = false;
          this.cdr.detectChanges();
        },
      });
    }
  }

  // will resend the activation link to the user's email
  resendActivationLink(): void {
    this.loading = true;

    this.auth.resendUserActivation(this.email).subscribe({
      next: response => {
        this.toastrService.success(response.message);
        this.loading = false;
        this.cdr.detectChanges();
      },
      error: response => {
        this.toastrService.error(
          response.error.message || response.error.error,
        );
        this.loading = false;
        this.cdr.detectChanges();
      },
    });
  }

  navigateTosignUp() {
    this.router.navigate(['/auth/register']);
  }

  ngOnDestroy(): void {
    this._unsubscribe.next(false);
    this._unsubscribe.complete();
    this.isActivationFailed = false;
    this.loading = false;
  }
}
