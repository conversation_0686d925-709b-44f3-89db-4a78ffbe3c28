import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import data from '../../../../assets/Json/box.json';
import {
  Config,
  HeatmapData,
  HeatmapLayout,
  PlotData,
} from './graph-work.model';

@Component({
  selector: 'app-graph-work',
  imports: [CommonModule, PlotlyViaCDNModule],
  templateUrl: './graph-work.component.html',
  styleUrls: ['./graph-work.component.css'],
})
export class GraphWorkComponent implements OnInit {
  plotData: PlotData[] = [];
  plotLayout?: Partial<Plotly.Layout>;
  heatmapData: HeatmapData[] = [];
  heatmapLayout: HeatmapLayout | null = null;
  config: Config | null = null;

  ngOnInit(): void {
    this.getJsonData();
    this.createHeatmap();
  }

  createHeatmap() {
    // Example correlation matrix data
    const correlationMatrix = [
      [1, 0.8, 0.5],
      [0.8, 1, 0.3],
      [0.5, 0.3, 1],
    ];

    // Set up the heatmap data
    this.heatmapData = [
      {
        z: correlationMatrix,
        x: ['Feature 1', 'Feature 2', 'Feature 3'], // X-axis labels
        y: ['Feature 1', 'Feature 2', 'Feature 3'], // Y-axis labels
        type: 'heatmap',
        colorscale: 'Viridis', // Change to your preferred colorscale
        colorbar: {
          title: 'Correlation',
        },
      },
    ];

    // Set up the layout for the heatmap
    this.heatmapLayout = {
      title: 'Correlation Heatmap',
      xaxis: {
        title: 'Features',
      },
      yaxis: {
        title: 'Features',
      },
    };

    // Optional: Configuration options for the plot
    this.config = {
      displayModeBar: true,
      responsive: true,
    };
  }

  graph = {
    data: [
      {
        y: [
          'Prothrombin',
          'N_Days',
          'Albumin',
          'Platelets',
          'Age',
          'Cholesterol',
          'Bilirubin',
          'Tryglicerides',
          'Alk_Phos',
          'Copper',
          'SGOT',
        ],
        x: [0.04, 0.05, 0.06, 0.07, 0.08, 0.05, 0.06, 0.04, 0.03, 0.04, 0.02],
        orientation: 'v',
        type: 'bar',
      },
      {
        y: [
          'Prothrombin',
          'N_Days',
          'Albumin',
          'Platelets',
          'Age',
          'Cholesterol',
          'Bilirubin',
          'Tryglicerides',
          'Alk_Phos',
          'Copper',
          'SGOT',
        ],
        x: [0.02, 0.01, 0.02, 0.03, 0.01, 0.03, 0.02, 0.01, 0.01, 0.02, 0.01],
        orientation: 'v',
        type: 'bar',
      },
    ],
    layout: {
      title: 'SHAP value (impact on model output)',
      height: 300,
    },
  };

  getJsonData(): void {
    const plotDataFromJson = data.data.plot.data; // Adjust according to your JSON structure

    // Set the plot data and layout
    this.plotData = plotDataFromJson; //

    this.plotLayout = {
      height: 290,
    };
  }
}
