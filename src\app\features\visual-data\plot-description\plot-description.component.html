<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="rounded-xl shadow-lg p-4 relative w-[700px] h-[50vh] mat-dialog overflow-y-auto">
    <div
      *ngIf="loading"
      class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
      <app-loader [loading]="loading"></app-loader>
    </div>
    <div class="flex justify-between items-center object-center p-4">
      <div>
        <h3 class="font-sm">Plot</h3>
        <span>file name goes here</span>
      </div>
      <button
        mat-icon-button
        class="text-gray-400 hover:text-gray-600"
        (click)="cancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] w-full border-gray-300" *ngIf="data">
      <div class="plot-form p-4">
        <mat-card-title>Description</mat-card-title>
        <p>{{ data.description }}</p>
      </div>
      <div class="plot-form p-4">
        <mat-card-title>Interpretation</mat-card-title>
        <p>{{ data.interpretation }}</p>
      </div>
    </div>
    <div></div>
  </div>
</div>
