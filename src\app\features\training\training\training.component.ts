import { Component } from '@angular/core';
import { g_const } from '../../../_utility/global_const';
import { MatTabChangeEvent } from '@angular/material/tabs';

@Component({
  selector: 'app-training',
  templateUrl: './training.component.html',
  styleUrl: './training.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class TrainingComponent {
  projectData: unknown;
  g_const = g_const;

  // training result start
  activeItem: number | null = null;
  isPipeLine = true;
  isInference = false;
  isPerformanceEvaluation = false;
  isFinetuning = false;
  isAdvancedAnalysis = false;
  openAddForm(itemIndex: number): void {
    this.activeItem = itemIndex;
    this.isPipeLine = false;
    this.isInference = false;
    this.isPerformanceEvaluation = false;
    this.isFinetuning = false;
    this.isAdvancedAnalysis = false;
    switch (itemIndex) {
      case 1001:
        this.isPipeLine = true;
        break;
      case 2:
        this.isInference = true;
        break;
      case 3:
        this.isPerformanceEvaluation = true;
        break;
      case 4:
        this.isFinetuning = true;
        break;
      case 5:
        this.isAdvancedAnalysis = true;
    }
  }

  activeTabIndex = 0;
  totalTabs = 4;

  onTabChange(event: MatTabChangeEvent): void {
    this.activeTabIndex = event.index;
  }

  nextStep(): void {
    if (this.activeTabIndex < this.totalTabs - 1) {
      this.activeTabIndex++;
    }
  }

  // Handle "Back" button click
  previousStep(): void {
    if (this.activeTabIndex > 0) {
      this.activeTabIndex--;
    }
  }

  isTrainingModal = false;
  isModalOpen = false;
  // g_const = g_const;
  files: File[] = [];
  openModal() {
    console.log('modal:::', this.isModalOpen);
    // this.isModalOpen = !this.isModalOpen;
    this.isModalOpen = true;
  }
  closeModal() {
    this.isModalOpen = false;
    this.isTrainingModal = false;
  }

  trainingModal() {
    this.isTrainingModal = true;
  }

  openPipeLine() {
    console.log('clicked');
    console.log('data', this.isPipeLine);
    this.isPipeLine = true;
  }
  // training result end
  isFirstSectionHidden = true;
  isCustomModalOpne = false;
  customModal() {
    this.isCustomModalOpne = true;
  }
  closeCustomModal() {
    console.log('want to know', this.isCustomModalOpne);
    this.isCustomModalOpne = false;
  }
}
