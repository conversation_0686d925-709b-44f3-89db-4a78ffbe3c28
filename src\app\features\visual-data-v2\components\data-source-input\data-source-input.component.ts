import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { PlotService } from '../../services/plot.service';
import { Folder } from '../../../../_models/visual-data/plot.model';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NestedTreeControl } from '@angular/cdk/tree';
import { DataviewService } from '../../../data-views/services/data-view.service';

interface TreeNode {
  id: number;
  name: string;
  children?: TreeNode[];
  created_date?: string;
}

interface FileSelection {
  fileName: string | null;
  fileId: number | null;
}

@Component({
  selector: 'app-data-source-input',
  imports: [MatTreeModule, MatButtonModule, MatIconModule],
  templateUrl: './data-source-input.component.html',
  styleUrl: './data-source-input.component.css',
})
export class DataSourceInputComponent implements OnInit {
  @Output() selectionEmitted = new EventEmitter<FileSelection>();

  plotService = inject(PlotService);
  dataviewService = inject(DataviewService);

  folders: Folder[] = [];
  hierarchicalFileFolderStruct: TreeNode[] = [];
  hasNextList: string | null = null;
  hasPreviousList: string | null = null;
  fileselection: FileSelection = {
    fileName: null,
    fileId: null,
  };
  isFileListVisible = false;
  loading = false;

  nestedDataSource = new MatTreeNestedDataSource<TreeNode>();
  nestTreeControl = new NestedTreeControl<TreeNode>(node => node.children);

  async ngOnInit() {
    this.loadFileList();
  }

  hasNestedChild = (_: number, node: TreeNode) =>
    !!node.children && node.children.length > 0;

  loadFileList(): void {
    const project_id = Number(localStorage.getItem('project_id'));
    this.plotService.plotFileList(project_id).subscribe({
      next: response => {
        if (response.status === 'success') {
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
          this.buildHierarchy(response.data);
          this.nestedDataSource.data = this.hierarchicalFileFolderStruct;
        }
      },
      error: error => {
        console.error('Error fetching plot data', error);
      },
    });
  }

  //TO-DO : why different API to fetch the next page data???
  fetchPageData(url: string): void {
    this.plotService.getNextPageByUrl(url).subscribe({
      next: response => {
        if (response.status === 'success') {
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
          this.buildHierarchy(response.data);
          this.nestedDataSource.data = this.hierarchicalFileFolderStruct;
        } else {
          console.error('Error retrieving data: ' + response.message);
        }
      },
      error: error => {
        console.error('Error fetching data:', error);
        if (error.status === 401 && error.statusText === 'Unauthorized') {
          console.error('Token expired, please login again!');
        }
      },
    });
  }

  // Function to load the next set of files
  loadNextFiles(): void {
    if (this.hasNextList) {
      this.fetchPageData(this.hasNextList);
    }
  }

  // Function to load the previous set of files
  loadPreviousFiles(): void {
    if (this.hasPreviousList) {
      this.fetchPageData(this.hasPreviousList);
    }
  }

  buildHierarchy(apiFoldersResponse: Folder[]): void {
    this.hierarchicalFileFolderStruct = apiFoldersResponse.map(folder =>
      this.buildTreeHierarchyFromRoot(folder),
    );
    console.log(this.hierarchicalFileFolderStruct);
  }

  private buildTreeHierarchyFromRoot(folder: Folder): TreeNode {
    // Create base folder node
    const node: TreeNode = {
      id: folder.folder_id,
      name: folder.folder_name,
      children: [],
    };

    // Add subfolders recursively
    if (folder.subfolders && folder.subfolders.length > 0) {
      for (const subfolder of folder.subfolders) {
        const subfolderNode = this.buildTreeHierarchyFromRoot(subfolder);
        node.children?.push(subfolderNode);
      }
    }

    // Add files as leaf nodes
    if (folder.files && folder.files.length > 0) {
      const fileNodes = folder.files.map(file => ({
        id: file.file_id,
        name: file.file_name,
        created_date: file.created_date,
      }));
      node.children?.push(...fileNodes);
    }

    return node;
  }

  onFileSelect(node: TreeNode) {
    console.log(node);
    this.fileselection.fileName = node.name;
    this.fileselection.fileId = node.id;
    this.isFileListVisible = false;
    this.selectionEmitted.emit(this.fileselection);
  }

  toggleFileList() {
    this.isFileListVisible = !this.isFileListVisible;
  }
}
