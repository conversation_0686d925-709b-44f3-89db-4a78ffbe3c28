<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="rounded-xl shadow-lg p-4 relative w-[860px] mat-dialog overflow-y-auto">
    <div
      *ngIf="loading"
      class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
      <app-loader [loading]="loading"></app-loader>
    </div>
    <div class="flex justify-between items-center object-center p-4">
      <h6 class="font-sm font-normal">Plot Settings</h6>
      <button
        mat-icon-button
        (click)="cancel()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] w-full border-gray-300">
      <div class="plot-form p-4">
        <form [formGroup]="form" (ngSubmit)="saveSettings()">
          <div class="flex">
            <div
              class="flex flex-col transition-all duration-300"
              [style.width.%]="activeToolWidget() ? 65 : 100">
              <div>
                <label
                  for="style"
                  class="flex flex-col mb-2 font-medium text-sm"
                  >Style</label
                >
                <mat-form-field class="w-[420px]">
                  <mat-select [formControl]="form.controls.selectedPlotStyle">
                    @for (file of plotStyles(); track file.id) {
                      <mat-option [value]="file">
                        {{ file.name }}
                      </mat-option>
                    }
                  </mat-select>
                </mat-form-field>
              </div>
              @if (form.controls.selectedPlotStyle.value !== null) {
                <div>
                  <label for="style" class="mb-2 font-medium text-sm">{{
                    g_const.colors
                  }}</label>
                  <div>
                    <mat-select
                      [formControl]="form.controls.selectedColorPalette"
                      (selectionChange)="handleColorPaletteChange()">
                      <mat-select-trigger>
                        @if (currentlySelectedColorPalette(); as palette) {
                          <app-color-option
                            [editMode]="
                              activeToolWidget() === 'ColorPicker' &&
                              this.colorPaletteEdited()?.id === palette.id
                            "
                            [palette]="palette"
                            [colorPaletteSelected]="true"
                            (openColorPicker)="showColorHidePicker(palette)"
                            (saveChangedName)="
                              changePaletteName(palette.id, $event)
                            "></app-color-option>
                        }
                      </mat-select-trigger>
                      @for (palette of colorPalettes(); track palette.id) {
                        <mat-option [value]="palette">
                          <app-color-option
                            [editMode]="false"
                            [palette]="palette"></app-color-option>
                        </mat-option>
                      }
                    </mat-select>
                  </div>
                </div>
                <div class="mt-4 max-h-[200px] w-full overflow-y-auto">
                  <label
                    for="style"
                    class="flex flex-col mb-2 font-medium text-sm">
                    {{ g_const.chart_elements }}</label
                  >
                  <mat-chip-listbox>
                    <mat-chip-option
                      [selected]="chartContent()?.id === file.id"
                      (click)="showHideChart(file)"
                      class="mr-2 mb-2 mt-2 ml-2 text-cyan-50"
                      *ngFor="let file of availablePlotStyleOptions()"
                      >{{ file?.name }}
                    </mat-chip-option>
                  </mat-chip-listbox>
                </div>
              }
            </div>
            @if (activeToolWidget() !== null) {
              <app-tool-widget
                class="flex-shrink-0 flex-grow-0 w-[35%] transition-all duration-300">
                @if (activeToolWidget() === 'ChartContent') {
                  @if (chartContent(); as chartContent) {
                    <app-chart-content-toolbar
                      [chartContent]="chartContent"
                      (chartContentChanged)="
                        handleChartContentChange(chartContent, $event)
                      "></app-chart-content-toolbar>
                  }
                } @else if (activeToolWidget() === 'ColorPicker') {
                  <app-color-picker-toolbar
                    [color]="
                      colorSettingsService.currentlySelectedColorInPicker()
                    "
                    (colorChange)="adaptCurrentSelectedColorInPicker($event)"
                    (cancel)="showColorHidePicker(null)"
                    (addColor)="updateColorPalette($event)">
                  </app-color-picker-toolbar>
                }
              </app-tool-widget>
            }
          </div>
          <div class="mt-6 flex justify-start space-x-4 static">
            <button mat-flat-button class="bg-btn-color" type="submit">
              {{ g_const.save_changes }}
            </button>
            <button mat-button class="default-filter" (click)="cancel()">
              <mat-icon>restart_alt</mat-icon>
              {{ g_const.discard_changes }}
            </button>
          </div>
        </form>
      </div>
    </div>
    <div></div>
  </div>
</div>
