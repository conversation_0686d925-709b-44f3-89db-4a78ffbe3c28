openapi: 3.0.3
info:
  title: AICU`s API
  version: 1.0.0
  description: API for data analysis in medical research.
paths:
  /appoptions/DropDown/advancedML/:
    get:
      operationId: appoptions_DropDown_advancedML_list
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MachineLearningModel'
          description: ''
  /appoptions/DropDown/advancedML/{id}/:
    get:
      operationId: appoptions_DropDown_advancedML_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineLearningModel'
          description: ''
  /appoptions/DropDown/advancedOptions/:
    get:
      operationId: appoptions_DropDown_advancedOptions_list
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Option'
          description: ''
  /appoptions/DropDown/advancedOptions/{id}/:
    get:
      operationId: appoptions_DropDown_advancedOptions_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Option'
          description: ''
  /appoptions/DropDown/datacleaning/:
    get:
      operationId: appoptions_DropDown_datacleaning_list
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DataCleaningAndPreparation'
          description: ''
  /appoptions/DropDown/datacleaning/{id}/:
    get:
      operationId: appoptions_DropDown_datacleaning_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataCleaningAndPreparation'
          description: ''
  /appoptions/DropDown/datatypes/:
    get:
      operationId: appoptions_DropDown_datatypes_list
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DataType'
          description: ''
  /appoptions/DropDown/datatypes/{id}/:
    get:
      operationId: appoptions_DropDown_datatypes_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataType'
          description: ''
  /appoptions/DropDown/datawrangling/:
    get:
      operationId: appoptions_DropDown_datawrangling_list
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DataWrangling'
          description: ''
  /appoptions/DropDown/datawrangling/{id}/:
    get:
      operationId: appoptions_DropDown_datawrangling_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - appoptions
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataWrangling'
          description: ''
  /dataversion/create-dataset-version/{project_id}/:
    post:
      operationId: dataversion_create_dataset_version_create
      parameters:
        - in: path
          name: project_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataversion/dataset-version/{dataset_version_id}/:
    get:
      operationId: dataversion_dataset_version_retrieve
      parameters:
        - in: path
          name: dataset_version_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: dataversion_dataset_version_update
      parameters:
        - in: path
          name: dataset_version_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: dataversion_dataset_version_destroy
      parameters:
        - in: path
          name: dataset_version_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /dataversion/dataset-version/project/{project_id}/:
    get:
      operationId: dataversion_dataset_version_project_retrieve
      parameters:
        - in: path
          name: project_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataversion/delete-pipeline/{pipeline_id}/:
    delete:
      operationId: dataversion_delete_pipeline_destroy
      parameters:
        - in: path
          name: pipeline_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /dataversion/pipeline-step/{id}/:
    get:
      operationId: dataversion_pipeline_step_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineStep'
          description: ''
  /dataversion/pipeline-steps/:
    get:
      operationId: dataversion_pipeline_steps_list
      tags:
        - dataversion
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PipelineStep'
          description: ''
  /dataversion/update-pipeline/{id}/:
    post:
      operationId: dataversion_update_pipeline_create
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: dataversion_update_pipeline_update
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: dataversion_update_pipeline_partial_update
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataversion/user-pipeline/{pipeline_id}/:
    get:
      operationId: dataversion_user_pipeline_retrieve
      parameters:
        - in: path
          name: pipeline_id
          schema:
            type: integer
          required: true
      tags:
        - dataversion
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/column-ID/{file_id}/:
    get:
      operationId: dataview_column_ID_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/column-choices/{file_id}/:
    get:
      operationId: dataview_column_choices_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/column-values/{file_id}/:
    get:
      operationId: dataview_column_values_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/custom/processing/{file_id}/:
    post:
      operationId: dataview_custom_processing_create
      description: Mathematical operations on columns and filtering (Needs to be tested)
      parameters:
        - in: query
          name: column_threshold
          schema:
            type: number
            format: double
          description: Column Threshold.
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Current Page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Custom Page Size. Default is 15.
        - in: query
          name: remove_duplicates
          schema:
            type: boolean
          description: Remove Duplicates.
        - in: query
          name: row_threshold
          schema:
            type: number
            format: double
          description: Row Threshold.
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/custom/processing/delete-cols/{file_id}/:
    post:
      operationId: dataview_custom_processing_delete_cols_create
      description: Mathematical operations on columns and filtering (Needs to be tested)
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Current Page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Custom Page Size. Default is 15.
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/custom/processing/list-nan/{file_id}/:
    get:
      operationId: dataview_custom_processing_list_nan_retrieve
      description: Get columns list with datatypes.
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/data/statistics/{file_id}/:
    post:
      operationId: dataview_data_statistics_create
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: processed
          schema:
            type: boolean
          description: Processed Data
        - in: query
          name: type
          schema:
            type: string
          description: "Type of statistics to retrieve: 'numeric' or 'non-numeric'"
          required: true
          explode: true
          style: form
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/delete-cols/{file_id}/:
    delete:
      operationId: dataview_delete_cols_destroy
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /dataview/display/file/{file_id}/:
    get:
      operationId: dataview_display_file_retrieve
      description: Display Original Data with pagination.
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Current Page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Custom Page Size. Default is 15.
        - in: query
          name: preview
          schema:
            type: boolean
          description: Preview Data i.e. a small subset of the data
        - in: query
          name: processed
          schema:
            type: boolean
          description: Processed Data
        - in: query
          name: random
          schema:
            type: boolean
          description: Randomize the data
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/download/data/statistics/{file_id}/:
    get:
      operationId: dataview_download_data_statistics_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: type
          schema:
            type: string
          description: "Type of statistics to retrieve: 'numeric' or 'non-numeric'"
          required: true
          explode: true
          style: form
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/download/data/statistics/image-plot/{file_id}/:
    get:
      operationId: dataview_download_data_statistics_image_plot_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: plot_name
          schema:
            type: string
          description: Name of the plot to download
          required: true
        - in: query
          name: show_fig
          schema:
            type: boolean
          description: Show figures for developer
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/download/file/{file_id}/:
    get:
      operationId: dataview_download_file_retrieve
      description: Download Processed or Original Data.
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: processed
          schema:
            type: boolean
          description: Processed Data
        - in: query
          name: random
          schema:
            type: boolean
          description: Randomize the data
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/downloadImageStatsistics/{file_id}/:
    get:
      operationId: dataview_downloadImageStatsistics_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: plot_names
          schema:
            type: string
          description:
            Comma-separated list of plot names to download (e.g. "Histogram,Sobel
            Edge Detection")
        - in: query
          name: show_fig
          schema:
            type: boolean
          description: Show figures for developer
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/file/{file_id}/rename/:
    put:
      operationId: dataview_file_rename_update
      description: Rename a file
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: file_id
          schema:
            type: integer
          description: ID of the file to rename
        - in: query
          name: new_name
          schema:
            type: string
          description: New name for the file
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/filter/{filter_id}/:
    delete:
      operationId: dataview_filter_destroy
      description: Delete filter based on filter ID.
      parameters:
        - in: path
          name: filter_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /dataview/filter/data/{file_id}/:
    post:
      operationId: dataview_filter_data_create
      description: Filter processed Data.
      parameters:
        - in: query
          name: debug
          schema:
            type: boolean
          description: If you need to debug this function.
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Current Page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Custom Page Size. Default is 15.
        - in: query
          name: processed
          schema:
            type: boolean
          description: Processed Data
        - in: query
          name: sort_column
          schema:
            type: string
          description: Column to sort by
        - in: query
          name: sort_order
          schema:
            type: string
          description: 'Sort order: asc for ascending, desc for descending'
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/filter/file/{filter_id}/:
    get:
      operationId: dataview_filter_file_retrieve
      description: Retrieve filter options for a given filter ID.
      parameters:
        - in: path
          name: filter_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/image/statistics/{file_id}/:
    post:
      operationId: dataview_image_statistics_create
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Pagination cursor i.e. page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Pagination limit i.e. page_size
        - in: query
          name: show_fig
          schema:
            type: boolean
          description: Show figures for developer
      tags:
        - dataview
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: {}
          application/x-www-form-urlencoded:
            schema:
              type: object
              additionalProperties: {}
          multipart/form-data:
            schema:
              type: object
              additionalProperties: {}
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/math-advanced/{file_id}/:
    post:
      operationId: dataview_math_advanced_create
      description: Math with Columns API
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: page
          schema:
            type: integer
          description: Current Page
        - in: query
          name: page_size
          schema:
            type: integer
          description: Custom Page Size. Default is 15.
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/processed/column-ID/{file_id}/:
    get:
      operationId: dataview_processed_column_ID_retrieve
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/set-column-ID/{file_id}/:
    post:
      operationId: dataview_set_column_ID_create
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /dataview/set-file-metdadata/{file_id}/:
    post:
      operationId: dataview_set_file_metdadata_create
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - dataview
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/ColumnInfo/{file_id}/:
    get:
      operationId: files_ColumnInfo_retrieve
      parameters:
        - in: query
          name: exclude_id
          schema:
            type: boolean
          description: Flag to exclude the ID column from the results
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: files_ColumnInfo_update
      description: Edit column information associated with a CSV file.
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: update_all
          schema:
            type: boolean
          description:
            Flag to update all and then delete existing or update only given
            and keep rest
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/:
    get:
      operationId: files_Files_retrieve
      description: List all S3 files for the authenticated user.
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/{file_id}/:
    delete:
      operationId: files_Files_destroy
      description: Delete a specific file by project ID and file ID.
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
        - in: query
          name: file_id
          schema:
            type: integer
          description: File ID to be deleted
          required: true
          explode: true
          style: form
        - in: query
          name: project_id
          schema:
            type: integer
          description: Project ID to which file belongs
          required: true
          explode: true
          style: form
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /files/Files/folder/{id}/:
    get:
      operationId: files_Files_folder_list
      description: List S3 files by project and folder for the authenticated user.
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          description: Project ID to get files for
          required: true
        - in: path
          name: id_folder
          schema:
            type: integer
          description: Folder ID to get files for
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/S3File'
          description: ''
        '400':
          description: Invalid input
        '404':
          description: File not found
  /files/Files/process/{file_id}/:
    post:
      operationId: files_Files_process_create
      parameters:
        - in: path
          name: file_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/process/batch/:
    post:
      operationId: files_Files_process_batch_create
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/project/{id}/:
    get:
      operationId: files_Files_project_list
      description: List S3 files by project for the authenticated user.
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          description: Project ID to get files for
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/S3File'
          description: ''
        '400':
          description: Invalid input
        '404':
          description: File not found
  /files/Files/upload-folder/{folder_id}/:
    post:
      operationId: files_Files_upload_folder_create
      description: |-
        1. Accepts a zip file containing the folder and files
        2. Unzips the file in a temporary directory
        3. Uploads the individual files (checks type of file) to S3.
        4. Creates entries of the folder and files in the DB.
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/upload-folder/{folder_id}/status/:
    post:
      operationId: files_Files_upload_folder_status_create
      description: |-
        Checks the status of the uploaded folder to S3 by comparing the files and subfolders
        in the folder structure to those in the S3 bucket.

        Args:
        - folder_structure (dict): A JSON structure with folder_id, files, and subfolders.

        Returns:
        - Response object with the status of the upload and any missing files or folders.
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/upload-status/{folder_id}/:
    post:
      operationId: files_Files_upload_status_create
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      requestBody:
        content:
          application/json:
            schema:
              schema:
                type: object
                properties:
                  filename:
                    type: string
                required:
                  - filename
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Files/upload-url/{folder_id}/:
    post:
      operationId: files_Files_upload_url_create
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      requestBody:
        content:
          application/json:
            schema:
              schema:
                type: object
                properties:
                  filename:
                    type: string
                required:
                  - filename
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/Folders/{folder_id}/:
    get:
      operationId: files_Folders_retrieve
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: files_Folders_create
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: files_Folders_update
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: files_Folders_destroy
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /files/Folders/hierarchy/{folder_id}/:
    get:
      operationId: files_Folders_hierarchy_retrieve
      description: |-
        On the basis of user, we just need to give the folder id and relevant files and folders under that are displayed
        Pagination is applied to folders as well as files
        Args:
            request:
            folder_id: id of the folder to see the contents of

        Returns: list of files and folders
      parameters:
        - in: path
          name: folder_id
          schema:
            type: integer
          required: true
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /files/StoreAPIRequestInfo/:
    post:
      operationId: files_StoreAPIRequestInfo_create
      description: |-
        This API allows you to store information about an API request, including the
        project ID, file ID, target column, ID column, and a list of columns passed
        as features. The information is stored in the database for further processing.
      parameters:
        - in: query
          name: columns_passed
          schema:
            type: array
            items:
              type: str
          description: Columns that should be added as features.
          required: true
          explode: true
          style: form
        - in: query
          name: file_id
          schema:
            type: integer
          description: file_id
        - in: query
          name: project_id
          schema:
            type: integer
          description: project_id
        - in: query
          name: target_column
          schema:
            type: string
          description: target_column
      tags:
        - files
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /get_api_endpoints/:
    get:
      operationId: get_api_endpoints_retrieve
      description: |-
        Unified API to generate various plots including Summary of Target Variable, Pair Plots,
        and Linear Correlation Plots.
      tags:
        - get_api_endpoints
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /projects/Projects/:
    get:
      operationId: projects_Projects_retrieve
      parameters:
        - in: query
          name: description_contains
          schema:
            type: string
          description: description_contains
        - in: query
          name: list_files
          schema:
            type: boolean
          description: Set to true to list projects with S3 objects
        - in: query
          name: max_created_at
          schema:
            type: string
          description: max_created_at
        - in: query
          name: min_created_at
          schema:
            type: string
          description: min_created_at
        - in: query
          name: search_projects
          schema:
            type: boolean
          description: Set to true to search and filter projects
        - in: query
          name: title_contains
          schema:
            type: string
          description: title_contains
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
          description: ''
    post:
      operationId: projects_Projects_create
      tags:
        - projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
          description: ''
  /projects/Projects/{id}/:
    get:
      operationId: projects_Projects_retrieve_2
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
        - in: query
          name: list_files
          schema:
            type: boolean
          description: Set to true to list projects with S3 objects
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: projects_Projects_update
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: projects_Projects_destroy
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /projects/Projects/{id}/Files/filter/:
    get:
      operationId: projects_Projects_Files_filter_retrieve
      parameters:
        - in: query
          name: description_contains
          schema:
            type: string
          description: description_contains
        - in: path
          name: id
          schema:
            type: integer
          required: true
        - in: query
          name: list_files
          schema:
            type: boolean
          description: Set to true to list projects with S3 objects
        - in: query
          name: max_created_at
          schema:
            type: string
          description: max_created_at
        - in: query
          name: min_created_at
          schema:
            type: string
          description: min_created_at
        - in: query
          name: search_files
          schema:
            type: boolean
          description: Set to true to search and filter projects
        - in: query
          name: title_contains
          schema:
            type: string
          description: title_contains
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/S3File'
          description: ''
  /projects/Projects/{id}/Hypothesis/:
    get:
      operationId: projects_Projects_Hypothesis_retrieve_2
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: projects_Projects_Hypothesis_create
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /projects/Projects/Hypothesis/{id}/:
    get:
      operationId: projects_Projects_Hypothesis_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: projects_Projects_Hypothesis_update
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: projects_Projects_Hypothesis_destroy
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      tags:
        - projects
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /training-results/:
    get:
      operationId: training_results_retrieve
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/:
    get:
      operationId: training_results_advanced_plots_retrieve
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/decision/:
    get:
      operationId: training_results_advanced_plots_decision_retrieve
      description: |-
        SHAP decision plots show how complex models arrive at their predictions (i.e., how models make decisions).
        plotting one decision plot per class

        Args:
            request:

        Returns:
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/degree-of-importance/:
    get:
      operationId: training_results_advanced_plots_degree_of_importance_retrieve
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/dependence/:
    get:
      operationId: training_results_advanced_plots_dependence_retrieve
      description: |-
        Create a SHAP dependence plot, colored by an interaction feature. Plots the value of the feature on the x-axis
        and the SHAP value of the same feature on the y-axis. This shows how the model depends on the given feature,
        and is like a richer extenstion of the classical parital dependence plots. Vertical dispersion of the data points
        represents interaction effects. Grey ticks along the y-axis are data points where the feature’s value was NaN

        Args:
            request:

        Returns: base64 image encoding along with graph interpretation
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/dependence/classes/:
    get:
      operationId: training_results_advanced_plots_dependence_classes_retrieve
      description: |-
        API to return the file classes and the data_type. Also need to return the numerical type of columns for the
        dependence plot
        Args:
            request:

        Returns: classes or categories of the dataset, their datatype and the list of numericla columns
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/force/:
    get:
      operationId: training_results_advanced_plots_force_retrieve
      description: |-
        Visualize the given SHAP values with an additive force layout. plotting one decision plot per class
        Args:
            request: takes in the training id param along with the id, target and feature columns

        Returns: base64 encoding of the plot in a string format, along with the type of the plo, interpretation and
        total number of pages
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/advanced-plots/important-features/:
    get:
      operationId: training_results_advanced_plots_important_features_retrieve
      description: |-
        Quite a lot errors while saving Shap summmary plots.
        1. save the plot to s3 and then give s3 url (Issue: if user asks for the plot again, the old key cannot be used,
        either use timestamp'
        2. use image_base64 content (not sure if angular can render this on the webpage) Does not need any saving of the graphs
        https://stackoverflow.com/questions/48286094/serving-matplotlib-graphs-with-django-without-saving
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/dataset/:
    get:
      operationId: training_results_dataset_retrieve
      description: |-
        1. Check User
        2. Get session variables
        3. Get all the files associated with a project
        4. For each file get the list of columns that file has
        5. Add pagination to get 3 such file and column information first.
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /training-results/preprocessing/:
    post:
      operationId: training_results_preprocessing_create
      tags:
        - training-results
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /users/activate/{uid}/{token}/:
    get:
      operationId: users_activate_retrieve
      parameters:
        - in: path
          name: token
          schema:
            type: string
          required: true
        - in: path
          name: uid
          schema:
            type: string
          required: true
      tags:
        - users
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          description: No response body
  /users/change-password/:
    post:
      operationId: users_change_password_create
      tags:
        - users
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          description: No response body
  /users/delete/:
    delete:
      operationId: users_delete_destroy
      tags:
        - users
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /users/forgot-password/:
    post:
      operationId: users_forgot_password_create
      tags:
        - users
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          description: No response body
  /users/login/:
    post:
      operationId: users_login_create
      tags:
        - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          description: No response body
  /users/logout/:
    post:
      operationId: users_logout_create
      tags:
        - users
      security:
        - jwtAuth: []
      responses:
        '200':
          description: No response body
  /users/register/:
    post:
      operationId: users_register_create
      tags:
        - users
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          description: No response body
  /users/reset-password/:
    post:
      operationId: users_reset_password_create
      tags:
        - users
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                password_reset_token:
                  type: string
                  description: The token sent to reset the password
                new_password:
                  type: string
                  description: The new password for the user
                confirm_new_password:
                  type: string
                  description: The new password confirmation
              required:
                - password_reset_token
                - new_password
                - confirm_new_password
            examples:
              PasswordResetRequest:
                value:
                  password_reset_token: example_token_123
                  new_password: new_secure_password
                  confirm_new_password: new_secure_password
                summary: Password Reset Request
      security:
        - jwtAuth: []
        - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                description: Password reset successfully
                content:
                  application/json:
                    example:
                      message: Password reset successfully
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: Bad request
                content:
                  application/json:
                    example:
                      error: Some error message describing the issue
          description: ''
  /visualization/color-palette/:
    get:
      operationId: visualization_color_palette_list
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ColorPalette'
          description: ''
    post:
      operationId: visualization_color_palette_create
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColorPalette'
          description: ''
  /visualization/color-palette/{id}/:
    get:
      operationId: visualization_color_palette_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColorPalette'
          description: ''
    put:
      operationId: visualization_color_palette_update
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ColorPaletteRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColorPalette'
          description: ''
  /visualization/plot/:
    get:
      operationId: visualization_plot_list
      description: Fetch All Plots API retrieves a list of all plot names.
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Plot'
          description: ''
  /visualization/plot-style/:
    get:
      operationId: visualization_plot_style_list
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PlotStyle'
          description: ''
    post:
      operationId: visualization_plot_style_create
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlotStyleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PlotStyleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PlotStyleRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyle'
          description: ''
  /visualization/plot-style-option/{id}/:
    get:
      operationId: visualization_plot_style_option_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyleOption'
          description: ''
    patch:
      operationId: visualization_plot_style_option_partial_update
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleOptionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleOptionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleOptionRequest'
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyleOption'
          description: ''
  /visualization/plot-style-option/{id}/children/:
    get:
      operationId: visualization_plot_style_option_children_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyleOption'
          description: ''
  /visualization/plot-style/{id}/:
    get:
      operationId: visualization_plot_style_retrieve
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyle'
          description: ''
    patch:
      operationId: visualization_plot_style_partial_update
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPlotStyleRequest'
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlotStyle'
          description: ''
    delete:
      operationId: visualization_plot_style_destroy
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /visualization/plot/{slug}/:
    get:
      operationId: visualization_plot_retrieve
      description: Fetch All Plots API retrieves a list of all plot names.
      parameters:
        - in: path
          name: slug
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plot'
          description: ''
  /visualization/user-plot/:
    get:
      operationId: visualization_user_plot_list
      description:
        Handles retrieval of user's old plots with optional filters and
        pagination.
      parameters:
        - in: query
          name: cursor
          schema:
            type: integer
          description: Pagination cursor
        - in: query
          name: limit
          schema:
            type: integer
          description: Pagination limit
        - in: query
          name: max_selection_date
          schema:
            type: string
            format: date
          description: Filter plots up to this date (inclusive)
        - in: query
          name: min_selection_date
          schema:
            type: string
            format: date
          description: Filter plots from this date (inclusive)
        - in: query
          name: project_id
          schema:
            type: integer
          description: Project ID
        - in: query
          name: search_text
          schema:
            type: string
          description: Search text in description or interpretation
        - in: query
          name: show_fig
          schema:
            type: boolean
          description: Show figure for developer
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserPlot'
          description: ''
    post:
      operationId: visualization_user_plot_create
      description: Handles creation of a new User Plot.
      parameters:
        - in: query
          name: file_id
          schema:
            type: integer
          description: File ID
        - in: query
          name: show_fig
          schema:
            type: boolean
          description: Show figure for developer
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
  /visualization/user-plot/{id}/:
    get:
      operationId: visualization_user_plot_retrieve
      description: Retrieve UserPlot Info API
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
    put:
      operationId: visualization_user_plot_update
      description: Update UserPlot API
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
    patch:
      operationId: visualization_user_plot_partial_update
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
    delete:
      operationId: visualization_user_plot_destroy
      description: Delete UserPlot API
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      security:
        - jwtAuth: []
      responses:
        '204':
          description: No response body
  /visualization/user-plot/{id}/associate-filters/:
    post:
      operationId: visualization_user_plot_associate_filters_create
      description: |-
        Associates filters with a specific plot.
        Args:
            request: The request object containing filter data.
            plot_id: The ID of the plot to associate the filters with.

        Payload:
            {
              "filters": [
                {
                  "filter_column": "Status",
                  "filter_operator": "eq",
                  "filter_value": "D",
                  "logic": "AND"
                },
                ...
              ]
            }
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
  /visualization/user-plot/{id}/description/:
    post:
      operationId: visualization_user_plot_description_create
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserPlotRequest'
        required: true
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
  /visualization/user-plot/display-layout/:
    patch:
      operationId: visualization_user_plot_display_layout_partial_update
      tags:
        - visualization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserPlotRequest'
      security:
        - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPlot'
          description: ''
components:
  schemas:
    Aggregation:
      type: object
      properties:
        aggregation_type:
          type: string
          maxLength: 255
        aggregation_description:
          type: string
      required:
        - aggregation_description
        - aggregation_type
    BlankEnum:
      enum:
        - ''
    ColorPalette:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          description: Name of the color palette (e.g., 'Custom', 'Viridis')
          maxLength: 100
        colors:
          description: List of colors in the palette (stored as a JSON list)
      required:
        - id
        - name
    ColorPaletteRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          description: Name of the color palette (e.g., 'Custom', 'Viridis')
          maxLength: 100
        colors:
          description: List of colors in the palette (stored as a JSON list)
      required:
        - name
    DataCleaningAndPreparation:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        options:
          nullable: true
        description:
          type: string
        applicable_data_types: {}
        benefits: {}
        limitations: {}
        example_code: {}
      required:
        - applicable_data_types
        - benefits
        - description
        - example_code
        - id
        - limitations
        - name
    DataType:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        description:
          type: string
          nullable: true
        options:
          nullable: true
      required:
        - id
        - name
    DataTypeEnum:
      enum:
        - int
        - float
        - string
        - bool
        - int or null
        - float or string
      type: string
      description: |-
        * `int` - Integer
        * `float` - Float
        * `string` - String
        * `bool` - Boolean
        * `int or null` - Integer or Null
        * `float or string` - Float or String
    DataWrangling:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        description:
          type: string
        applicable_data_types: {}
        benefits: {}
        limitations: {}
        example_code: {}
      required:
        - applicable_data_types
        - benefits
        - description
        - example_code
        - id
        - limitations
        - name
    DatasetTypeEnum:
      enum:
        - image
        - table
      type: string
      description: |-
        * `image` - Image
        * `table` - Table
    FileTypeEnum:
      enum:
        - image
        - csv
        - parquet
        - tsv
        - xml
        - txt
        - yaml
        - yml
      type: string
      description: |-
        * `image` - Image
        * `csv` - CSV
        * `parquet` - Parquet
        * `tsv` - TSV
        * `xml` - XML
        * `txt` - TSV
        * `yaml` - YAML
        * `yml` - YAML
    Hypothesis:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        project:
          type: integer
          readOnly: true
        hypothesis_type:
          type: string
          maxLength: 100
        hypothesis:
          type: string
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
        - created_at
        - hypothesis
        - hypothesis_type
        - id
        - project
        - updated_at
    HypothesisRequest:
      type: object
      properties:
        hypothesis_type:
          type: string
          minLength: 1
          maxLength: 100
        hypothesis:
          type: string
          minLength: 1
      required:
        - hypothesis
        - hypothesis_type
    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 100
        password:
          type: string
          writeOnly: true
          minLength: 1
          maxLength: 100
        keep_me_logged_in:
          type: boolean
          writeOnly: true
          default: false
      required:
        - email
        - password
    MachineLearningModel:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        options:
          type: array
          items:
            $ref: '#/components/schemas/Option'
          readOnly: true
        name:
          type: string
          maxLength: 100
        machine_learning_tasks:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MachineLearningTasksEnum'
            - $ref: '#/components/schemas/BlankEnum'
            - $ref: '#/components/schemas/NullEnum'
        category:
          $ref: '#/components/schemas/MachineLearningModelCategoryEnum'
        description:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        suitable_for:
          type: integer
          nullable: true
      required:
        - category
        - created_at
        - id
        - name
        - options
        - updated_at
    MachineLearningModelCategoryEnum:
      enum:
        - 'Supervised Learning: Regression'
        - Optimization Algorithms
        - 'Supervised Learning: Classification'
        - Model Evaluation
        - 'Unsupervised Learning: Clustering'
        - Dimensionality Reduction
        - Association Analysis
      type: string
      description: |-
        * `Supervised Learning: Regression` - Supervised Learning: Regression
        * `Optimization Algorithms` - Optimization Algorithms
        * `Supervised Learning: Classification` - Supervised Learning: Classification
        * `Model Evaluation` - Model Evaluation
        * `Unsupervised Learning: Clustering` - Unsupervised Learning: Clustering
        * `Dimensionality Reduction` - Dimensionality Reduction
        * `Association Analysis` - Association Analysis
    MachineLearningTasksEnum:
      enum:
        - Regression
        - Classification
        - Clustering
        - Dimensionality Reduction
        - Time Series Forecasting
        - Association Analysis
      type: string
      description: |-
        * `Regression` - Regression
        * `Classification` - Classification
        * `Clustering` - Clustering
        * `Dimensionality Reduction` - Dimensionality Reduction
        * `Time Series Forecasting` - Time Series Forecasting
        * `Association Analysis` - Association Analysis
    NullEnum:
      enum:
        - null
    Option:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        value:
          type: string
          nullable: true
          maxLength: 255
        data_type:
          $ref: '#/components/schemas/DataTypeEnum'
        description:
          type: string
          nullable: true
        options: {}
        ml_model:
          type: integer
      required:
        - id
        - name
    PatchedPlotStyleOptionRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          description: The name of the option (e.g., 'Color Palette', 'Marker Size').
          maxLength: 100
        value:
          type: string
          minLength: 1
          description: The current value of the option.
          maxLength: 100
        data_type:
          type: string
          minLength: 1
        description:
          type: string
          minLength: 1
        options: {}
        parent:
          type: integer
          nullable: true
        display_type:
          type: string
          minLength: 1
    PatchedPlotStyleRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          description: Name of the plot style (e.g., 'High Impact Research')
          maxLength: 100
        color_palette:
          type: integer
          nullable: true
        style_type:
          allOf:
            - $ref: '#/components/schemas/StyleTypeEnum'
          description: |-
            Indicates if this is a global, user, or project-wide style

            * `global` - Global
            * `user` - User
            * `project` - Project
            * `local` - Local
        user:
          type: integer
          nullable: true
          description: User who owns the style
        project:
          type: integer
          nullable: true
        options:
          type: array
          items:
            $ref: '#/components/schemas/PlotStyleOptionRequest'
    PatchedUserPlotRequest:
      type: object
      properties:
        selected_options:
          type: array
          items:
            $ref: '#/components/schemas/UserOptionRequest'
        selected_settings:
          type: array
          items:
            $ref: '#/components/schemas/UserSettingRequest'
        description:
          type: string
          nullable: true
        interpretation:
          type: string
          nullable: true
        plot_style:
          type: integer
          nullable: true
          description: Plot-specific style for this plot
        plot_title:
          type: string
          nullable: true
        display_layout:
          nullable: true
    PipelineStep:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        id:
          type: integer
          readOnly: true
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        description:
          type: string
        step_type:
          $ref: '#/components/schemas/StepTypeEnum'
        dataset_type:
          $ref: '#/components/schemas/DatasetTypeEnum'
        params:
          type: array
          items:
            $ref: '#/components/schemas/PipelineStepOption'
      required:
        - dataset_type
        - description
        - id
        - name
        - order
        - params
        - step_type
    PipelineStepOption:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        id:
          type: integer
          readOnly: true
        value:
          type: string
          maxLength: 100
        data_type:
          type: string
          maxLength: 50
        description:
          type: string
        options: {}
      required:
        - data_type
        - description
        - id
        - name
        - value
    Plot:
      type: object
      properties:
        plot_name:
          type: string
          maxLength: 255
        plot_description:
          type: string
        icon:
          type: string
          maxLength: 255
        category:
          $ref: '#/components/schemas/PlotCategoryEnum'
        option_set:
          type: array
          items:
            $ref: '#/components/schemas/Option'
          readOnly: true
        setting_set:
          type: array
          items:
            $ref: '#/components/schemas/Setting'
          readOnly: true
        aggregation_set:
          type: array
          items:
            $ref: '#/components/schemas/Aggregation'
          readOnly: true
        smartbucketing_set:
          type: array
          items:
            $ref: '#/components/schemas/SmartBucketing'
          readOnly: true
        slug:
          type: string
          nullable: true
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
      required:
        - aggregation_set
        - option_set
        - plot_description
        - plot_name
        - setting_set
        - smartbucketing_set
    PlotCategoryEnum:
      enum:
        - Visualise
        - Explain
        - Standard
      type: string
      description: |-
        * `Visualise` - Visualise
        * `Explain` - Explain
        * `Standard` - Standard
    PlotStyle:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          description: Name of the plot style (e.g., 'High Impact Research')
          maxLength: 100
        color_palette:
          type: integer
          nullable: true
        style_type:
          allOf:
            - $ref: '#/components/schemas/StyleTypeEnum'
          description: |-
            Indicates if this is a global, user, or project-wide style

            * `global` - Global
            * `user` - User
            * `project` - Project
            * `local` - Local
        user:
          type: integer
          nullable: true
          description: User who owns the style
        project:
          type: integer
          nullable: true
        options:
          type: array
          items:
            $ref: '#/components/schemas/PlotStyleOption'
      required:
        - id
        - name
        - options
    PlotStyleOption:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          description: The name of the option (e.g., 'Color Palette', 'Marker Size').
          maxLength: 100
        value:
          type: string
          description: The current value of the option.
          maxLength: 100
        data_type:
          type: string
        description:
          type: string
        options: {}
        parent:
          type: integer
          nullable: true
        children:
          type: string
          readOnly: true
        display_type:
          type: string
      required:
        - children
        - id
        - name
        - value
    PlotStyleOptionRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          description: The name of the option (e.g., 'Color Palette', 'Marker Size').
          maxLength: 100
        value:
          type: string
          minLength: 1
          description: The current value of the option.
          maxLength: 100
        data_type:
          type: string
          minLength: 1
        description:
          type: string
          minLength: 1
        options: {}
        parent:
          type: integer
          nullable: true
        display_type:
          type: string
          minLength: 1
      required:
        - name
        - value
    PlotStyleRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          description: Name of the plot style (e.g., 'High Impact Research')
          maxLength: 100
        color_palette:
          type: integer
          nullable: true
        style_type:
          allOf:
            - $ref: '#/components/schemas/StyleTypeEnum'
          description: |-
            Indicates if this is a global, user, or project-wide style

            * `global` - Global
            * `user` - User
            * `project` - Project
            * `local` - Local
        user:
          type: integer
          nullable: true
          description: User who owns the style
        project:
          type: integer
          nullable: true
        options:
          type: array
          items:
            $ref: '#/components/schemas/PlotStyleOptionRequest'
      required:
        - name
        - options
    Project:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        owner:
          type: integer
          readOnly: true
        title:
          type: string
          maxLength: 200
        description:
          type: string
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        file_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        files_size:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        plot_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        model_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        train_runs:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        folder_key:
          type: string
          maxLength: 45
        folder_id:
          type: string
          readOnly: true
        hypotheses:
          type: array
          items:
            $ref: '#/components/schemas/Hypothesis'
          readOnly: true
        field_plot_styles:
          type: integer
          nullable: true
          description: The plot styles associated with the project
      required:
        - created_at
        - description
        - folder_id
        - hypotheses
        - id
        - owner
        - title
        - updated_at
    ProjectRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 200
        description:
          type: string
          minLength: 1
        file_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        files_size:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        plot_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        model_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        train_runs:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
        folder_key:
          type: string
          maxLength: 45
        field_plot_styles:
          type: integer
          nullable: true
          description: The plot styles associated with the project
      required:
        - description
        - title
    S3File:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          readOnly: true
        project:
          type: integer
          readOnly: true
        file_key:
          type: string
          maxLength: 255
        file_name:
          type: string
          maxLength: 255
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        file_size:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        file_type:
          $ref: '#/components/schemas/FileTypeEnum'
        extension:
          type: string
          nullable: true
          maxLength: 10
        status:
          type: string
          nullable: true
          maxLength: 500
        original_file_id:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        processed_file_id:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        polymorphic_ctype:
          type: integer
          readOnly: true
          nullable: true
        folder:
          type: integer
          nullable: true
      required:
        - created_at
        - file_key
        - file_name
        - file_type
        - id
        - polymorphic_ctype
        - project
        - updated_at
        - user
    Setting:
      type: object
      properties:
        setting_name:
          type: string
          maxLength: 255
        setting_description:
          type: string
        multiple:
          type: boolean
        settings_type:
          $ref: '#/components/schemas/SettingsTypeEnum'
      required:
        - setting_description
        - setting_name
    SettingsTypeEnum:
      enum:
        - Dropdown_Settings
        - Quartile_methods
        - Switch_buttons
      type: string
      description: |-
        * `Dropdown_Settings` - Dropdown_Settings
        * `Quartile_methods` - Quartile_methods
        * `Switch_buttons` - Switch_buttons
    SmartBucketing:
      type: object
      properties:
        smart_bucketing_name:
          type: string
          maxLength: 255
        smart_bucketing_value:
          type: string
          maxLength: 100
        bucketing_type:
          type: string
          maxLength: 50
        bucketing_description:
          type: string
      required:
        - bucketing_description
        - bucketing_type
        - smart_bucketing_name
        - smart_bucketing_value
    StepTypeEnum:
      enum:
        - preprocessing
        - augmentation
      type: string
      description: |-
        * `preprocessing` - Preprocessing
        * `augmentation` - Augmentation
    StyleTypeEnum:
      enum:
        - global
        - user
        - project
        - local
      type: string
      description: |-
        * `global` - Global
        * `user` - User
        * `project` - Project
        * `local` - Local
    UserOption:
      type: object
      properties:
        option_name:
          type: string
          readOnly: true
        selected_column_name:
          type: string
          maxLength: 255
        selected_aggregation:
          type: string
          readOnly: true
        selected_smart_bucketing:
          type: string
          readOnly: true
      required:
        - option_name
        - selected_aggregation
        - selected_column_name
        - selected_smart_bucketing
    UserOptionRequest:
      type: object
      properties:
        selected_column_name:
          type: string
          minLength: 1
          maxLength: 255
      required:
        - selected_column_name
    UserPlot:
      type: object
      properties:
        plot_name:
          type: string
          readOnly: true
        file_id:
          type: string
          readOnly: true
        file_name:
          type: string
          readOnly: true
        selected_options:
          type: array
          items:
            $ref: '#/components/schemas/UserOption'
        selected_settings:
          type: array
          items:
            $ref: '#/components/schemas/UserSetting'
        selection_date:
          type: string
          format: date-time
          readOnly: true
        description:
          type: string
          nullable: true
        interpretation:
          type: string
          nullable: true
        plot_style:
          type: integer
          nullable: true
          description: Plot-specific style for this plot
        plot_title:
          type: string
          nullable: true
        display_layout:
          nullable: true
      required:
        - file_id
        - file_name
        - plot_name
        - selected_options
        - selected_settings
        - selection_date
    UserPlotRequest:
      type: object
      properties:
        selected_options:
          type: array
          items:
            $ref: '#/components/schemas/UserOptionRequest'
        selected_settings:
          type: array
          items:
            $ref: '#/components/schemas/UserSettingRequest'
        description:
          type: string
          nullable: true
        interpretation:
          type: string
          nullable: true
        plot_style:
          type: integer
          nullable: true
          description: Plot-specific style for this plot
        plot_title:
          type: string
          nullable: true
        display_layout:
          nullable: true
      required:
        - selected_options
        - selected_settings
    UserSetting:
      type: object
      properties:
        setting_name:
          type: string
          readOnly: true
        selected_setting_value:
          type: boolean
      required:
        - setting_name
    UserSettingRequest:
      type: object
      properties:
        selected_setting_value:
          type: boolean
  securitySchemes:
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
