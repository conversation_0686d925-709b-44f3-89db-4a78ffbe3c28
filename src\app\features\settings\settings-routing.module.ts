import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SettingsComponent } from './settings/settings.component';
import { StripeCheckoutResultComponent } from './stripe-checkout-result/stripe-checkout-result.component';

const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,
  },
  {
    path: 'checkout-success',
    component: StripeCheckoutResultComponent,
  },
  {
    path: 'checkout-cancel',
    component: StripeCheckoutResultComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
