import { Component, model, output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  ChromePickerComponent,
  ColorPickerControl,
} from '@iplab/ngx-color-picker';
import { g_const } from '../../../../../_utility/global_const';
import { MatIconButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-color-picker-toolbar',
  imports: [
    ReactiveFormsModule,
    ChromePickerComponent,
    MatIconModule,
    MatIconButton,
  ],
  templateUrl: './color-picker-toolbar.component.html',
  styleUrl: './color-picker-toolbar.component.css',
})
export class ColorPickerToolbarComponent {
  color = model<string>('#ffffff');
  addColor = output<string>();
  cancelColor = output<void>();
  public chromeControl = new ColorPickerControl().hideAlphaChannel();
  protected readonly g_const = g_const;
}
