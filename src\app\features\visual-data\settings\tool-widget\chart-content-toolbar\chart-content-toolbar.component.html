@if (chartContent(); as chartContent) {
  <form [formGroup]="this.form()">
    <p>{{ chartContent.name }}</p>
    <mat-tab-group>
      <mat-tab
        *ngFor="let tab of chartContent.children"
        [label]="tab.name"
        class="overflow-x-auto">
        <div class="flex items-center justify-center m-2">
          @switch (true) {
            @case (tab.children.length === 0) {
              @if (tab.data_type === 'boolean') {
                <mat-slide-toggle
                  [formControl]="$any(this.form().get(tab.name))">
                  {{ true ? 'activated' : 'deactivated' }}
                </mat-slide-toggle>
              } @else if (tab.options.length > 0) {
                <mat-form-field>
                  <mat-label>{{ tab.name }}</mat-label>
                  <mat-select [formControl]="$any(this.form().get(tab.name))">
                    @for (option of tab.options; track option) {
                      <mat-option [value]="option">
                        {{ option }}
                      </mat-option>
                    }
                  </mat-select>
                </mat-form-field>
              } @else if (tab.data_type === 'int') {
                <mat-form-field>
                  <mat-label>{{ tab.name }}</mat-label>
                  <input
                    type="number"
                    matInput
                    [formControl]="$any(this.form().get(tab.name))" />
                </mat-form-field>
              } @else if (tab.data_type === 'string') {
                <mat-form-field>
                  <mat-label>{{ tab.name }}</mat-label>
                  <input
                    type="text"
                    matInput
                    [formControl]="$any(this.form().get(tab.name))" />
                </mat-form-field>
              }
            }
            @case (tab.children.length > 0) {
              <div class="flex flex-col">
                @for (child of tab.children; track child) {
                  @if (child.data_type === 'boolean') {
                    <mat-slide-toggle
                      [formControl]="
                        $any(this.form().get(tab.name)?.get(child.name))
                      ">
                      {{ child.name }}</mat-slide-toggle
                    >
                  } @else if (child.options.length > 0) {
                    <mat-form-field>
                      <mat-label>{{ child.name }}</mat-label>
                      <mat-select
                        [formControl]="
                          $any(this.form().get(tab.name)?.get(child.name))
                        ">
                        @for (option of child.options; track option) {
                          <mat-option [value]="option">
                            {{ option }}
                          </mat-option>
                        }
                      </mat-select>
                    </mat-form-field>
                  } @else if (child.data_type === 'int') {
                    <mat-form-field>
                      <mat-label>{{ child.name }}</mat-label>
                      <input
                        type="number"
                        matInput
                        [formControl]="
                          $any(this.form().get(tab.name)?.get(child.name))
                        " />
                    </mat-form-field>
                  } @else if (child.data_type === 'string') {
                    <mat-form-field>
                      <mat-label>{{ child.name }}</mat-label>
                      <input
                        type="text"
                        matInput
                        [formControl]="
                          $any(this.form().get(tab.name)?.get(child.name))
                        " />
                    </mat-form-field>
                  } @else if (child.data_type === 'boolean') {
                    <mat-form-field>
                      <mat-label>{{ child.name }}</mat-label>
                      <mat-slide-toggle>{{ child.name }}</mat-slide-toggle>
                    </mat-form-field>
                  }
                }
              </div>
            }
          }
        </div>
      </mat-tab>
    </mat-tab-group>
  </form>
}
