.segmented-button-group {
  display: inline-flex;
  border-radius: 8px;
  background-color: white;
}

.segmented-button-group .mat-button-toggle {
  margin: 0;
  padding: 0.5rem 1rem;
  background-color: transparent;
  font-weight: 500;

  transition:
    background-color 0.3s,
    color 0.3s;
}

.segmented-button-group .mat-button-toggle-checked {
  background-color: #1d4d7a;
  color: white;
}
::ng-deep .mat-button-toggle-label-content {
  line-height: normal !important;
}
::ng-deep .mat-mdc-dialog-content {
  overflow: hidden !important;
  max-height: none !important;
}
::ng-deep .mat-mdc-dialog-surface {
  min-width: 860px;
}

::ng-deep .custom-dialog-container {
  min-width: 860px !important;
}

.segmented-button-group
  .mat-button-toggle:not(.mat-button-toggle-checked):hover {
  background-color: #e0e7ff;
}

.upgrade-header {
  ::ng-deep &.mat-mdc-dialog-title::before {
    content: none;
  }
}

::ng-deep .mat-mdc-card-header-text {
  flex: 1;
}
