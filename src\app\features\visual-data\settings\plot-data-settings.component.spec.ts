import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { PlotDataSettingsComponent } from './plot-data-settings.component';
import { PlotService } from '../../../services/plot.services';
import { DataviewService } from '../../data-views/services/data-view.service';
import { of } from 'rxjs';

describe('PlotDataSettingsComponent', () => {
  let component: PlotDataSettingsComponent;
  let fixture: ComponentFixture<PlotDataSettingsComponent>;
  let mockPlotService: jasmine.SpyObj<PlotService>;
  let mockDataService: jasmine.SpyObj<DataviewService>;

  beforeEach(async () => {
    mockPlotService = jasmine.createSpyObj('PlotService', [
      'getPlotStyles',
      'getColorPalette',
      'getPlotStyleName',
    ]);
    mockDataService = jasmine.createSpyObj('DataviewService', []);

    await TestBed.configureTestingModule({
      declarations: [PlotDataSettingsComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: PlotService, useValue: mockPlotService },
        { provide: DataviewService, useValue: mockDataService },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlotDataSettingsComponent);
    component = fixture.componentInstance;

    mockPlotService.getPlotStyles.and.mockReturnValue(
      of({
        data: {
          plot_styles: [
            {
              id: 1,
              name: 'Line',
              color_palette: 12,
              style_type: 'global',
              user: 10,
              project: 10,
              options: [],
            },
          ],
        },
      }),
    );
    mockPlotService.getColorPalette.and.mockReturnValue(
      of({
        data: {
          color_palettes: [{ id: 1, name: 'Palette1', colors: ['#000'] }],
        },
      }),
    );

    fixture.detectChanges(); // Trigger lifecycle hooks
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
