<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 min-w-[200px] min-h-[200px]">
  <div
    class="mat-dialog rounded-xl shadow-lg p-4 relative w-auto h-auto min-w-[40vw] min-h-[40vh]">
    <div class="flex justify-between items-center object-center p-4 pl-2">
      <div class="flex items-center justify-center">
        <h3 class="m-0">Filter Data</h3>
        <button mat-icon-button class="">
          <mat-icon>info_outline</mat-icon>
        </button>
      </div>

      <button
        mat-icon-button
        (click)="closeFilterDataModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300 mb-4"></div>
    <div class="space-y-4">
      <div class="flex flex-row items-center gap-4">
        <p class="font-medium text-base">Column</p>
        <p class="font-medium text-base ml-[50%]">Operator</p>
        <p class="flex font-medium text-base gap-3 ml-[20%]">Value</p>
      </div>

      <!-- Always include the first row -->
      <div class="flex flex-row items-center gap-4 mt-4">
        <mat-select
          class="outline-none opacity-50 flex-grow border px-3 py-2 shadow-sm h-12 rounded-md w-[350px]"
          [(ngModel)]="filterDataRows[0].selectedColumn"
          (selectionChange)="onColumnChange($event, filterDataRows[0])">
          <mat-option value="" disabled selected>Choose Column</mat-option>

          <mat-optgroup label="Categories">
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-optgroup>

          <mat-optgroup label="Numerical">
            <mat-option *ngFor="let number of numerical" [value]="number">
              {{ number }}
            </mat-option>
          </mat-optgroup>
        </mat-select>

        <mat-select
          [(ngModel)]="filterDataRows[0].selectedOperator"
          class="outline-none opacity-50 border px-3 py-2 shadow-sm h-12 rounded-md"
          (selectionChange)="onOperatorChange($event, filterDataRows[0])">
          <mat-option value="" disabled selected>Select Operator</mat-option>
          <mat-option
            *ngFor="let operator of filterDataRows[0].availableOperators"
            [value]="operator">
            {{ operator }}
          </mat-option>
        </mat-select>

        <ng-container
          *ngIf="
            filterDataRows[0].columnType === 'Categories';
            else numericalInput
          ">
          <div class="relative">
            <input
              type="text"
              class="outline-none opacity-50 bg-transparent flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="filterDataRows[0].selectedValue"
              (focus)="showDropdown = true"
              (blur)="hideDropdownWithDelay()"
              placeholder="Select or type a value" />

            <ul
              *ngIf="showDropdown"
              class="absolute border border-gray-300 shadow-md rounded-md z-10 mt-1 w-full max-h-40 overflow-auto stroke-btn z-10">
              @for (
                option of filterDataRows[0].availableValues;
                track option;
                let i = $index
              ) {
                <li
                  class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  [tabindex]="i"
                  (click)="selectOption(option)"
                  (keyup.enter)="selectOption(option)">
                  {{ option }}
                </li>
              }
            </ul>
          </div>
        </ng-container>

        <ng-template #numericalInput>
          <ng-container
            *ngIf="filterDataRows[0].isBetweenOperator; else singleInput">
            <input
              [placeholder]="'Min: ' + filterDataRows[0].minPlaceholder"
              class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="filterDataRows[0].minValue" />
            <input
              [placeholder]="'Max: ' + filterDataRows[0].maxPlaceholder"
              class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="filterDataRows[0].maxValue" />
          </ng-container>
          <ng-template #singleInput>
            <input
              [placeholder]="
                filterDataRows[0].selectedColumn
                  ? 'From ' +
                    filterDataRows[0].minPlaceholder +
                    ' to ' +
                    filterDataRows[0].maxPlaceholder
                  : 'Select a value'
              "
              class="outline-none opacity-50 bg-transparent flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="filterDataRows[0].singleValue" />
          </ng-template>
        </ng-template>

        <button class="w-8 flex justify-center items-center" [disabled]="true">
          <mat-icon>delete_outline</mat-icon>
        </button>
      </div>

      <!-- Additional rows can be added here -->
      <div
        *ngFor="let row of filterDataRows.slice(1); let i = index"
        class="flex flex-row items-center gap-4 mt-4">
        <mat-select
          [(ngModel)]="row.logicOperator"
          class="outline-none opacity-50 border px-3 py-2 shadow-sm h-12 rounded-md">
          <mat-option value="" disabled>Select Logic</mat-option>
          <mat-option value="AND">AND</mat-option>
          <mat-option value="OR">OR</mat-option>
        </mat-select>

        <mat-select
          [(ngModel)]="row.selectedColumn"
          class="outline-none opacity-50 flex-grow border px-3 py-2 shadow-sm w-auto h-12 rounded-md"
          (selectionChange)="onColumnChange($event, row)">
          <mat-option value="" disabled selected>Choose Column</mat-option>
          <mat-optgroup label="Categories">
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-optgroup>
          <mat-optgroup label="Numerical">
            <mat-option *ngFor="let number of numerical" [value]="number">
              {{ number }}
            </mat-option>
          </mat-optgroup>
        </mat-select>

        <mat-select
          [(ngModel)]="row.selectedOperator"
          class="outline-none opacity-50 border px-3 py-2 shadow-sm h-12 rounded-md"
          (selectionChange)="onOperatorChange($event, row)">
          <mat-option value="" disabled selected>Select Operator</mat-option>
          <mat-option
            *ngFor="let operator of row.availableOperators"
            [value]="operator">
            {{ operator }}
          </mat-option>
        </mat-select>

        <ng-container
          *ngIf="row.columnType === 'Categories'; else numericalInput">
          <mat-select
            class="outline-none opacity-50 flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
            [(ngModel)]="row.selectedValue"
            placeholder="Select a value">
            <mat-option value="" disabled>Select a value</mat-option>
            <mat-option
              *ngFor="let option of row.availableValues"
              [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </ng-container>

        <ng-template #numericalInput>
          <ng-container *ngIf="row.isBetweenOperator; else singleInput">
            <input
              [placeholder]="'Min: ' + row.minPlaceholder"
              class="outline-none opacity-50 flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="row.minValue" />
            <input
              [placeholder]="'Max: ' + row.maxPlaceholder"
              class="outline-none opacity-50 flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="row.maxValue" />
          </ng-container>
          <ng-template #singleInput>
            <input
              [placeholder]="
                row.selectedColumn
                  ? 'From ' + row.minPlaceholder + ' to ' + row.maxPlaceholder
                  : 'Select a value'
              "
              class="outline-none opacity-50 bg-transparent flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="row.singleValue" />
          </ng-template>
        </ng-template>

        <button
          class="w-8 flex justify-center items-center"
          (click)="removeRow(i + 1)">
          <mat-icon>delete_outline</mat-icon>
        </button>
      </div>

      <!-- Button to add more rows -->
      <button
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-transparent !text-txt-color"
        (click)="addRow()">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div class="flex justify-start mt-4 gap-2">
      <button
        mat-flat-button
        [disabled]="!isFormValid()"
        (click)="filterDataSave()">
        Save
      </button>
      <button
        mat-button
        class="default-filter text-txt-color"
        (click)="removeFilter()">
        <mat-icon>restart_alt</mat-icon> Remove Filter
      </button>
    </div>
  </div>
</div>
