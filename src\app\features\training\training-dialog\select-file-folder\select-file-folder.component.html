<div class="w-full mb-4">
  <div>
    <!-- Input field that shows the selected file -->
    <input
      matInput
      placeholder="Select a file"
      [value]="selectedFileName"
      (click)="toggleFileList()"
      class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md"
      readonly />

    <!-- File List (Visible on Input Click) -->
    <div class="w-full" *ngIf="isFileListVisible">
      <div class="file-select-container">
        <!-- Load previous files option -->
        <div
          class="file-select-option"
          *ngIf="hasPreviousList"
          tabindex="0"
          (click)="loadPreviousFiles()"
          (keydown)="handleKeydown($event, loadPreviousFiles)">
          Load previous files...
        </div>

        <!-- Main folders and files -->
        <div *ngFor="let folder of hierarchicalFiles" class="folder-group">
          <div class="folder-name flex items-center gap-2">
            <mat-icon fontSet="material-icons-outlined">folder</mat-icon>
            <div>{{ folder.folder_name }}</div>
          </div>

          <!-- Files in the main folder -->
          <div *ngIf="folder.files.length > 0" class="file-list">
            <div
              *ngFor="let file of folder.files"
              class="file-select-option flex items-center gap-2"
              tabindex="0"
              (click)="onFileSelect(file)"
              (keydown)="handleFileKeydown($event, file)">
              <mat-icon fontSet="material-icons-outlined">table_chart</mat-icon>
              <div>{{ file.file_name }}</div>
            </div>
          </div>

          <!-- Subfolders -->
          <div
            *ngFor="let subfolder of folder.subfolders"
            class="subfolder-group flex items-center gap-2">
            <ng-container
              *ngTemplateOutlet="
                renderSubfolder;
                context: { subfolder: subfolder }
              "></ng-container>
          </div>
        </div>

        <!-- Load more files option -->
        <div
          class="file-select-option"
          *ngIf="hasNextList"
          tabindex="0"
          (click)="loadNextFiles()"
          (keydown)="handleKeydown($event, loadNextFiles)">
          Load more files...
        </div>

        <!-- Subfolder Template -->
        <ng-template #renderSubfolder let-subfolder="subfolder">
          <div class="folder-name flex items-center gap-2">
            <mat-icon fontSet="material-icons-outlined">folder</mat-icon>
            <div>{{ subfolder.folder_name }}</div>
          </div>

          <!-- Files in the subfolder -->
          <div *ngIf="subfolder.files.length > 0" class="file-list">
            <div
              *ngFor="let file of subfolder.files"
              class="file-select-option flex items-center gap-2"
              tabindex="0"
              (click)="onFileSelect(file)"
              (keydown)="handleFileKeydown($event, file)">
              <mat-icon fontSet="material-icons-outlined">table_chart</mat-icon>
              <div>{{ file.file_name }}</div>
            </div>
          </div>

          <!-- Nested subfolders -->
          <div
            *ngFor="let nestedSubfolder of subfolder.subfolders"
            class="nested-subfolder-group">
            <ng-container
              *ngTemplateOutlet="
                renderSubfolder;
                context: { subfolder: nestedSubfolder }
              "></ng-container>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
