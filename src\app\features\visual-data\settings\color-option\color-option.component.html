<div class="p-2">
  <div class="py-2">
    @if (colorPaletteNameEdited() && colorPaletteSelected()) {
      <input
        #paletteNameInput
        matNativeControl
        (focusout)="deactivatePaletteNameInput()"
        (keydown)="saveName($event)"
        [formControl]="paletteNameControl()"
        [value]="palette().name" />
      @if (paletteNameControl().invalid) {
        <mat-error>{{ g_const.palette_name_required }}</mat-error>
      }
    } @else {
      <div
        class="inline"
        (click)="activateNameEditMode($event)"
        (keyup.enter)="activateNameEditMode($event)"
        tabindex="0">
        {{ palette().name }}
      </div>
    }
  </div>

  <app-color-palette
    (addingColor)="openColorPickerClicked()"
    [colorPaletteSelected]="colorPaletteSelected()"
    [colorPalette]="palette()">
  </app-color-palette>
</div>
