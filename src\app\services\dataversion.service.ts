import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../env/env';
import { Observable } from 'rxjs';
import {
  DatasetVersion,
  DatasetVersionResponse,
  PipelineByIdResponse,
  PipelineStep,
} from '../_models/dataset-version.model';
import { ResponseData } from '../_models/common.model';

@Injectable({
  providedIn: 'root',
})
export class DataversionService {
  constructor(private http: HttpClient) {}

  // the backend api endpoint
  private dataversionUrl = `${environment.apiUrl}`;

  /**
   * Create the Http header to set in the api request.
   * @returns Http headers
   */
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  /**
   * To fetch the dataset versions based on the project id
   * @param projectId - if of the project for which to get the dataset versions
   * @returns dataset versions
   */
  getDatasetVersion(projectId: string): Observable<DatasetVersionResponse> {
    return this.http.get<DatasetVersionResponse>(
      `${this.dataversionUrl}dataversion/dataset-version/project/${projectId}`,
      { headers: this.getHeaders() },
    );
  }

  /**
   * To create new data version for a particular project.
   * @param name - name of the new dataset version to be added.
   * @param projectId - project id for which the new dataset version need to be added.
   * @returns
   */
  addDataVersionName(
    name: string,
    projectId: string,
  ): Observable<DatasetVersionResponse> {
    return this.http.post<DatasetVersionResponse>(
      `${this.dataversionUrl}dataversion/create-dataset-version/${projectId}/`,
      { name },
      { headers: this.getHeaders() },
    );
  }

  /**
   * Delete the dataset version for the specified id.
   * @param dataVersionId - id of dataset version to be deleted.
   * @returns
   */
  deleteDataVersion(dataVersionId: number): Observable<DatasetVersionResponse> {
    const headers = this.getHeaders();
    return this.http
      .delete<DatasetVersionResponse>(
        `${environment.apiUrl}dataversion/dataset-version/${dataVersionId}/`,
        { headers },
      )
      .pipe();
  }

  /**
   * Update the dataset version details.
   * @param dataVersionId - id of dataset version to be updated.
   * @param data - details to be updated.
   * @returns
   */
  updateDataVersion(
    dataVersionId: number,
    data: { name: string },
  ): Observable<DatasetVersionResponse> {
    const headers = this.getHeaders();
    return this.http.put<DatasetVersionResponse>(
      `${environment.apiUrl}dataversion/dataset-version/${dataVersionId}/`,
      data,
      { headers },
    );
  }

  /**
   * To fetch the dataset type related details such as step and pipeline details.
   * @param dataVersionId - version id
   * @param datasetType - dataset type
   * @param stepType - step type
   * @returns
   */
  getDataVersionById(
    dataVersionId: number,
    datasetType: string,
    stepType: string,
  ): Observable<ResponseData<DatasetVersion>> {
    return this.http.get<ResponseData<DatasetVersion>>(
      `${environment.apiUrl}dataversion/dataset-version/${dataVersionId}/?dataset_type=${datasetType}&step_type=${stepType}`,
      { headers: this.getHeaders() },
    );
  }

  /**
   * Get the Pipeline steps options.
   * @param stepType - the step type ('preprocessing' or 'augmentation';)
   * @param datasetType - the dataset type (table or image)
   * @param pageSize - page size
   * @returns
   */
  getPipeLineSteps(
    stepType: string,
    datasetType: string,
    pageSize: number,
  ): Observable<ResponseData<PipelineStep[]>> {
    return this.http.get<ResponseData<PipelineStep[]>>(
      `${this.dataversionUrl}dataversion/pipeline-steps/?step_type=${stepType}&dataset_type=${datasetType}&page_size=${pageSize}`,
      { headers: this.getHeaders() },
    );
  }

  /**
   * To create new dataversion pipeline.
   * @param dataVersionId - dataset version id
   * @param data - the pipeline steps payload
   * @returns
   */
  addDataVersionPipeline(
    dataVersionId: number,
    data: object,
  ): Observable<{ pipeline_id: number; step_count: number }> {
    return this.http.post<{ pipeline_id: number; step_count: number }>(
      `${environment.apiUrl}dataversion/update-pipeline/${dataVersionId}/`,
      data,
      { headers: this.getHeaders() },
    );
  }

  /**
   * To get the pipeline data by id.
   * @param pipelineIid - the pipeline id for which to fetch the data
   * @param stepType - the step type such as preprocessing or augmentation.
   * @param type - data set type such as image or table.
   * @returns
   */
  getPipelineDataById(
    pipelineIid: number,
    stepType: string,
    type: string,
  ): Observable<ResponseData<PipelineStep>> {
    return this.http.get<ResponseData<PipelineStep>>(
      `${environment.apiUrl}dataversion/pipeline-step/${pipelineIid}/?step_type=${stepType}&dataset_type=${type}`,
      { headers: this.getHeaders() },
    );
  }

  /**
   * To update the pipepline details.
   * @param pipelineIid - the pipeline id.
   * @param data - the payload to be updated.
   * @returns
   */
  updatePipelineDataById(
    pipelineIid: number,
    data: object,
  ): Observable<{ pipeline_id: number }> {
    return this.http.put<{ pipeline_id: number }>(
      `${environment.apiUrl}dataversion/update-pipeline/${pipelineIid}/`,
      data,
      { headers: this.getHeaders() },
    );
  }

  /**
   * Get pipleline details by id.
   * @param pipelineId - the id of the pipeline to fetch the data for
   * @returns
   */
  getUserPipeLineDataById(
    pipelineId: number,
  ): Observable<ResponseData<PipelineByIdResponse>> {
    return this.http.get<ResponseData<PipelineByIdResponse>>(
      `${environment.apiUrl}dataversion/user-pipeline/${pipelineId}/`,
      { headers: this.getHeaders() },
    );
  }

  /**
   * Delete the pipeline by the specified id
   * @param pipelineId - id of the pipeline to be deleted.
   * @returns
   */
  deleteUserPipleLineSteps(
    pipelineId: number,
  ): Observable<ResponseData<PipelineStep[]>> {
    return this.http.delete<ResponseData<PipelineStep[]>>(
      `${environment.apiUrl}dataversion/delete-pipeline/${pipelineId}/`,
      { headers: this.getHeaders() },
    );
  }
}
