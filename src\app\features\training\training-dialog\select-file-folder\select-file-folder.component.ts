/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';

import { FormArray, FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { g_const } from '../../../../_utility/global_const';
import { DataviewService } from '../../../data-views/services/data-view.service';
import { MatIcon } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { TrainingService } from '../../service/training-service.service';

export interface ListItem {
  id: number | string;
  text: string | number;
  isDisabled?: boolean;
}

interface _Group {
  option_name: string;
  selected_column_name: FormControl | string;
  isMultiple: boolean;
  isRequired: boolean;
  selected_aggregation?: FormControl | string;
  isSmartBucketing?: boolean;
  columnOptions?: FormArray | string[];
  isChild?: boolean;
}

interface File {
  file_id: number;
  file_name: string;
  created_date: string;
}

interface Subfolder {
  folder_id: number;
  folder_name: string;
  subfolders: Subfolder[];
  files: File[];
}

interface Folder {
  folder_id: number;
  folder_name: string;
  subfolders: Subfolder[];
  files: File[];
}

@Component({
  selector: 'app-select-file-folder',
  imports: [MatIcon, CommonModule],
  templateUrl: './select-file-folder.component.html',
  styleUrl: './select-file-folder.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectFileFolderComponent implements OnInit, OnDestroy, OnChanges {
  g_const = g_const;
  loading = false;
  @Output() selectedFile = new EventEmitter<{ id: number; name: string }>();
  @Input()
  preselectedFile: {
    file_id: number;
    file_name: string;
  } = { file_id: 0, file_name: '' };

  _unsubscribe = new Subject();
  selectedFileID = 0;

  folders: Folder[] = [];
  selectedFileControl = new FormControl();
  allFiles: File[] = [];
  hasNextList: string | null = null;
  hasPreviousList: string | null = null;

  isFileListVisible = false;
  selectedFileName = '';
  searchTerm = '';
  searchControl = new FormControl('');

  formControlSettings = new FormControl('');

  constructor(
    private trainingService: TrainingService,
    private dataviewService: DataviewService,
    private toastrService: ToastrService,
    private cd: ChangeDetectorRef,
  ) {
    this.formControlSettings.valueChanges.subscribe(v => console.log(v));
    this.searchControl.valueChanges.subscribe(searchTerm => {
      if (searchTerm == '') {
        console.log(searchTerm);
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        this.hierarchicalFiles = this.hierarchicalFiles.map(folder => ({
          ...folder,
          files: folder.files.filter(file =>
            file.file_name.toLowerCase().includes(lowerCaseSearchTerm),
          ),
          subfolders: this.filterSubfolders(
            folder.subfolders,
            lowerCaseSearchTerm,
          ),
        }));
        this.cd.detectChanges();
      } else {
        this.filterFiles(searchTerm || '');
      }
    });
  }
  ngOnChanges(_changes: SimpleChanges): void {
    if (this.preselectedFile) {
      this.selectedFileID = this.preselectedFile.file_id;
      this.selectedFileName = this.preselectedFile.file_name;
    }
  }

  ngOnInit(): void {
    this.loading = true;
    this.loadFileList();
  }

  // Function to load the next set of files
  loadNextFiles(): void {
    if (this.hasNextList) {
      this.fetchPageData(this.hasNextList);
    }
  }

  // Function to load the previous set of files
  loadPreviousFiles(): void {
    if (this.hasPreviousList) {
      this.fetchPageData(this.hasPreviousList);
    }
  }

  // Function to fetch files by navigating between pages
  fetchPageData(url: string): void {
    this.dataviewService.getNextPageByUrl(url).subscribe(
      response => {
        if (response.status === 'success') {
          this.hierarchicalFiles = Object.values(response.data);
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
        } else {
          this.toastrService.error(
            'Error retrieving data: ' + response.message,
          );
        }
        this.cd.markForCheck();
      },
      error => {
        console.error('Error fetching data:', error);
        if (error.status === 401 && error.statusText === 'Unauthorized') {
          this.toastrService.error('Token expired, please login again!');
        }
      },
    );
  }

  toggleFileList() {
    this.isFileListVisible = !this.isFileListVisible;
    this.filterFiles(this.searchControl.value || '');
  }

  // Function to handle file selection
  onFileSelect(file: any): void {
    this.selectedFileName = file.file_name;
    this.isFileListVisible = false;

    if (file) {
      this.selectedFileID = file.file_id;
      this.selectedFile.emit({
        id: this.selectedFileID,
        name: this.selectedFileName,
      });
    }
  }

  // Function to filter files by name
  filterFiles(searchTerm: string): void {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    this.hierarchicalFiles = this.hierarchicalFiles.map(folder => ({
      ...folder,
      files: folder.files.filter(file =>
        file.file_name.toLowerCase().includes(lowerCaseSearchTerm),
      ),
      subfolders: this.filterSubfolders(folder.subfolders, lowerCaseSearchTerm),
    }));
  }

  // Function to recursively filter subfolders by name
  filterSubfolders(subfolders: any[], searchTerm: string): any[] {
    return subfolders
      .map(subfolder => ({
        ...subfolder,
        files: subfolder.files.filter((file: any) =>
          file.file_name.toLowerCase().includes(searchTerm),
        ),
        subfolders: this.filterSubfolders(subfolder.subfolders, searchTerm),
      }))
      .filter(
        subfolder =>
          subfolder.files.length > 0 || subfolder.subfolders.length > 0,
      );
  }

  // Function to reset the file list to its original state
  resetFileList(): void {
    this.loadFileList();
  }
  loadFileList(): void {
    const project_id = Number(localStorage.getItem('project_id'));
    this.trainingService.getFileList(project_id).subscribe(
      response => {
        if (response.status === 'success') {
          this.folders = response.data;
          this.hasNextList = response.pagination.next;
          this.hasPreviousList = response.pagination.previous;
          this.extractFiles();
        }
      },
      error => {
        console.error('Error fetching plot data', error);
      },
    );
  }

  hierarchicalFiles: {
    folder_name: string;
    files: File[];
    subfolders: {
      folder_name: string;
      files: File[];
      subfolders: any[];
    }[];
  }[] = [];

  extractFiles() {
    this.folders.forEach(folder => {
      this.buildHierarchy(folder);
    });
  }

  uniqueBy<T>(array: T[], key: keyof T) {
    return [...new Map(array.map(item => [item[key], item])).values()];
  }

  private buildHierarchy(folder: Folder): any {
    const folderData = {
      folder_name: folder.folder_name,
      files: this.uniqueBy(folder.files, 'file_id'),
      subfolders: [] as {
        folder_name: string;
        files: File[];
        subfolders: any[];
      }[],
    };

    if (folder.subfolders && folder.subfolders.length > 0) {
      folder.subfolders.forEach(subfolder => {
        const subfolderData = this.buildHierarchy(subfolder);
        folderData.subfolders.push(subfolderData);
      });
    }
    const existingFolder = this.hierarchicalFiles.find(
      f => f.folder_name === folderData.folder_name,
    );
    if (!existingFolder) {
      this.hierarchicalFiles = [folderData];
    } else {
      existingFolder.files = this.uniqueBy(
        [...existingFolder.files, ...folderData.files],
        'file_id',
      );
      existingFolder.subfolders.push(...folderData.subfolders);
    }
    return folderData;
  }

  extractSubfolderFiles(subfolders: Subfolder[]) {
    subfolders.forEach((subfolder: Subfolder) => {
      this.allFiles.push(...subfolder.files);
      if (subfolder.subfolders && subfolder.subfolders.length > 0) {
        this.extractSubfolderFiles(subfolder.subfolders);
      }
    });
  }

  ngOnDestroy(): void {
    this._unsubscribe.next(false);
    this._unsubscribe.complete();
  }

  handleKeydown(event: KeyboardEvent, action: () => void): void {
    if (event.key === 'Enter' || event.key === ' ') {
      action();
      event.preventDefault(); // Prevent default behavior (e.g., scrolling for Space)
    }
  }

  handleFileKeydown(event: KeyboardEvent, file: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      this.onFileSelect(file);
      event.preventDefault(); // Prevent default scrolling behavior for Space key
    }
  }
}
