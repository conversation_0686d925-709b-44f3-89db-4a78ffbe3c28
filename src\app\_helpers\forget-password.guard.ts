import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { StorageService } from '../core/services/storage.service';

export const forgetPasswordGuard: CanActivateFn = () => {
  const router = inject(Router);
  const storageService = inject(StorageService);

  const hasResetToken = storageService.getItem('hasResetToken');
  if (hasResetToken) return true;

  // If no token, redirect to login
  router.navigate(['/auth/login']);
  return false;
};
