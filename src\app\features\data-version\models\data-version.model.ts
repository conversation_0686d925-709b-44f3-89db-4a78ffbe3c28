// Represents each dataset version in the `data` array
import { PaginationInfo } from '../../../_models/visual-data/visual-data.model';

//TODO GET dataversion/dataset-version/${dataVersionId}/
export interface DatasetVersion {
  id: number;
  version_number: number;
  name: string;
  created_at: string;
  project: number;
  user_pipeline: {
    id: number;
    is_active: boolean;
    name: string;
    steps: PipelineStep[];
  }[];
}

// Full API response structure
// TODO GET dataversion/dataset-version/project/${projectId}
// TODO POST dataversion/create-dataset-version/${projectId}/
// TODO DELETE dataversion/dataset-version/${dataVersionId}/
// TODO PUT dataversion/dataset-version/${dataVersionId}/
export interface DatasetVersionResponse {
  status: string;
  message: string;
  data: DatasetVersion[];
  errors: string | null;
  pagination: PaginationInfo | null;
}

// Interface for each parameter option
export interface ParamOption {
  name: string;
  id: number;
  value: string | number | [number, number];
  data_type: string;
  description: string;
  options: (string | number | [number, number])[];
}

// Interface for each step in the pipeline
//TODO GET dataversion/pipeline-steps/
//TODO GET dataversion/pipeline-step/${pipelineIid}/
//TODO DELETE dataversion/delete-pipeline/${pipelineId}/
export interface PipelineStep {
  name: string;
  id: number;
  order: number;
  description: string;
  step_type: string;
  dataset_type: string;
  params: ParamOption[];
}

// Interface for the full API response structure
export interface PipelineStepsResponse {
  status: string;
  message: string;
  data: PipelineStep[];
  errors: string | null;
  pagination: PaginationInfo | null;
}

// Interface for pipeline particular steps
//TODO GET dataversion/user-pipeline/${pipelineId}/
export interface PipelineByIdResponse {
  id: number;
  name: string;
  dataset_size: number;
  is_active: boolean;
  preprocessing_steps: PipelineStep[];
  augmentation_steps: PipelineStep[];
}

// Interface to save/update the pipeline steps payload

export interface createUpdatePipelineStepPayload {
  pipeline_name?: string;
  is_active: boolean;
  steps: {
    id: number;
    order?: number;
    step_type?: string;
    dataset_type?: string;
    params: {
      id: number;
      value: string | number | [number, number];
    }[];
  }[];
}
