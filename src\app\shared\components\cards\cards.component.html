<mat-card class="rounded-xl bg-white w-[365px] h-52 flex flex-col p-2">
  <!-- Header Section -->
  <div
    class="flex items-center justify-between w-full h-12 border-b border-gray-300 p-2">
    <button
      mat-icon-button
      [matMenuTriggerFor]="menu"
      aria-label="Example icon-button with a menu">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu" class="w-[335px] h-28">
      <div class="h-[72px]">
        <button mat-menu-item class="h-9" (click)="openEditModal()">
          <mat-icon>data_array</mat-icon>
          <span>Edit Project</span>
        </button>
        <button mat-menu-item class="h-9" (click)="openDeleteModal()">
          <mat-icon>delete_outline</mat-icon>
          <span>Delete Project</span>
        </button>
      </div>
    </mat-menu>
    <div class="flex items-center flex-grow">
      <mat-card-title class="custom-title font-bold">{{
        title
      }}</mat-card-title>
    </div>
    <button mat-icon-button>
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>

  <!-- Content Section -->
  <mat-card-content class="flex-grow mt-4 bg-[#FAFBFF]">
    <p class="text-sm font-normal text-gray-600">
      {{ truncatedDescription }}
    </p>
  </mat-card-content>

  <!-- Actions Section -->
  <mat-card-actions class="flex justify-between p-4 text-sm text-gray-500">
    <span
      >Files <strong>{{ files }}%</strong></span
    >
    <span
      >Data Size: <strong>{{ dataSize }}</strong></span
    >
  </mat-card-actions>
</mat-card>
<app-delete-modal
  #modal
  [isModalOpen]="isDeleteModalOpen"
  [modalTitle]="'Project'">
</app-delete-modal>
