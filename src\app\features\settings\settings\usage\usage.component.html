<div>
  <div>
    <div class="flex justify-between">
      <div>
        <h4 class="font-normal text-xl">Workspace Usage</h4>
        <p class="text-customGray">Usage of resources in workspace.</p>
      </div>
      <button class="flex items-center gap-2" mat-flat-button>
        <mat-icon>play_arrow</mat-icon>
        Start Tour
      </button>
    </div>

    <div class="gap-4 w-4/5">
      @for (usage of workspaceUsageList; track usage) {
        <div class="flex justify-start items-center p-2">
          <div
            class="flex items-center border px-3 py-2 bg-[#ffffff] h-12 rounded-md">
            <input
              #sRows
              placeholder="Enter Value"
              class="outline-none bg-transparent flex-1"
              [value]="usage.title"
              readonly />
            <button mat-icon-button (click)="usage.onClick(usage.title)">
              <mat-icon>{{ usage.icon }}</mat-icon>
            </button>
          </div>
          <div class="ml-3 w-full">
            @if (loading) {
              <mat-progress-bar mode="indeterminate"></mat-progress-bar>
            } @else {
              <p>{{ usage?.total_used }}/{{ usage?.total_allowed }}</p>
              <mat-progress-bar
                mode="determinate"
                [value]="usage.percentage"></mat-progress-bar>
            }
          </div>
          <button mat-icon-button class="justify-center">
            <mat-icon>info</mat-icon>
          </button>
        </div>
      }
    </div>
  </div>
  <!-- 
  <div class="flex justify-between mt-6">
    <div>
      <h4 class="font-normal text-xl">Monthly Usage</h4>
      <p class="text-customGray">
        Next month starts in a month on 1 September 2024. Monthly usage resets
        with plan purchase.
      </p>
    </div>
  </div>
  <div class="gap-4 w-4/5">
    <div class="flex justify-start items-center p-2">
      <div
        class="flex items-center border px-3 py-2 bg-[#ffffff] h-12 rounded-md">
        <input
          placeholder="Enter Value"
          class="outline-none bg-transparent flex-1"
          value="Training Credits" />
        <button mat-icon-button><mat-icon>model_training</mat-icon></button>
      </div>
      <div class="ml-3 w-full">
        <p>1/3</p>
        <mat-progress-bar
          mode="determinate"
          [value]="1/3 * 100"></mat-progress-bar>
      </div>
      <button mat-icon-button class="justify-center">
        <mat-icon>info</mat-icon>
      </button>
    </div>
  </div> -->
</div>
