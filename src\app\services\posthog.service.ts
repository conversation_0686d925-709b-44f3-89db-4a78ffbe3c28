import { Injectable } from '@angular/core';
import posthog from 'posthog-js';
import { environment } from '../env/env';

@Injectable({
  providedIn: 'root',
})
export class PosthogService {
  constructor() {
    this.initializePosthog();
  }

  private initializePosthog(): void {
    posthog.init(environment.posthog.apiKey, {
      api_host: environment.posthog.apiHost,
    });
  }

  trackEvent(eventName: string, properties?: Record<string, unknown>): void {
    posthog.capture(eventName, properties);
  }

  identifyUser(userId: string, properties?: Record<string, unknown>): void {
    posthog.identify(userId, properties);
  }

  reset(): void {
    posthog.reset();
  }
}
