<table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
  <!-- Position Column -->
  <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      {{ column | titlecase }}
    </th>
    <td
      mat-cell
      [class]="element[column] ? element[column].toString() : ''"
      *matCellDef="let element">
      {{ element[column] ?? 'N/A' }}
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
