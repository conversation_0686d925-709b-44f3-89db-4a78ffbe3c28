<app-loader [loading]="loading"></app-loader>

<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="rounded-xl shadow-lg p-4 relative w-[860px] mat-dialog overflow-y-auto">
    <div class="flex justify-between items-center object-center pb-4">
      <h3 class="font-sm">New Plot</h3>
      <button
        mat-icon-button
        (click)="cancel()"
        class="text-setSettinggray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div
      class="border-t-[1px] w-full border-gray-300 plot-form overflow-hidden">
      <div class="plot-form py-4">
        <form [formGroup]="graphForm" class="modal-body">
          <!-- Plot Name section : STARTS -->
          <div class="flex align-bottom justify-between mb-4 align-items-end">
            <div>
              <label for="style" class="input-grp-ttll font-medium">
                {{ g_const.plotType }}
              </label>
              <div class="mb-2 w-full p-0">
                <form class="custom-mat-form-field p-0">
                  <mat-select
                    class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm w-[420px] h-12 rounded-md"
                    (selectionChange)="
                      setSelectedPlotTypeFromChangeEvent($event)
                    "
                    [disableOptionCentering]="true">
                    <mat-select-trigger>
                      <img
                        *ngIf="selectedPlotType()?.icon"
                        class="mr-2 inline"
                        src="assets/charts/{{ selectedPlotType()?.icon }}.svg"
                        alt="Chart Type Icon" />
                      <b>{{ selectedPlotType()?.plot_name }}</b>
                    </mat-select-trigger>
                    <mat-optgroup
                      *ngFor="
                        let category of plotTypes().CompleteList
                          | groupBy: 'category'
                      "
                      [label]="category.key">
                      <mat-option
                        *ngFor="let item of category.value"
                        [value]="item">
                        <img
                          *ngIf="item.icon"
                          height="15"
                          width="15"
                          class="mr-2 inline"
                          src="assets/charts/{{ item.icon }}.svg"
                          alt="Chart Type Icon" />
                        <b>{{ item.plot_name }}</b>
                      </mat-option>
                    </mat-optgroup>
                  </mat-select>
                </form>
              </div>
            </div>
            @if (switchOptions().length > 0) {
              <div class="mb-2 flex items-end">
                <div>
                  <mat-button-toggle-group
                    [formControl]="formControlPlotVariant"
                    (change)="setPlotVariantSetting($event)">
                    @for (toggleOption of switchOptions(); track toggleOption) {
                      <mat-button-toggle [value]="toggleOption">
                        {{ toggleOption.label }}
                      </mat-button-toggle>
                    }
                  </mat-button-toggle-group>
                </div>
              </div>
            }
          </div>
          <!-- Plot Name section : END -->
          <!-- Data Source section - START-->
          <div class="w-full mb-4">
            <label for="style" class="input-grp-ttl font-medium">{{
              g_const.data_source
            }}</label>
            <div>
              <!-- Input field that shows the selected file -->
              <input
                matInput
                placeholder="Select a file"
                [value]="selectedFileName"
                (click)="toggleFileList()"
                class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md"
                readonly />
              <!-- File List (Visible on Input Click) -->
              <div class="w-full" *ngIf="isFileListVisible">
                <div class="file-select-container">
                  <!-- Load previous files option -->
                  <div
                    class="file-select-option"
                    *ngIf="hasPreviousList"
                    (click)="loadPreviousFiles()"
                    (keyup.enter)="loadPreviousFiles()"
                    tabindex="0">
                    Load previous files...
                  </div>
                  <!-- Main folders and files -->
                  <div
                    *ngFor="let folder of hierarchicalFiles"
                    class="folder-group">
                    <div class="folder-name flex items-center gap-2">
                      <mat-icon fontSet="material-icons-outlined"
                        >folder
                      </mat-icon>
                      <div>{{ folder.folder_name }}</div>
                    </div>
                    <!-- Files in the main folder -->
                    <div *ngIf="folder.files.length > 0" class="file-list">
                      <div
                        *ngFor="let file of folder.files"
                        class="file-select-option flex items-center gap-2"
                        (click)="onFileSelect(file)"
                        (keyup.enter)="onFileSelect(file)"
                        tabindex="0">
                        <mat-icon fontSet="material-icons-outlined"
                          >table_chart
                        </mat-icon>
                        <div>{{ file.file_name }}</div>
                      </div>
                    </div>
                    <!-- Subfolders -->
                    <div
                      *ngFor="let subfolder of folder.subfolders"
                      class="subfolder-group flex items-center gap-2">
                      <ng-container
                        *ngTemplateOutlet="
                          renderSubfolder;
                          context: { subfolder: subfolder }
                        "></ng-container>
                    </div>
                  </div>
                  <!-- Load more files option -->
                  <div
                    class="file-select-option"
                    *ngIf="hasNextList"
                    (click)="loadNextFiles()"
                    (keyup.enter)="loadNextFiles()"
                    tabindex="0">
                    Load more files...
                  </div>
                  <!-- Subfolder Template -->
                  <ng-template #renderSubfolder let-subfolder="subfolder">
                    <div class="folder-name flex items-center gap-2">
                      <mat-icon fontSet="material-icons-outlined"
                        >folder
                      </mat-icon>
                      <div>{{ subfolder.folder_name }}</div>
                    </div>
                    <!-- Files in the subfolder -->
                    <div *ngIf="subfolder.files.length > 0" class="file-list">
                      <div
                        *ngFor="let file of subfolder.files"
                        class="file-select-option flex items-center gap-2"
                        (click)="onFileSelect(file)"
                        (keyup.enter)="onFileSelect(file)"
                        tabindex="0">
                        <mat-icon fontSet="material-icons-outlined"
                          >table_chart
                        </mat-icon>
                        <div>{{ file.file_name }}</div>
                      </div>
                    </div>
                    <!-- Nested subfolders -->
                    <div
                      *ngFor="let nestedSubfolder of subfolder.subfolders"
                      class="nested-subfolder-group">
                      <ng-container
                        *ngTemplateOutlet="
                          renderSubfolder;
                          context: { subfolder: nestedSubfolder }
                        "></ng-container>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
          <!-- Data Source section - END -->
          <!-- Only show the below section if the user selects plot_name and file -->
          @if (graphForm.value?.plot_name !== '' && selectedFileName !== '') {
            <!-- x-axis : STARTS -->
            <div class="flex gap-2">
              <div
                class="col-md-6 mb-2 flex flex-col"
                formArrayName="selected_options"
                *ngIf="getSelectedOptions">
                <ng-container
                  *ngFor="
                    let op of getSelectedOptions?.controls;
                    let fi = index
                  "
                  [formGroupName]="fi">
                  <div
                    class="mt-2"
                    *ngIf="
                      ['x-axis'].includes(op.get('option_name')?.value) &&
                      op.get('isRequired')?.value
                    ">
                    <label
                      for="style"
                      *ngIf="
                        isFirstElementOfControlKind(
                          op,
                          op.get('option_name')?.value
                        )
                      "
                      class="input-grp-ttl font-medium"
                      >{{ op.get('option_name')?.value }}</label
                    >
                    <div class="flex">
                      <div
                        class="input-group flex items-center custom-input-group w-[270px]"
                        *ngIf="op.get('selected_column_name')">
                        <mat-select
                          class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [class.faded]="!op.get('selected_column_name')?.value"
                          formControlName="selected_column_name"
                          (change)="updateFilteredOptions()"
                          required>
                          <mat-option value="" hidden defaultvalue>
                            {{ g_const.selectColumn }}
                          </mat-option>
                          <ng-container
                            *ngFor="let item of op.get('columnOptions')?.value">
                            <mat-option
                              *ngIf="
                                !selectedOptionsList.includes(item) ||
                                op.get('selected_column_name')?.value === item
                              "
                              [value]="item">
                              {{ item }}
                            </mat-option>
                          </ng-container>
                        </mat-select>
                      </div>
                      <div
                        class="input-group flex items-center custom-input-group w-[270px] ml-2 mr-2"
                        *ngIf="op.get('selected_aggregation')">
                        <mat-select
                          class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [class.faded]="!op.get('selected_aggregation')?.value"
                          formControlName="selected_aggregation"
                          required>
                          <mat-option value="" hidden defaultvalue>
                            {{ g_const.selectAggregation }}
                          </mat-option>
                          <mat-option
                            *ngFor="let item of aggregationList"
                            [value]="item">
                            {{ item }}
                          </mat-option>
                        </mat-select>
                      </div>
                      <div class="mt-2">
                        <label
                          for="style"
                          *ngIf="op.get('isMultiple')?.value"
                          role="button"
                          (click)="addNewOption(op.value, fi)"
                          (keyup.enter)="addNewOption(op.value, fi)"
                          tabindex="0"
                          class="flex items-center float-right"
                          [class.disabled]="
                            getSelectedOptions.value.length >=
                            op.get('columnOptions')?.value?.length
                          ">
                          <button
                            type="button"
                            mat-button
                            class="default-filter">
                            <mat-icon>add</mat-icon>
                            Column
                          </button>
                        </label>
                        <label
                          for="style"
                          *ngIf="op.get('isChild')?.value"
                          role="button"
                          (click)="removeOption(fi)"
                          (keyup.enter)="removeOption(fi)"
                          tabindex="0"
                          class="flex items-center float-right">
                          <button
                            type="button"
                            mat-button
                            class="default-filter">
                            <mat-icon>delete_outline</mat-icon>
                            Column
                          </button>
                        </label>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- x-axis ENDS -->
            <!-- y-axis : STARTS -->
            <div class="flex gap-2">
              <div
                class="col-md-6 mb-4 flex flex-col gap-2"
                formArrayName="selected_options"
                *ngIf="getSelectedOptions">
                <ng-container
                  *ngFor="
                    let op of getSelectedOptions?.controls;
                    let fi = index
                  "
                  [formGroupName]="fi">
                  <div
                    *ngIf="
                      ['y-axis'].includes(op.get('option_name')?.value) &&
                      op.get('isRequired')?.value
                    ">
                    <div class="flex">
                      <label
                        for="style"
                        *ngIf="
                          isFirstElementOfControlKind(
                            op,
                            op.get('option_name')?.value
                          )
                        "
                        class="input-grp-ttl font-medium"
                        >{{ op.get('option_name')?.value }}</label
                      >
                    </div>
                    <div class="flex gap-2">
                      <div
                        class="input-group flex items-center custom-input-group w-[270px]"
                        *ngIf="op.get('selected_column_name')">
                        <mat-select
                          class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [class.faded]="!op.get('selected_column_name')?.value"
                          formControlName="selected_column_name"
                          (change)="updateFilteredOptions()">
                          <mat-option value="" hidden defaultvalue>
                            {{ g_const.selectColumn }}
                          </mat-option>
                          <ng-container
                            *ngFor="let item of op.get('columnOptions')?.value">
                            <mat-option
                              *ngIf="
                                !selectedOptionsList.includes(item) ||
                                op.get('selected_column_name')?.value === item
                              "
                              [value]="item">
                              {{ item }}
                            </mat-option>
                          </ng-container>
                        </mat-select>
                      </div>
                      <div
                        class="input-group flex items-center custom-input-group w-[270px] ml-2 mr-2"
                        *ngIf="op.get('selected_aggregation')">
                        <mat-select
                          class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [class.faded]="!op.get('selected_aggregation')?.value"
                          formControlName="selected_aggregation"
                          required>
                          <mat-option value="" hidden defaultvalue>
                            {{ g_const.selectAggregation }}
                          </mat-option>
                          <mat-option
                            *ngFor="let item of aggregationList"
                            [value]="item">
                            {{ item }}
                          </mat-option>
                        </mat-select>
                      </div>
                      <div
                        *ngIf="op.get('isMultiple')?.value"
                        role="button"
                        (click)="addNewOption(op.value, fi)"
                        (keyup.enter)="addNewOption(op.value, fi)"
                        [tabindex]="fi"
                        class="flex items-center float-right"
                        [class.disabled]="
                          getSelectedOptions.value.length >=
                          op.get('columnOptions')?.value?.length
                        ">
                        <button type="button" mat-button class="default-filter">
                          <mat-icon>add</mat-icon>
                          Column
                        </button>
                      </div>
                      <div
                        *ngIf="op.get('isChild')?.value"
                        role="button"
                        (click)="removeOption(fi)"
                        (keyup.enter)="removeOption(fi)"
                        tabindex="0"
                        class="flex items-center float-right">
                        <button type="button" mat-button class="default-filter">
                          <mat-icon>delete_outline</mat-icon>
                          Column
                        </button>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- y-axis ENDS -->
            <!-- UNKNOWN SECTION : STARTS : Need better identification for this section -->
            <div
              class="flex flex-col mb-4"
              formArrayName="selected_options"
              *ngIf="getSelectedOptions">
              <ng-container
                *ngFor="let op of getSelectedOptions?.controls; let fi = index"
                [formGroupName]="fi">
                <div
                  class="col-md-6 mt-2"
                  *ngIf="
                    !['x-axis', 'y-axis', 'sort_by_column_name'].includes(
                      op.get('option_name')?.value
                    ) ||
                    (op.get('option_name')?.value === 'sort_by_column_name' &&
                      sortByColName.length > 0)
                  ">
                  <div>
                    <div class="">
                      <label
                        for="style"
                        *ngIf="
                          isFirstElementOfControlKind(
                            op,
                            op.get('option_name')?.value
                          )
                        "
                        class="input-grp-ttl font-medium">
                        {{ op.get('option_name')?.value }}
                      </label>
                    </div>
                  </div>
                  <div class="flex gap-2">
                    <div
                      class="input-group flex items-center custom-input-group w-[270px]"
                      *ngIf="op.get('selected_column_name')">
                      <mat-select
                        class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                        [class.faded]="!op.get('selected_column_name')?.value"
                        formControlName="selected_column_name"
                        (change)="updateFilteredOptions()">
                        <mat-option value="" hidden defaultvalue>
                          {{ g_const.selectColumn }}
                        </mat-option>
                        <ng-container
                          *ngIf="
                            !['sort_by_column_name'].includes(
                              op.get('option_name')?.value
                            );
                            else sortByColumnName
                          ">
                          <ng-container
                            *ngFor="let item of op.get('columnOptions')?.value">
                            <mat-option
                              *ngIf="
                                !selectedOptionsList.includes(item) ||
                                op.get('selected_column_name')?.value === item
                              "
                              [value]="item">
                              {{ item }}
                            </mat-option>
                          </ng-container>
                        </ng-container>
                        <ng-template #sortByColumnName>
                          <ng-container *ngFor="let item of sortByColName">
                            <option [value]="item">{{ item }}</option>
                          </ng-container>
                        </ng-template>
                      </mat-select>
                    </div>
                    <div
                      class="input-group flex items-center custom-input-group w-[270px] gap-2"
                      *ngIf="op.get('selected_aggregation')">
                      <mat-select
                        class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                        [class.faded]="!op.get('selected_aggregation')?.value"
                        formControlName="selected_aggregation">
                        <mat-option value="" hidden defaultvalue>
                          {{ g_const.selectAggregation }}
                        </mat-option>
                        <mat-option
                          *ngFor="let item of aggregationList"
                          [value]="item">
                          {{ item }}
                        </mat-option>
                      </mat-select>
                    </div>
                    <div class="flex items-center">
                      <label
                        for="style"
                        *ngIf="op.get('isMultiple')?.value"
                        role="button"
                        (click)="addNewOption(op.value, fi)"
                        (keyup.enter)="addNewOption(op.value, fi)"
                        tabindex="0"
                        class="flex items-center float-right"
                        [class.disabled]="
                          getSelectedOptions.value.length >=
                          op.get('columnOptions')?.value?.length
                        ">
                        <button type="button" mat-button class="default-filter">
                          <mat-icon>add</mat-icon>
                          {{ g_const.column }}
                        </button>
                      </label>
                      <label
                        for="style"
                        *ngIf="op.get('isChild')?.value"
                        role="button"
                        (click)="removeOption(fi)"
                        (keyup.enter)="removeOption(fi)"
                        tabindex="0"
                        class="flex items-center float-right mt-2">
                        <button type="button" mat-button class="default-filter">
                          <mat-icon>delete_outline</mat-icon>
                          {{ g_const.column }}
                        </button>
                      </label>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
            <!-- UNKNOWN SECTION : ENDS -->
            <!-- Settings section-->
            <div class="flex" *ngIf="isSettingsEnabled">
              <div class="col-md-6 mb-4 w-full">
                <ng-container>
                  <div>
                    <div class="flex">
                      <label for="style" class="input-grp-ttl font-medium">{{
                        g_const.settings
                      }}</label>
                    </div>
                    <div class="flex multiple gap-2">
                      <div
                        class="multiple input-group flex items-center custom-input-group w-1/2 mt-1">
                        <mat-select
                          class="form-select outline-none bg-[#f7f9ff] flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [multiple]="dropdownSettingsMultiple"
                          [placeholder]="'Select Settings'"
                          [formControl]="formControlSettings"
                          (selectionChange)="setSetting($event)">
                          <mat-select-trigger>
                            {{ formControlSettings.getRawValue()?.length ?? 0 }}
                            Values selected
                          </mat-select-trigger>
                          <mat-option
                            *ngFor="let item of settingsList"
                            [value]="item">
                            {{ item }}
                          </mat-option>
                        </mat-select>
                      </div>
                      <div
                        class="multiple input-group flex items-center custom-input-group mt-1 w-1/2"
                        *ngIf="isSmartBucketingEnabled">
                        <mat-select
                          required
                          class="form-select outline-none bg-[#f7f9ff] opacity-50 flex-grow border px-3 py-2 shadow-sm min-w-[100px] h-12 rounded-md"
                          [class.faded]="!selectedSmartBucketing"
                          [(ngModel)]="selectedSmartBucketing"
                          [ngModelOptions]="{ standalone: true }">
                          <mat-option value="" hidden defaultvalue>
                            {{ g_const.smartBucketing }}
                          </mat-option>
                          <mat-option
                            *ngFor="let item of smartBucketingList"
                            [value]="item">
                            {{ item.smart_bucketing_name }}
                          </mat-option>
                        </mat-select>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- Settings section -end -->
          }
        </form>
      </div>
    </div>
    <div class="flex gap-2">
      <button
        mat-flat-button
        class="mt-8"
        type="submit"
        [disabled]="
          this.graphForm.invalid ||
          selectedPlotType() === null ||
          selectedFileID === 0
        "
        (click)="submit()">
        {{ g_const.save }}
      </button>
      <button
        mat-button
        class="default-filter text-txt-color mt-8"
        (click)="cancel()">
        {{ g_const.cancel }}
      </button>
    </div>
    <div></div>
  </div>
</div>
