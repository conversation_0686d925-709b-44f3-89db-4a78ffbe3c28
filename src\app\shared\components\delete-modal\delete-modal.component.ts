import { Component, EventEmitter, Input, Output } from '@angular/core';
import { g_const } from '../../../_utility/global_const';

@Component({
  selector: 'app-delete-modal',
  templateUrl: './delete-modal.component.html',
  styleUrl: './delete-modal.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class DeleteModalComponent {
  g_const = g_const;
  @Input() isModalOpen = false;
  @Input() modalTitle = '';
  @Output() cancelEvent = new EventEmitter<boolean>();
  @Output() deleteEvent = new EventEmitter<boolean>();

  @Input() projectTitle = '';

  cancel(): void {
    this.cancelEvent.emit(true);
  }

  delete(): void {
    this.deleteEvent.emit(true);
  }
}
