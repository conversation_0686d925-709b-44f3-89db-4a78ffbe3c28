<div class="flex flex-col h-screen md:flex-row">
  <div class="relative">
    <img
      src="../../../../assets/Welcome.png"
      alt="Side Image"
      class="w-full h-[30vh] md:h-[100vh] md:w-[40vw]" />

    <div
      class="absolute inset-y-0 left-0 flex items-center justify-center ml-4 sm:ml-8">
      <div class="text-white">
        <h1 class="text-white m-0 welcome">Welcome</h1>
        <p class="mt-3">Please Enter Your Details.</p>
      </div>
    </div>
  </div>

  <div
    class="flex-1 p-5 rounded-lg flex flex-col justify-center items-center lg:ml-[-15px]">
    <form
      [formGroup]="loginForm"
      (ngSubmit)="onSubmit()"
      class="w-full max-w-xs lg:max-w-sm">
      <div class="mb-[60px] flex flex-col">
        <img
          src="../../assets/Logo.png"
          alt="Side Image"
          class="size-16 mb-6" />
        <h1>{{ g_const.logIn }}</h1>
      </div>

      <div class="w-full mb-6">
        <label for="email" class="block mb-2 text-sm font-medium">{{
          g_const.email
        }}</label>
        <input
          type="email"
          id="email"
          placeholder="E-Mail"
          formControlName="email"
          class="form-input" />

        <!-- Email validation error messages -->
        <div
          *ngIf="
            loginForm.get('email')?.touched && loginForm.get('email')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="loginForm.get('email')?.errors?.['required']">
            {{ g_const.emailRequired }}
          </div>
          <div *ngIf="loginForm.get('email')?.errors?.['email']">
            {{ g_const.emailInvalid }}
          </div>
        </div>
      </div>

      <div class="w-full mb-6">
        <label for="password" class="block mb-2 text-sm font-medium">{{
          g_const.password
        }}</label>
        <div
          class="flex items-center bg-white w-full rounded-lg border border-gray-300 box-border pl-2 p-2">
          <input
            [type]="passwordFieldType"
            id="password1"
            placeholder="Password"
            formControlName="password"
            class="outline-none bg-white flex-1" />
          <button
            (click)="togglePasswordVisibility()"
            type="button"
            class="mr-1 mt-1">
            <mat-icon>{{
              passwordFieldType === 'password' ? 'visibility' : 'visibility_off'
            }}</mat-icon>
          </button>
        </div>

        <!-- Password validation error messages -->
        <div
          *ngIf="
            loginForm.get('password')?.touched &&
            loginForm.get('password')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="loginForm.get('password')?.errors?.['required']">
            {{ g_const.passwordRequired }}
          </div>
          <div *ngIf="loginForm.get('password')?.errors?.['minlength']">
            {{ g_const.min8charRequired }}
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center mb-6">
        <section class="flex items-center">
          <mat-checkbox formControlName="keep_me_logged_in" class="">
            {{ g_const.keepLogged }}
          </mat-checkbox>
        </section>
        <a
          href="javascript:void(0)"
          class="text-sm text-txt-color no-underline ml-2"
          (click)="navigateToForgetPassword()"
          >{{ g_const.forgotPassword }}</a
        >
      </div>

      <span class="block text-sm text-gray-400 mb-4">
        By logging in, you agree to the
        <a
          href="https://www.aicuflow.com/privacy-policy"
          target="_blank"
          class="text-txt-color no-underline">
          data privacy policy</a
        >
      </span>
      <!-- Disabled attribute bound to form's validity -->
      <button
        mat-flat-button
        [disabled]="loginForm.invalid"
        class="w-full mb-[10px] p-3 initial-text">
        {{ g_const.logIn }}
      </button>

      <span class="block text-sm text-gray-400">
        {{ g_const.dontHaveAcc }}
        <a
          href="javascript:void(0)"
          class="text-txt-color no-underline"
          (click)="navigateTosignUp()"
          >{{ g_const.signUp }}</a
        >
      </span>
    </form>
  </div>
</div>
