import {
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
  ViewEncapsulation,
} from '@angular/core';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NgxFileDropModule } from 'ngx-file-drop';
import { PlotlyViaCDNModule } from 'angular-plotly.js';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, NgxFileDropModule],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class AppComponent implements OnInit {
  title = 'AICU';

  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    @Inject(DOCUMENT) private document: Document,
  ) {}

  async ngOnInit(): Promise<void> {
    // Ensure this code only runs in the browser environment
    if (isPlatformBrowser(this.platformId)) {
      PlotlyViaCDNModule.setPlotlyVersion('1.55.2', 'plotly'); // Configure Plotly CDN version
      PlotlyViaCDNModule.setPlotlyBundle(null);

      // Detect the user's color scheme preference
      const prefersDarkScheme = window.matchMedia(
        '(prefers-color-scheme: dark)',
      );

      // Apply the appropriate class to the body based on preference
      if (prefersDarkScheme.matches) {
        this.document.body.classList.add('dark-mode');
      } else {
        this.document.body.classList.add('light-mode');
      }

      // Listen for changes in the user's color scheme preference
      prefersDarkScheme.addEventListener('change', e => {
        if (e.matches) {
          this.document.body.classList.add('dark-mode');
          this.document.body.classList.remove('light-mode');
        } else {
          this.document.body.classList.add('light-mode');
          this.document.body.classList.remove('dark-mode');
        }
      });
    }
  }
}
