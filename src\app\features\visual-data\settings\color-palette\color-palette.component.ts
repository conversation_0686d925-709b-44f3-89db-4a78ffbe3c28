import {
  Component,
  computed,
  inject,
  input,
  model,
  output,
  untracked,
} from '@angular/core';
import { ColorTileComponent } from './color-tile/color-tile.component';
import {
  ColorContainer,
  ColorPalette,
} from '../../../../_models/visual-data/visual-data.model';
import { MatIcon } from '@angular/material/icon';
import { ColorSettingsService } from '../color-settings.service';

@Component({
  selector: 'app-color-palette',
  imports: [ColorTileComponent, MatIcon],
  templateUrl: './color-palette.component.html',
  styleUrl: './color-palette.component.css',
})
export class ColorPaletteComponent {
  colorPalette = input.required<ColorPalette>();
  colorPaletteColorsWithId = computed(() =>
    this.colorPalette().colors.map(
      (c, index): ColorContainer => ({
        id: index + 1000 * untracked(this.colorPalette).id,
        index: index,
        colorHex: c,
      }),
    ),
  );

  colorPaletteSelected = input.required<boolean>();
  colorEditMode = input<boolean>(false);
  colorEditState = input<string | null>(null);
  colorTileInEditMode = model<ColorTileComponent | null>(null);
  addingColor = output<ColorPalette>();
  colorSettingsService = inject(ColorSettingsService);
  colorIdInEdit = this.colorSettingsService.currentlySelectedColorTileId;
  addingColorTileVisible = computed(() => {
    const colorIdInEdit = this.colorIdInEdit();
    return colorIdInEdit === null || colorIdInEdit.id < 0;
  });

  existingColorClicked(
    event: MouseEvent | Event,
    colorId: number,
    index: number,
    colorHex?: string,
  ) {
    if (this.colorPaletteSelected()) {
      this.switchToEditMode(event, colorId, index, colorHex);
    }
  }

  addButtonClicked(
    event: MouseEvent | Event,
    colorId: number,
    colorHex?: string,
  ) {
    this.switchToEditMode(event, colorId, -1, colorHex);
  }

  switchToEditMode(
    event: MouseEvent | Event,
    colorId: number,
    index: number,
    colorHex?: string,
  ) {
    if (this.colorPaletteSelected()) {
      event.stopPropagation();
    }
    this.colorSettingsService.currentlySelectedColorTileId.set({
      id: colorId,
      index,
      colorHex: colorHex ?? '#FFFFF',
    });
    if (colorHex === undefined) {
      this.colorSettingsService.setCurrentlySelectedColorInPicker('#FFFFFF');
    } else {
      this.colorSettingsService.setCurrentlySelectedColorInPicker(colorHex);
    }
    this.addingColor.emit(this.colorPalette());
  }
}
