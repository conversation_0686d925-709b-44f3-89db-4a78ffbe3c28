import { Component, inject, OnInit } from '@angular/core';
import { MatButton } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogConfig,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TrainingService } from '../../service/training-service.service';
import { NgxFileDropModule } from 'ngx-file-drop';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../../../shared/shared.module';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-start-inference-dialog',
  imports: [
    MatDialogModule,
    MatIcon,
    MatTabsModule,
    NgxFileDropModule,
    CommonModule,
    SharedModule,
    MatButton,
    ReactiveFormsModule,
    MatSelectModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './start-inference-dialog.component.html',
  styleUrl: './start-inference-dialog.component.css',
})
export class StartInferenceDialogComponent implements OnInit {
  static viewConfig: MatDialogConfig = {
    maxWidth: '100%',
    width: '860px',
    disableClose: true,
    maxHeight: '100%',
  };
  readonly data = inject<{ trainingId: number; type: string }>(MAT_DIALOG_DATA);
  readonly dialogRef = inject(MatDialogRef<StartInferenceDialogComponent>);
  selected_features: {
    id: number;
    name: string;
    type: string;
    options: string[];
  }[] = [];
  loading = true;
  result!: { predicted_column: string; predicted_values: string[] } | null;
  predictForm!: FormGroup;
  inference_id = 0;
  predictLoader = false;

  constructor(
    private trainingService: TrainingService,
    private fb: FormBuilder,
    private toasterService: ToastrService,
  ) {}
  ngOnInit(): void {
    this.predictForm = this.fb.group({
      entries: this.fb.array([]),
    });
    if (this.data.type == 'new') {
      this.trainingService.startNewInference(this.data.trainingId).subscribe({
        next: response => {
          this.getMlInference(response?.data);
        },
        error: error => {
          this.toasterService.error(error.message);
        },
      });
    } else {
      this.trainingService
        .checkRunningInference(this.data.trainingId)
        .subscribe({
          next: response => {
            this.getMlInference(response?.data);
          },
          error: error => {
            this.toasterService.error(error.message);
          },
        });
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getMlInference(inferenceInfo: any) {
    this.loading = false;
    this.selected_features = inferenceInfo?.selected_features;
    this.inference_id = inferenceInfo?.id;
    this.selected_features.map(feature => {
      const entry = this.fb.group({
        id: [feature.id, Validators.required],
        name: [feature.name, Validators.required],
        type: [feature.type],
        options: [feature.options],
        value: ['', Validators.required],
      });
      this.entries.push(entry);
    });
  }

  get entries(): FormArray {
    return this.predictForm.get('entries') as FormArray;
  }

  getPredictions() {
    this.predictLoader = true;
    this.result = null;
    const payload = {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      features: this.entries.value.map((entry: any) => ({
        id: entry.id,
        name: entry.name,
        value: entry.type == 'IntegerField' ? Number(entry.value) : entry.value,
      })),
    };
    this.trainingService
      .getTraningPredictions(payload, this.inference_id)
      .subscribe({
        next: predictionResult => {
          this.predictLoader = false;
          this.result = predictionResult?.data?.result;
        },
        error: error => {
          this.predictLoader = false;
          this.toasterService.error(error?.error?.errors?.error?.detail);
        },
      });
  }
  OnCloseModal() {
    this.dialogRef.close(this.inference_id);
  }

  getCategoricalOptions(options: string[]) {
    return options.filter(option => option !== 'nan');
  }

  preventDecimal(event: KeyboardEvent): void {
    if (event.key === '.' || event.key === ',' || event.key === 'e') {
      event.preventDefault();
    }
  }
}
