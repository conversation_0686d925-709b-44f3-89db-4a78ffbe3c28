import { Component } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { g_const } from '../../../../_utility/global_const';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-new-password',
  imports: [
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    CommonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './new-password.component.html',
  styleUrls: ['./new-password.component.css'],
})
export class NewPasswordComponent {
  g_const = g_const;
  newPasswordForm: FormGroup;
  errorMessage = '';
  resetKey: string | null = '';

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
    private route: ActivatedRoute,
  ) {
    this.resetKey = this.route.snapshot.paramMap.get('key');
    this.newPasswordForm = this.formBuilder.group(
      {
        password_reset_token: ['', Validators.required],
        password: ['', [Validators.required, Validators.minLength(8)]],
        password2: ['', [Validators.required, Validators.minLength(8)]],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password')?.value;
    const password2 = formGroup.get('password2')?.value;
    return password === password2 ? null : { passwordMismatch: true };
  }

  onSubmit(): void {
    if (this.newPasswordForm.valid) {
      const payload = {
        password: this.newPasswordForm.get('password')?.value,
        password_reset_token: this.newPasswordForm.get('password_reset_token')
          ?.value,
      };

      this.authService.newPassword(payload).subscribe({
        next: res => {
          this.toastrService.success(`${res.message}`);
          this.router.navigateByUrl('/auth/login');
        },
        error: err => {
          this.toastrService.error(`${err.error.error}`);
        },
      });
    }
  }

  navigateTosignUp() {
    this.router.navigate(['/auth/register']);
  }
}
