import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RegisterComponent } from './register.component';
import { Router } from '@angular/router';
import { AuthService } from '../../../../services/auth.services';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

describe('RegisterComponent', () => {
  let component: RegisterComponent;
  let fixture: ComponentFixture<RegisterComponent>;
  let authService: jest.Mocked<AuthService>;
  let router: jest.Mocked<Router>;
  let toastrService: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    const authServiceMock = {
      register: jest.fn(),
    } as unknown as jest.Mocked<AuthService>;
    const routerMock = {
      navigateByUrl: jest.fn(),
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, RegisterComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(RegisterComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;

    fixture.detectChanges(); // Trigger initial data binding
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with required fields', () => {
    const emailControl = component.signupForm.get('email');
    const firstNameControl = component.signupForm.get('first_name');
    const lastNameControl = component.signupForm.get('last_name');
    const password1Control = component.signupForm.get('password1');
    const password2Control = component.signupForm.get('password2');

    expect(emailControl).toBeTruthy();
    expect(firstNameControl).toBeTruthy();
    expect(lastNameControl).toBeTruthy();
    expect(password1Control).toBeTruthy();
    expect(password2Control).toBeTruthy();
  });

  describe('Form validation', () => {
    it('should require email, first name, last name, and passwords', () => {
      component.signupForm.get('email')?.setValue('');
      component.signupForm.get('first_name')?.setValue('');
      component.signupForm.get('last_name')?.setValue('');
      component.signupForm.get('password1')?.setValue('');
      component.signupForm.get('password2')?.setValue('');

      expect(component.signupForm.get('email')?.hasError('required')).toBe(
        true,
      );
      expect(component.signupForm.get('first_name')?.hasError('required')).toBe(
        true,
      );
      expect(component.signupForm.get('last_name')?.hasError('required')).toBe(
        true,
      );
      expect(component.signupForm.get('password1')?.hasError('required')).toBe(
        true,
      );
      expect(component.signupForm.get('password2')?.hasError('required')).toBe(
        true,
      );
    });

    it('should validate email format', () => {
      component.signupForm.get('email')?.setValue('invalid-email');
      expect(component.signupForm.get('email')?.hasError('email')).toBe(true);
    });

    it('should require passwords to be at least 8 characters long', () => {
      component.signupForm.get('password1')?.setValue('short');
      component.signupForm.get('password2')?.setValue('short');
      expect(component.signupForm.get('password1')?.hasError('minlength')).toBe(
        true,
      );
      expect(component.signupForm.get('password2')?.hasError('minlength')).toBe(
        true,
      );
    });

    it('should validate that passwords match', () => {
      component.signupForm.get('password1')?.setValue('password123');
      component.signupForm.get('password2')?.setValue('different123');
      expect(component.signupForm.hasError('passwordMismatch')).toBe(true);
    });

    it('should accept valid form inputs', () => {
      component.signupForm.get('email')?.setValue('<EMAIL>');
      component.signupForm.get('first_name')?.setValue('John');
      component.signupForm.get('last_name')?.setValue('Doe');
      component.signupForm.get('password1')?.setValue('password123');
      component.signupForm.get('password2')?.setValue('password123');
      expect(component.signupForm.valid).toBe(true);
    });
  });

  describe('onSubmit', () => {
    it('should call register on authService with form data and navigate on success', () => {
      const mockResponse = { message: 'Registration successful' };
      authService.register.mockReturnValue(of(mockResponse));

      component.signupForm.get('email')?.setValue('<EMAIL>');
      component.signupForm.get('first_name')?.setValue('John');
      component.signupForm.get('last_name')?.setValue('Doe');
      component.signupForm.get('password1')?.setValue('password123');
      component.signupForm.get('password2')?.setValue('password123');
      component.onSubmit();

      expect(authService.register).toHaveBeenCalledWith(
        component.signupForm.value,
      );
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message);
      expect(router.navigateByUrl).toHaveBeenCalledWith('/auth/login');
    });

    it('should show an error message on registration failure', () => {
      const mockError = { error: { error: 'Registration failed' } };
      authService.register.mockReturnValue(throwError(mockError));

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith(mockError.error.error);
      expect(component.loading).toBe(false);
    });
  });

  describe('Password visibility toggling', () => {
    it('should toggle password field type for password1', () => {
      component.passwordFieldType = 'password';
      component.togglePasswordVisibility();
      expect(component.passwordFieldType).toBe('text');

      component.togglePasswordVisibility();
      expect(component.passwordFieldType).toBe('password');
    });

    it('should toggle password field type for password2', () => {
      component.repeatPasswordFieldType = 'password';
      component.toggleRepeatPasswordFieldType();
      expect(component.repeatPasswordFieldType).toBe('text');

      component.toggleRepeatPasswordFieldType();
      expect(component.repeatPasswordFieldType).toBe('password');
    });
  });

  it('should navigate to login page', () => {
    component.navigateToLogin();
    expect(router.navigate).toHaveBeenCalledWith(['/auth/login']);
  });
});
