import { Children, PlotStyleOption } from './visual-data.model';

export class PlotStyleOptionBuilder {
  id = 2103446257213716;
  name = 'o';
  value = 'Y';
  data_type = 'p';
  description = 'G';
  options: PlotStyleOption[] = [];
  parent?: number = 5360819734208748;
  children: Children[] = [];

  withId(value: number): PlotStyleOptionBuilder {
    this.id = value;
    return this;
  }

  withName(value: string): PlotStyleOptionBuilder {
    this.name = value;
    return this;
  }

  withValue(value: string): PlotStyleOptionBuilder {
    this.value = value;
    return this;
  }

  withDataType(value: string): PlotStyleOptionBuilder {
    this.data_type = value;
    return this;
  }

  withDescription(value: string): PlotStyleOptionBuilder {
    this.description = value;
    return this;
  }

  withOptions(value: PlotStyleOption[]): PlotStyleOptionBuilder {
    this.options = value;
    return this;
  }

  withParent(value: number): PlotStyleOptionBuilder {
    this.parent = value;
    return this;
  }

  with<PERSON><PERSON><PERSON>n(value: Children[]): PlotStyleOptionBuilder {
    this.children = value;
    return this;
  }

  build(): PlotStyleOption {
    return {
      id: this.id,
      name: this.name,
      value: this.value,
      data_type: this.data_type,
      description: this.description,
      options: this.options,
      parent: this.parent,
      children: this.children,
    };
  }
}
