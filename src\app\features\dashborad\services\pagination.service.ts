import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PaginationService {
  calculatePageSize(): number {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    if (screenWidth <= 768) return screenHeight <= 800 ? 5 : 8; // Tablet size
    if (screenWidth <= 1440) return screenHeight <= 900 ? 10 : 12; // Laptop size
    return screenHeight <= 1080 ? 15 : 20; // Larger screen sizes
  }

  setPagination(dataLength: number, pageSize: number): number[] {
    const totalPages = Math.ceil(dataLength / pageSize);
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }
}
