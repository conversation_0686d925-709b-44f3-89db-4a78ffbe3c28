// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ModelOverviewData = (resultData: any) => {
  const displayData = [
    {
      name: 'Model Name',
      value:
        resultData?.ml_training?.machine_learning_model?.name ??
        resultData.machine_learning_model.name,
    },
    {
      name: 'Training Status',
      value: resultData?.ml_training?.status ?? resultData.status,
    },
    { name: 'Training Date', value: resultData.training_date },
    { name: 'Training Duration', value: resultData.training_duration },
    {
      name: 'File Name',
      value:
        resultData?.ml_training?.file?.file_name ?? resultData.file.file_name,
    },
    {
      name: 'Selected Target',
      value: resultData.selected_target,
    },
    {
      name: 'Selected Features',
      value: resultData.selected_features,
    },
  ];
  const displayColumns = ['name', 'value'];
  return { displayData: displayData, displayColumns: displayColumns };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const RegressionMetric = (resultData: any) => {
  const displayData = [
    { metric: 'Mean Squared Error (MSE)', value: formatValue(resultData.mse) },
    {
      metric: 'Root Mean Squared Error (RMSE)',
      value: formatValue(resultData.rmse),
    },
    { metric: 'Mean Absolute Error (MAE)', value: formatValue(resultData.mae) },
    {
      metric: 'Median Absolute Error (MedAE)',
      value: formatValue(resultData.medae),
    },
    { metric: 'R-Squared (R²)', value: formatValue(resultData.r_squared) },
    {
      metric: 'Explained Variance',
      value: formatValue(resultData.explained_variance),
    },
    {
      metric: 'Relative Absolute Error (RAE)',
      value: formatValue(resultData.rae),
    },
    {
      metric: 'Residual Standard Error (RSE)',
      value: formatValue(resultData.rse),
    },
    {
      metric: 'Coefficient of Variation (CV)',
      value: formatValue(resultData.cv),
    },
    { metric: 'Mean Bias Deviation (MBD)', value: formatValue(resultData.mbd) },
    {
      metric: 'Quantile Loss (90th Percentile)',
      value: formatValue(resultData.quantile_loss_90),
    },
    {
      metric: 'Mean Absolute Percentage Error (MAPE, 90th Percentile)',
      value: formatValue(resultData.mape_90),
    },
  ];
  const displayColumns = ['metric', 'value'];
  return { displayData: displayData, displayColumns: displayColumns };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ClassificationMetric = (resultData: any) => {
  const displayData = [
    { metric: 'Accuracy', value: formatValue(resultData.accuracy) },
    { metric: 'F1-Score', value: formatValue(resultData.f1_score) },
    { metric: 'Precision', value: formatValue(resultData.precision) },
    { metric: 'Recall', value: formatValue(resultData.recall) },
    { metric: 'ROC AUC', value: formatValue(resultData.roc_auc) },
    { metric: 'PR AUC', value: formatValue(resultData.pr_auc) },
    { metric: 'True Positive', value: formatValue(resultData.true_positive) },
    { metric: 'True Negative', value: formatValue(resultData.true_negative) },
    { metric: 'False Positive', value: formatValue(resultData.false_positive) },
    { metric: 'False Negative', value: formatValue(resultData.false_negative) },
  ];
  const displayColumns = ['metric', 'value'];
  return { displayData: displayData, displayColumns: displayColumns };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ClassificationReport = (resultData: any) => {
  const displayData = Object.keys(resultData.classification_report)
    .filter(key => typeof resultData.classification_report[key] === 'object') // Filter out non-object keys like "accuracy"
    .map(key => ({
      class: key,
      recall: formatValue(resultData.classification_report[key].recall),
      precision: formatValue(resultData.classification_report[key].precision),
      f1_score: formatValue(resultData.classification_report[key]['f1-score']),
      support: formatValue(resultData.classification_report[key].support),
    }));
  const displayColumns = [
    'class',
    'recall',
    'precision',
    'f1_score',
    'support',
  ];

  return { displayData: displayData, displayColumns: displayColumns };
};

function formatValue(num: string | number) {
  return typeof num === 'number' ? num.toFixed(2) : num;
}
