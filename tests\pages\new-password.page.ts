import { Page, Locator } from '@playwright/test';

export class NewPasswordPage {
  // Locators
  readonly resetTokenInput: Locator;
  readonly passwordInput: Locator;
  readonly confirmPasswordInput: Locator;
  readonly saveButton: Locator;
  readonly signUpLink: Locator;
  readonly welcomeText: Locator;
  readonly welcomeImage: Locator;
  readonly logoImage: Locator;
  readonly passwordErrorMessage: Locator;
  readonly passwordMatchErrorMessage: Locator;
  readonly resetTokenErrorMessage: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;
  readonly newPasswordTitle: Locator;

  constructor(private page: Page) {
    this.resetTokenInput = page.locator(
      'input[formControlName="password_reset_token"]',
    );
    this.passwordInput = page.locator('input[formControlName="password"]');
    this.confirmPasswordInput = page.locator(
      'input[formControlName="password2"]',
    );
    this.saveButton = page.locator('button:has-text("Save")');
    this.signUpLink = page.locator('a', { hasText: 'Sign Up' });
    this.welcomeText = page.locator('h1.welcome', { hasText: 'Welcome' });
    this.welcomeImage = page.locator('img[src*="assets/Welcome.png"]');
    this.logoImage = page.locator('img[src*="assets/Logo.png"]');
    this.passwordErrorMessage = page.locator(
      'text=Minimum 8 characters are required',
    );
    this.passwordMatchErrorMessage = page.locator(
      'text=Passwords do not match',
    );
    this.resetTokenErrorMessage = page.locator('text=Reset token is required');
    this.successToast = page.locator('.toast-success');
    this.errorToast = page.locator('.toast-error');
    this.newPasswordTitle = page.locator('h1', { hasText: 'New Password' });
  }

  /**
   * Navigate to the new password page
   */
  async goto() {
    try {
      // Navigate to the new password page with a longer timeout
      await this.page.goto('/auth/new-password', { timeout: 30000 });

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
      await this.page.waitForLoadState('networkidle', { timeout: 30000 });

      // Wait a bit more to ensure everything is loaded
      await this.page.waitForTimeout(1000);
    } catch (e) {
      // Error navigating to new password page
      throw e;
    }
  }

  /**
   * Fill the new password form
   */
  async fillNewPasswordForm(
    resetToken: string,
    password: string,
    confirmPassword: string,
  ) {
    await this.resetTokenInput.fill(resetToken);
    await this.passwordInput.fill(password);
    await this.confirmPasswordInput.fill(confirmPassword);
  }

  /**
   * Click the save button
   */
  async clickSave() {
    try {
      // Click the save button with a longer timeout
      await this.saveButton.click({ timeout: 10000 });

      // Wait for navigation or response
      await this.page.waitForTimeout(2000); // Wait a bit for any response

      // Wait for any network requests to complete
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (e) {
      // Error clicking save button
      throw e; // Re-throw to allow test to fail
    }
  }

  /**
   * Complete the new password process with provided information
   * Returns the current state without assertions
   */
  async setNewPassword(
    resetToken: string,
    password: string,
    confirmPassword: string,
  ) {
    try {
      // Fill the form
      await this.fillNewPasswordForm(resetToken, password, confirmPassword);

      // Click save
      await this.clickSave();

      // Return current state without assertions
      return {
        url: this.page.url(),
        hasSuccessToast: await this.successToast.isVisible(),
        hasErrorToast: await this.errorToast.isVisible(),
        successToastText: await this.getSuccessToastText(),
        errorToastText: await this.getErrorToastText(),
      };
    } catch (e) {
      // Error during new password process
      return {
        url: this.page.url(),
        hasSuccessToast: false,
        hasErrorToast: false,
        successToastText: null,
        errorToastText: null,
        error: e,
      };
    }
  }

  /**
   * Navigate to the signup page
   */
  async goToSignUp() {
    await this.signUpLink.click();
    await this.page.waitForURL(/\/register/, { timeout: 10000 });
  }

  /**
   * Check if save button is disabled
   */
  async isSaveButtonDisabled() {
    return this.saveButton.isDisabled();
  }

  /**
   * Get the current URL
   */
  async getCurrentUrl() {
    return this.page.url();
  }

  /**
   * Check if password validation error is visible
   */
  async isPasswordErrorVisible() {
    return this.passwordErrorMessage.isVisible();
  }

  /**
   * Check if password match error is visible
   */
  async isPasswordMatchErrorVisible() {
    return this.passwordMatchErrorMessage.isVisible();
  }

  /**
   * Check if reset token error is visible
   */
  async isResetTokenErrorVisible() {
    return this.resetTokenErrorMessage.isVisible();
  }

  /**
   * Get text from success toast if visible
   */
  async getSuccessToastText() {
    try {
      const successToasts = this.page.locator('.toast-success');
      const count = await successToasts.count();

      if (count > 0) {
        // Get text from the first success toast
        return await successToasts.first().textContent();
      }
    } catch (e) {
      // Error getting success toast text
    }
    return null;
  }

  /**
   * Get text from error toast if visible
   */
  async getErrorToastText() {
    try {
      const errorToasts = this.page.locator('.toast-error');
      const count = await errorToasts.count();

      if (count > 0) {
        // Get text from the first error toast
        return await errorToasts.first().textContent();
      }
    } catch (e) {
      // Error getting error toast text
    }
    return null;
  }

  /**
   * Get visibility status of UI elements on the new password page
   * @returns Object containing visibility status of all UI elements
   */
  async getUIElementsVisibility() {
    try {
      // Use longer timeouts for visibility checks
      const timeout = { timeout: 10000 };

      // Collect the visibility status of all elements
      const currentUrl = this.page.url();
      const isOnNewPasswordPage = /\/new-password/i.test(currentUrl);

      return {
        currentUrl,
        isOnNewPasswordPage,
        formElements: {
          resetTokenInputVisible: await this.resetTokenInput.isVisible(timeout),
          passwordInputVisible: await this.passwordInput.isVisible(timeout),
          confirmPasswordInputVisible:
            await this.confirmPasswordInput.isVisible(timeout),
          saveButtonVisible: await this.saveButton.isVisible(timeout),
        },
        navigationElements: {
          signUpLinkVisible: await this.signUpLink.isVisible(timeout),
        },
        uiElements: {
          welcomeTextVisible: await this.welcomeText.isVisible(timeout),
          welcomeImageVisible: await this.welcomeImage.isVisible(timeout),
          logoImageVisible: await this.logoImage.isVisible(timeout),
          newPasswordTitleVisible:
            await this.newPasswordTitle.isVisible(timeout),
        },
      };
    } catch (e: unknown) {
      // Error checking UI elements visibility
      const errorMessage = e instanceof Error ? e.message : String(e);

      return {
        error: errorMessage,
        currentUrl: 'unknown',
        isOnNewPasswordPage: false,
        formElements: {
          resetTokenInputVisible: false,
          passwordInputVisible: false,
          confirmPasswordInputVisible: false,
          saveButtonVisible: false,
        },
        navigationElements: {
          signUpLinkVisible: false,
        },
        uiElements: {
          welcomeTextVisible: false,
          welcomeImageVisible: false,
          logoImageVisible: false,
          newPasswordTitleVisible: false,
        },
      };
    }
  }
}
