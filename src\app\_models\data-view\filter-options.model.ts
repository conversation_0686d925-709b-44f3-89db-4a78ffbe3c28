export interface FilterValue {
  filter_column: string;
  filter_operator: string;
  filter_value: string;
}

export const OperatorOptions = {
  numeric: ['eq', 'neq', 'gt', 'gte', 'lt', 'lte', 'between'],
  string: ['eq', 'neq', 'contains', 'startswith', 'endswith', 'regex'],
  boolean: ['eq', 'neq'],
  datetime: [
    'eq',
    'neq',
    'before',
    'after',
    'on_or_before',
    'on_or_after',
    'between',
  ],
};

export const OperatorOptionsNames = {
  eq: 'Equals',
  neq: 'Not Equals',
  gt: 'Greater Than',
  gte: 'Greater Than or Equals',
  lt: 'Less Than',
  lte: 'Less Than or Equals',
  between: 'Between',
  contains: 'Contains',
  startswith: 'Starts With',
  endswith: 'Ends With',
  regex: 'Regex',
  before: 'Before',
  after: 'After',
  on_or_before: 'On Or Before',
  on_or_after: 'On Or After',
};
