import { CommonModule } from '@angular/common';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { Component, OnInit } from '@angular/core';
import {
  MatButtonToggle,
  MatButtonToggleGroup,
  MatButtonToggleModule,
} from '@angular/material/button-toggle';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { StripeService } from '../services/stripe.service';
import { FormsModule } from '@angular/forms';
import { Plan } from '../../../_models/plan.model';
import { SharedModule } from '../../../shared/shared.module';
import { ToastrService } from 'ngx-toastr';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-upgrade-plan-modal',
  imports: [
    CommonModule,
    MatIcon,
    MatButtonToggle,
    MatButtonToggleGroup,
    MatDialogModule,
    MatCardModule,
    MatButtonToggleModule,
    MatListModule,
    MatIconModule,
    FormsModule,
    SharedModule,
    MatButtonModule,
  ],
  templateUrl: './upgrade-plan-modal.component.html',
  styleUrl: './upgrade-plan-modal.component.css',
})
export class UpgradePlanModalComponent implements OnInit {
  // to show/hide loader
  loading = false;

  // List of purchase plans.
  plans: Plan[] = [];

  constructor(
    private dialogRef: MatDialogRef<UpgradePlanModalComponent>,
    private stripeService: StripeService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.getAllPurchasePlan();
  }

  /**
   * To fetch the purchase plans
   */
  async getAllPurchasePlan(): Promise<void> {
    this.loading = true;
    try {
      const response = await this.stripeService.getAllPurchasePlan(); // Fetch plans from the service
      if (response) {
        this.plans = response.data.map(plan => {
          plan.yearly_discount = Math.floor(Number(plan.yearly_discount));
          plan.selectedBillingCycle = 'monthly'; // Add selectedBillingCycle field

          return plan;
        });
      } else {
        console.error('Invalid plans data:', response);
        this.plans = [];
      }
      this.loading = false;
    } catch (error) {
      this.loading = false;
      this.toastrService.error(
        'An error occured while fetching purchase plans. Please try again later.',
      );
      console.error('HTTP error fetching purchase plans:', error);
    }
  }

  /**
   * Close dialog
   */
  closeModal() {
    this.dialogRef.close();
  }

  /**
   * Buy a buy with stripe.
   * @param plan - plan .
   */
  async buyPlan(plan: Plan): Promise<void> {
    this.loading = true;
    try {
      if (plan.selectedBillingCycle && plan.name) {
        await this.stripeService.purchasePlan(
          plan.name,
          plan.selectedBillingCycle,
        );
      }
      this.loading = false;
    } catch (error) {
      this.loading = false;
      this.toastrService.error(
        'An error occured while redirecting to checkout. Please try again later.',
      );
      console.error('HTTP error redirecting to checkout:', error);
    }
  }
}
