<app-loader [loading]="isComponentLoaded"></app-loader>
<div class="flex flex-col h-full p-1">
  <!-- Header -->
  <div class="my-component-light flex justify-between items-center">
    <div class="flex flex-col max-w-[450px]">
      <div
        #titleElement
        class="font-normal ellipsis"
        [matTooltip]="(projectDetails$ | async)?.data?.title"
        matTooltipPosition="above"
        [matTooltipDisabled]="!showTooltip">
        {{ (projectDetails$ | async)?.data?.title }}
      </div>
      <h2 class="text-normal text-3xl inline-flex">Visual Data Insights</h2>
    </div>

    <!--  <div class="flex">-->
    <!--    <label>x<input type="number" [(ngModel)]="x"></label>-->
    <!--    <label>y<input type="number" [(ngModel)]="y"></label>-->
    <!--    <label>cols<input type="number" [(ngModel)]="cols"></label>-->
    <!--    <label>rows<input type="number" [(ngModel)]="rows"></label>-->
    <!--    <label>front<input type="checkbox" [(ngModel)]="front"></label>-->
    <!--    <button (click)="addToGrid(x, y, cols, rows, front)">add</button>-->
    <!--    <button (click)="loadCustomPlots(graphsDataAll.length)">load more</button>-->
    <!--  </div>-->
    <div class="flex items-center">
      <app-search-header
        (searchPerformed)="searchFilters($event)"></app-search-header>
      <button
        mat-icon-button
        class="mr-5 ml-5"
        (click)="getFavPlots(togglefavplot)">
        <mat-icon *ngIf="!togglefavplot">star</mat-icon>
        <mat-icon *ngIf="togglefavplot">star_border</mat-icon>
      </button>

      <div class="flex gap-2 mt-1">
        <!-- <button
          mat-stroked-button
          class="whitespace-nowrap flex items-center h-12 border-gray-300"
          (click)="openPlotSettings()">
          <mat-icon fontSet="material-icons-outlined">settings</mat-icon>
          <span>Plot Settings</span>
        </button> -->

        <button
          mat-flat-button
          class="whitespace-nowrap flex items-center h-12"
          (click)="openAddForm()">
          <mat-icon>add</mat-icon>
          <span> {{ g_const.newPlot }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- nav bar -->
  <mat-paginator
    [hidePageSize]="true"
    (page)="changePage($event)"
    class="mt-1 rounded-t-lg overflow-hidden" />

  <!-- Grid -->
  <div class="overflow-y-auto scrollbar-hidden h-full flex-1 w-full" #container>
    <gridster class="rounded-b-lg" [options]="gridsterConfig()">
      @for (graph of graphsDataAll; track graph.id) {
        <gridster-item
          [item]="graph.display_layout ?? $any({})"
          (itemResize)="onItemResize(graph, $event)"
          (itemChange)="onItemChange(graph, $event)"
          class="rounded-lg drop-shadow-md border border-gray-200 dark:border-gray-500">
          <div [id]="'graph-' + $index">
            <div class="gridster-item-content">
              <div
                class="pb-2 bg-gray-50 dark:bg-[--md-sys-color-surface-variant] border-b">
                <div class="flex relative items-center w-full">
                  <div>
                    <button
                      class="relative material-icons-outlined mr-4 w-10 h-10 p-2 gap-2"
                      [matMenuTriggerFor]="menu">
                      more_vert

                      <!-- Custom badge (shows only when filter_active is true) -->
                      <span
                        *ngIf="graph.filter_active"
                        class="absolute top-0.5 right-3.5 bg-red-600 text-white text-xs w-2 h-2 rounded-full flex items-center justify-center"></span>
                    </button>

                    <mat-menu #menu="matMenu" class="bg-white dark:bg-gray-900">
                      <div class="">
                        <button
                          *ngIf="false"
                          mat-menu-item
                          class=""
                          (click)="openPlotEditPopup(graph, graph.id)">
                          <span class="flex items-center">
                            <mat-icon class="mr-2">edit</mat-icon> Edit
                            Plot</span
                          >
                        </button>

                        @if (graph.favorite === false) {
                          <button
                            mat-menu-item
                            (click)="setFavPlot(graph.id, true)">
                            <span class="flex items-center"
                              ><mat-icon class="mr-2">star_border</mat-icon>
                              Mark as Favorite</span
                            >
                          </button>
                        } @else {
                          <button
                            mat-menu-item
                            (click)="setFavPlot(graph.id, false)">
                            <span class="flex items-center"
                              ><mat-icon class="mr-2">star</mat-icon>
                              Remove as Favorite
                            </span>
                          </button>
                        }

                        <button
                          mat-menu-item
                          class=""
                          (click)="removePlot(graph.id)">
                          <span class="flex items-center"
                            ><mat-icon class="mr-2">delete</mat-icon> Remove
                            Plot</span
                          >
                        </button>
                        <button
                          mat-menu-item
                          class=""
                          (click)="
                            openFilterModal(
                              graph.id,
                              graph.filter_active,
                              graph.filter_instance_id ?? null,
                              graph.fileId
                            )
                          ">
                          <div
                            *ngIf="graph.filter_active"
                            class="size-2 rounded-full bg-red-600"></div>
                          <span class="flex items-center"
                            ><mat-icon class="mr-2">filter_list</mat-icon>

                            Filter Data</span
                          >
                        </button>
                        <!-- <button
                          mat-menu-item
                          class=""
                          (click)="openPlotSettings()">
                          <span class="flex"
                            ><mat-icon class="mr-2">settings</mat-icon> Plot
                            Settings</span
                          >
                        </button> -->
                      </div>
                    </mat-menu>
                  </div>

                  <!-- Card Title and Subtitle -->
                  <div class="mt-2 inline-grid">
                    <mat-card-title class="font-semibold -mb-1"
                      >{{ graph.name }}
                    </mat-card-title>
                    <mat-card-subtitle class="text-sm font-light text-gray-500"
                      >{{ graph.file_name }}
                    </mat-card-subtitle>
                  </div>
                  <div class="absolute right-0 pl-5">
                    <button
                      *ngIf="false"
                      mat-icon-button
                      class="mr-4"
                      (click)="openDescriptionModal(graph.id)">
                      <mat-icon class="text-btn-color">short_text</mat-icon>
                    </button>
                    <button
                      mat-icon-button
                      (click)="openFullscreen($index, graph)"
                      *ngIf="!isFullScreen">
                      <mat-icon>fullscreen</mat-icon>
                    </button>
                    <mat-icon
                      class="screen"
                      (click)="closeFullscreen()"
                      *ngIf="isFullScreen"
                      >fullscreen_exit
                    </mat-icon>
                  </div>
                </div>
              </div>
              <div>
                <plotly-plot
                  [data]="graph.json_data.data"
                  [layout]="graph.json_data.layout"
                  [useResizeHandler]="true"
                  [style]="{
                    position: 'relative',
                    width: '100%',
                    height: '100%',
                  }"
                  class="bg-white dark:bg-gray-900">
                </plotly-plot>
              </div>
            </div>
            <div class="relative pr-4">
              <button
                class="absolute bottom-1 right-1 drag-handler"
                mat-icon-button>
                <mat-icon class="ml-auto rotate-90 cursor-move"
                  >open_with
                </mat-icon>
              </button>
            </div>
          </div>
        </gridster-item>
      }
    </gridster>
  </div>
</div>

@if (plotPopupMode() === 'new') {
  <app-plot-data
    *ngIf="plotPopupOpen()"
    (cancelEvent)="closeNewPlot()"
    (saveEvent)="saveNewPlot()">
  </app-plot-data>
} @else if (plotPopupMode() === 'edit' && selectedPlotId) {
  <app-edit-plot-data
    *ngIf="plotPopupOpen()"
    (cancelEvent)="closeNewPlot()"
    (saveEvent)="saveNewPlot()"
    [selectedPlotId]="selectedPlotId"
    [selectedPlotTypeName]="selectedPlotTypeName">
  </app-edit-plot-data>
}

@if (projectDetails$ | async; as projectDetails) {
  @if (plotSettingsPopup()) {
    <app-plot-data-settings
      [projectDetails]="projectDetails.data"
      (cancelEvent)="closePlotSettings()"></app-plot-data-settings>
  }
}
@if (fileID !== null) {
  <app-filter-modal
    [fileID]="fileID"
    [plotId]="selectedGraphId"
    [filter_active]="filter_active"
    [filter_instance_id]="filter_instance_id"
    (closeModal)="closeFilterModal()"
    *ngIf="isFilterDataModalOpen"></app-filter-modal>
}

<app-plot-description
  [plotId]="selectedGraphId"
  (cancelEvent)="closeDesPlot()"
  (closeModal)="closeFilterModal()"
  *ngIf="plotDescriptionModal"></app-plot-description>
