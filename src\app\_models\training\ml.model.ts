export interface MlQuestion {
  id: number;
  options: string[];
  question: string;
  value: string[] | string;
}

export interface MlStepValue {
  active: boolean;
  done: boolean;
  name: string;
  step: number;
}

export interface MlRecommendationProgress {
  analysis_goal: MlStepValue;
  data_type: MlStepValue;
  labels: MlStepValue;
  feature_amount: MlStepValue;
  dataset_size: MlStepValue;
  recommendations: MlStepValue;
}
export interface RecommendedData {
  recommendations: string[];
  explanations: string[];
}

export interface MlQuestionData {
  data: RecommendedData | MlQuestion;
  mlRecommendationProgress: MlRecommendationProgress;
}
export interface MlInfo {
  explanation: string;
  label: string;
}

export interface ModelByNameValue {
  category: string;
  created_at: string | Date;
  description: string;
  goals: string;
  id: number;
  name: string;
  nature_of_data: string;
  options: MlModelOption[];
  updated_at: string | Date;
  machine_learning_tasks: string;
  suitable_for: number;
}
export interface MlModelOption {
  id: number;
  name: string;
  value?: string;
  data_type: string;
  description?: string;
  options: unknown[];
  ml_model: number;
}

export interface TrainModelResponse {
  training_result_id: 40;
  message: string;
  ml_model: string;
}

export interface ProjectModelResponse {
  id: number;
  name: string;
  machine_learning_tasks: string;
  updated_at: string | Date;
  basetrainingresults: BaseTrainingRes[];
  trainingResultId?: number;
}

export interface BaseTrainingRes {
  id: number;
  status: string;
  training_date: string | Date;
  loss?: number;
  score?: number;
  mse?: number;
  rmse?: number;
  mae?: number;
  r_squared?: number;
  explained_variance?: number;
  resourcetype: string;
}

export interface TrainingResultResponse {
  id: number;
  ml_model: MlModel;
  training_date: string;
  training_duration: string;
  loss: number;
  score: number;
  status: string;
  polymorphic_ctype: number;
  user: number;
  project: number;
  s3_object_metadata: number;
}

export interface MlModel {
  id: number;
  name: string;
  machine_learning_tasks: string;
  updated_at: string;
  basetrainingresults: number[];
  options: MlModelOption[];
  file_key: string;
  file_name: string;
}

export interface PerformanceEvaluationResponse {
  data: Plotly.Data[];
  example_data_rows_for_each_matrixresult: Record<string, string> | null;
  layout: Plotly.Layout;
}

export interface ModelSummaryData {
  loss: number;
  performance: string;
  machine_learning_tasks: string;
  top_drivers: [string, number][][];
}

export interface RegressionData extends DefaultModelData {
  prediction_interval: PredictionInterval;
}

export interface ClassificationData extends DefaultModelData {
  class_probabilities: number[][];
  class_labels: number[];
}

export interface DefaultModelData {
  predictions: number[];
  mltask: string;
}

export interface PredictionInterval {
  lower_bound: number[];
  upper_bound: number[];
  confidence_level: number;
}
