<app-training-header></app-training-header>

<main>
  <section>
    <mat-nav-list>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 1001"
        (click)="openAddForm(1001)"
        >Item 1</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 2"
        (click)="openAddForm(2)"
        >Item 2</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 3"
        (click)="openAddForm(3)"
        >Item 3</mat-list-item
      >
    </mat-nav-list>
  </section>
  <section>
    <mat-tab-group
      animationDuration="0ms"
      (selectedTabChange)="onTabChange($event)"
      [selectedIndex]="activeTabIndex">
      <mat-tab label="Analysis Goal">
        <div class="mt-6">
          <h5 class="m-0">Analysis Goal</h5>
          <p class="m-0">Choose your main goals of the analysis</p>
          <div class="flex flex-row items-center space-x-2 mt-4">
            <p class="mt-3 font-normal font-sans">Task Overview</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <div class="flex flex-row items-center space-x-2">
            <p class="mt-3 font-normal font-sans">Task Overview</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
        </div>
      </mat-tab>
      <mat-tab label="Target Selection">
        <div class="mt-6">
          <h5 class="m-0">Target Outputs</h5>
          <p>
            Based on the chosen analysis goal Object Detection, the model will
            predict
          </p>
          <p>
            The target output includes class labels and bounding box
            coordinates.
          </p>
          <p>These are the detected classes</p>
          <div class="flex flex-row justify-between">
            <button
              class="py-[6px] px-[80px] border-2 rounded-xl border-gray-400">
              Nucleous
            </button>
            <button
              class="py-[6px] px-[64px] border-2 rounded-xl border-gray-400">
              Mitochondrium
            </button>
            <button
              class="py-[6px] px-[80px] border-2 rounded-xl border-gray-400">
              Mitochondrium
            </button>
          </div>
          <p class="my-5">
            This is the detected bounding box format with an extracted example
          </p>
          <div class="flex flex-row">
            <div class="flex flex-row justify-between w-[392px]">
              <button
                class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                18
              </button>
              <button
                class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                1000
              </button>
              <button
                class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                18
              </button>
              <button
                class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                1000
              </button>
            </div>

            <div
              class="w-[380px] px-3 border-1 bg-white rounded-xl ml-auto flex flex-row justify-between">
              <p class="my-[14px]">xmin, ymin, xmax, ymax</p>
              <button class="ml-auto">
                <mat-icon>chevron_right</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </mat-tab>
      <mat-tab label="Processing & Augmentation">
        <h5 class="mt-6">Processing & Augmentation</h5>
        <main>
          <p>Preprocessing</p>
          <div class="flex flex-row justify-between">
            <button class="custom-button">Nucleous</button>
            <button class="custom-button">Custom Sauce</button>
            <button class="custom-button">Mitochondrium</button>
            <button class="custom-button">Custom D</button>
            <button class="custom-button">Custom E</button>
          </div>
        </main>
        <main class="mt-4">
          <p>Preprocessing</p>
          <div class="flex flex-row justify-between">
            <button class="custom-button">Nucleous</button>
            <button class="custom-button">Custom Sauce</button>
            <button class="custom-button">Mitochondrium</button>
            <button class="custom-button">Custom D</button>
            <button class="custom-button">Custom E</button>
          </div>
        </main>

        <main class="mt-4">
          <p>Dataset Split</p>
          <div class="flex flex-row items-center justify-between">
            <p class="m-0">Train</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              1000
            </button>
            <p class="m-0">Test</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              18
            </button>
            <p class="m-0">Validation</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              1000
            </button>
          </div>
        </main>
      </mat-tab>
      <mat-tab label="Recommendations">
        <div class="mt-6">
          <h5 class="m-0 font-sans">Recommendations</h5>
          <p>Select your recommended Machine Learning Models for the task</p>
          <div class="flex flex-row items-center space-x-2 mt-4">
            <p class="mt-3 font-sans">YOLO (You Only Look Once)</p>

            <mat-icon color="material-symbols-outlined">info</mat-icon>
          </div>
          <div class="flex flex-row items-center space-x-2">
            <p class="mt-3 font-sans">Faster R-CNN</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <div class="flex flex-row items-center space-x-2">
            <p class="mt-3 font-normal font-sans">
              SSD (Single Shot Multibox Detector)
            </p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>
</main>
