import { Component, TemplateRef, ViewChild } from '@angular/core';

interface ProjectModalContext {
  title: string;
  description: string;
  // Any other properties the template may need
}

@Component({
  selector: 'app-component-header',
  templateUrl: './component-header.component.html',
  styleUrl: './component-header.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class ComponentHeaderComponent {
  @ViewChild('projectModal') projectModal!: TemplateRef<ProjectModalContext>;
}
