<form class="hidden lg:block" (ngSubmit)="performSearch()">
  <div
    class="flex items-center border px-4 py-2 shadow-sm mat-search w-auto max-w-[380px] lg:w-[380px] h-12 rounded-full relative">
    <input
      placeholder="Hinted search text"
      class="outline-none bg-transparent flex-1 max-w-[260px] text-sm"
      [(ngModel)]="searchOptions.title"
      name="searchTitle" />
    <button mat-icon-button type="submit">
      <mat-icon class="">search</mat-icon>
    </button>

    <button
      mat-icon-button
      type="button"
      *ngIf="filter"
      (click)="toggleDateRangePicker()"
      class="relative">
      <mat-icon class="">filter_list</mat-icon>
      <span
        *ngIf="isFilterActive"
        class="absolute top-2.5 right-0 bg-red-500 h-2 w-2 rounded-full"></span>
    </button>

    <button
      type="button"
      class="p-0 m-0 mt-1"
      *ngIf="searchOptions.title || isFilterActive"
      mat-icon-button
      (click)="resetFilters()">
      <mat-icon class="m-0">close</mat-icon>
    </button>
  </div>

  <div
    *ngIf="isDateRangePickerVisible"
    class="absolute shadow-lg rounded p-4 w-[311px] h-[250px] z-50 mat-search">
    <p class="text-sm font-medium text-gray-700">Select date</p>
    <p class="text-3xl font-normal">Enter dates</p>
    <div class="flex items-center space-x-4 mt-2">
      <mat-form-field class="bg-white">
        <mat-label>Date</mat-label>
        <input
          matInput
          [matDatepicker]="startPicker"
          [(ngModel)]="startDate"
          name="startDate"
          placeholder="mm/dd/yyyy"
          [max]="endDate" />
        <mat-datepicker-toggle
          matSuffix
          [for]="startPicker"></mat-datepicker-toggle>
        <mat-datepicker #startPicker></mat-datepicker>
      </mat-form-field>

      <mat-form-field>
        <mat-label>End date</mat-label>
        <input
          matInput
          [matDatepicker]="endPicker"
          [(ngModel)]="endDate"
          name="endDate"
          placeholder="mm/dd/yyyy"
          [min]="startDate" />
        <mat-datepicker-toggle
          matSuffix
          [for]="endPicker"></mat-datepicker-toggle>
        <mat-datepicker #endPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div class="flex justify-end space-x-2 mt-4">
      <button mat-button (click)="toggleDateRangePicker()">Cancel</button>
      <button mat-button (click)="confirmDateRange()">OK</button>
    </div>
  </div>
</form>
