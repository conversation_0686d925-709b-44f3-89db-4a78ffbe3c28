import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UsageInterface } from '../models/settings.model';
import { environment } from '../../../env/env';

const api = {
  USAGE_API: 'subscriptions/usage/',
};

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  constructor(private http: HttpClient) {}

  getSubscriptionUsage(): Observable<UsageInterface> {
    return this.http.get<UsageInterface>(
      `${environment.apiUrl}${api.USAGE_API}`,
    );
  }
}
