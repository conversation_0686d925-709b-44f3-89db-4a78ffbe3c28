export interface SearchOptions {
  searchTerm?: string;
  title?: string;
  desc?: string;
  minDate?: string;
  maxDate?: string;
}

export interface ResponseData<T> {
  data: T;
  message?: string;
  status?: string;
}

export interface StandardPaginationResult {
  count: number;
  next: string | null;
  previous: string | null;
}

export interface PaginationResult<T> {
  status: string;
  message: string;
  data: T;
  error: string;
  pagination: StandardPaginationResult;
}

//TODO GET /displayFile/${file_id}/
export interface TableDataWithPagination {
  status: string;
  message: string;
  data: {
    data: Record<string, unknown>[];
    columns: string[];
  };
  error: string;
  pagination: StandardPaginationResult;
}

//TODO DELETE projects/${id}/
//TODO POST logout/
//TODO POST register/
//TODO POST delete/
//TODO DELETE /projects/${projectId}/
//TODO DELETE /files/delete/
//TODO PUT /ColumnInfo/${file_id}/
//TODO POST /SaveIDColumn/${fileId}/
export interface Message {
  message: string;
  status?: string;
}

export enum SidebarPages {
  dashboard = 'dashboard',
  dataView = 'data-view',
  dataVersion = 'data-version',
  exploreView = 'explore-view',
  training = 'training',
  results = 'results',
}
