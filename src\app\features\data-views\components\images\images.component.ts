import { isPlatformBrowser, Location } from '@angular/common';
import { Component, Inject, Input, OnInit, PLATFORM_ID } from '@angular/core';
import { DataviewService } from '../../services/data-view.service';
import { ToastrService } from 'ngx-toastr';
import { FileData } from '../../models/data-view.model';

@Component({
  selector: 'app-images',
  templateUrl: './images.component.html',
  styleUrls: ['./images.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class ImagesComponent implements OnInit {
  @Input() imageData: FileData | null = null;
  @Input() fileID: number | null = null;

  safeFileUrl = '';
  fileName = '';
  fileSize = '';
  resolution = '';
  activeSection = 'data';
  currentPage = 1;
  pageSize = 2;
  isStatisticsLoading = false;

  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    private location: Location,
    private dataViewService: DataviewService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (this.imageData) {
        this.initializeImageData(this.imageData);
      } else {
        console.warn('No image data available');
      }
    }
  }

  initializeImageData(imageData: FileData): void {
    this.safeFileUrl = imageData.json_data?.layout.images[0].source ?? '';
    this.fileName = imageData.metadata.filename;
    this.fileSize = imageData.metadata.file_size;
    this.resolution = imageData.metadata.resolution;
  }

  downloadFile(response: Blob, fileName: string): void {
    if (isPlatformBrowser(this.platformId)) {
      const url = URL.createObjectURL(response);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);
    }
  }

  downloadData(): void {
    const downloadMethod =
      this.activeSection === 'data'
        ? this.dataViewService.DownloadFileData(this.fileID)
        : this.dataViewService.DownloadImageStatisticsData(this.fileID);

    downloadMethod.subscribe(
      (response: Blob) => {
        const fileName =
          this.activeSection === 'data'
            ? `image_${this.fileID}.png`
            : `file_Statistics_${this.fileID}.zip`;
        this.downloadFile(response, fileName);
      },
      error => {
        console.error('Download failed:', error);
      },
    );
  }

  handleRandomImageData(): void {
    this.dataViewService
      .randomFileData(this.fileID, this.currentPage, this.pageSize, true)
      .subscribe(
        response => {
          this.toastrService.success(`${response.message}`);
          this.safeFileUrl =
            response.data.json_data?.layout.images[0].source ?? '';
        },
        error => {
          console.error('Download failed:', error);
        },
      );
  }

  goBack(): void {
    this.location.back();
  }

  capitalizeFirstLetter(value: string): string {
    return value ? value.charAt(0).toUpperCase() + value.slice(1) : '';
  }

  changeSection(section: string): void {
    this.activeSection = section;
  }
}
