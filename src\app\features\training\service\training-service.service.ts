/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../env/env';
import { Observable } from 'rxjs';
import {
  ColumnInfoData,
  CreateMLmodelPayload,
  CreateMlmodelResponse,
  MachineLearningModelInfoInterface,
  MLTrainingProjectResponse,
  StartMlTrainingResponse,
  TrainingOverviewInterface,
} from '../models/trainings.model';

@Injectable({
  providedIn: 'root',
})
export class TrainingService {
  constructor(private http: HttpClient) {}
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  getMlModels(
    _name?: string,
    _machine_learning_tasks?: string,
  ): Observable<MachineLearningModelInfoInterface> {
    return this.http.get<MachineLearningModelInfoInterface>(
      `${environment.apiUrl}mlmodels/ml-models/`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  getTargetFeatures(fileId: string): Observable<ColumnInfoData> {
    return this.http.get<ColumnInfoData>(
      `${environment.apiUrl}mlmodels/files/${fileId}/features-targets/Classification`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  createMlTraining(
    file_id: number,
    payload: CreateMLmodelPayload,
  ): Observable<CreateMlmodelResponse> {
    return this.http.post<CreateMlmodelResponse>(
      `${environment.apiUrl}mlmodels/mltraining/${file_id}/create`,
      {
        machine_learning_model: payload.machine_learning_model,
        name: payload.name,
        selected_target: payload.selected_target,
        selected_features: payload.selected_features,
        test_size: payload.test_size,
      },
      { headers: this.getHeaders() },
    );
  }

  getMlTrainingProjects(
    project_id: number,
    pageSize: number,
  ): Observable<MLTrainingProjectResponse> {
    return this.http.get<MLTrainingProjectResponse>(
      `${environment.apiUrl}mlmodels/projects/${project_id}/trainings/?limit=${pageSize}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  startMlTraining(model_id: number): Observable<StartMlTrainingResponse> {
    return this.http.post<StartMlTrainingResponse>(
      `${environment.apiUrl}mlmodels/mltraining/${model_id}/train`,
      {},
      { headers: this.getHeaders() },
    );
  }

  getMlTrainingInfo(id: number): Observable<TrainingOverviewInterface> {
    return this.http.get<TrainingOverviewInterface>(
      `${environment.apiUrl}mlmodels/mltraining/${id}/`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getFileList(project_id: number): Observable<any> {
    const headers = this.getHeaders();
    return this.http.get<any>( // eslint-disable-line @typescript-eslint/no-explicit-any
      `${environment.apiUrl}projects/projects/${project_id}/files/filter/?search_files=true&file_type=non_image`,
      { headers },
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getTrainingsResults(id: number): Observable<any> {
    const headers = this.getHeaders();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.get<any>(
      `${environment.apiUrl}mlmodels/mltraining/${id}/results`,
      { headers },
    );
  }

  startNewInference(trainingId: number) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.post<any>(
      `${environment.apiUrl}mlmodels/mlinference/${trainingId}/`,
      {},
      { headers: this.getHeaders() },
    );
  }
  getMlInferences(trainingId: number) {
    const headers = this.getHeaders();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.get<any>(
      `${environment.apiUrl}mlmodels/mlinference/${trainingId}/`,
      { headers },
    );
  }

  checkRunningInference(trainingId: number) {
    const headers = this.getHeaders();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.get<any>(
      `${environment.apiUrl}mlmodels/mlinference/check-running/${trainingId}/`,
      { headers },
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getTraningPredictions(payload: any, trainingId: number) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.post<any>(
      `${environment.apiUrl}mlmodels/mlinference/${trainingId}/prediction`,
      { features: payload.features },
      { headers: this.getHeaders() },
    );
  }
  stopMlInference(ml_inference_id: number) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.delete<any>(
      `${environment.apiUrl}mlmodels/mlinference/${ml_inference_id}/instance`,
    );
  }

  stopTrainModel(ml_training_id: number) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.http.delete<any>(
      `${environment.apiUrl}mlmodels/mltraining/${ml_training_id}/train`,
    );
  }
}
