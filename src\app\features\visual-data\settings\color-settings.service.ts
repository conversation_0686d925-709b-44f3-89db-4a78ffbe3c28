import { Injectable, Signal, signal } from '@angular/core';
import { ColorContainer } from '../../../_models/visual-data/visual-data.model';

@Injectable({
  providedIn: 'root',
})
export class ColorSettingsService {
  currentlySelectedColorTileId = signal<ColorContainer | null>(null);
  private _currentlySelectedColorInPicker = signal<string | null>(null);

  get currentlySelectedColorInPicker(): Signal<string> {
    const color = this._currentlySelectedColorInPicker();
    if (color === null) {
      return signal('#FFFFFF').asReadonly();
    } else {
      return signal(color).asReadonly();
    }
  }

  setCurrentlySelectedColorInPicker(value: string | null) {
    this._currentlySelectedColorInPicker.set(value);
  }
}
