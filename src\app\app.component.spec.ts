import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';

describe('AppComponent', () => {
  let fixture: ComponentFixture<AppComponent>;
  let app: AppComponent;
  let compiled: HTMLElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AppComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    app = fixture.componentInstance;
    compiled = fixture.nativeElement as HTMLElement;
  });

  it('should create the app instance', () => {
    expect(app).toBeTruthy();
  });

  it(`should have the correct title 'AICU'`, () => {
    expect(app.title).toEqual('AICU');
  });

  it('should render the title in the h1 element', () => {
    fixture.detectChanges();
    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, AICU');
  });
});
