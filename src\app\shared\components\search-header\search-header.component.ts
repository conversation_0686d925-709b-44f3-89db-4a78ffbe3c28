import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SearchOptions } from '../../../_models/common.model';

@Component({
  selector: 'app-search-header',
  templateUrl: './search-header.component.html',
  styleUrls: ['./search-header.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class SearchHeaderComponent {
  @Output() searchPerformed = new EventEmitter<SearchOptions>();
  @Input() filter = true;

  isDateRangePickerVisible = false;

  searchOptions: SearchOptions = {};
  startDate: Date | null = null;
  endDate: Date | null = null;
  isFilterActive = false;

  onDateChange() {
    if (this.endDate && this.startDate && this.endDate < this.startDate) {
      this.endDate = null; // Reset the end date if it's earlier than the start date
    }
  }
  performSearch(): void {
    if (this.startDate && this.endDate) {
      this.searchOptions.minDate = this.startDate.toISOString();
      this.searchOptions.maxDate = this.endDate.toISOString();
    }
    this.searchPerformed.emit(this.searchOptions);
  }

  toggleDateRangePicker(): void {
    this.isDateRangePickerVisible = !this.isDateRangePickerVisible;
  }

  confirmDateRange(): void {
    if (this.startDate && this.endDate) {
      this.searchOptions.minDate = this.startDate.toISOString();
      this.searchOptions.maxDate = this.endDate.toISOString();
    }
    this.isDateRangePickerVisible = false;
    this.isFilterActive = !!this.startDate || !!this.endDate;
    this.performSearch();
  }

  resetFilters(): void {
    this.searchOptions = {};
    this.startDate = null;
    this.endDate = null;
    this.isDateRangePickerVisible = false;
    this.performSearch();
    this.isFilterActive = false;
  }
}
