import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../env/env';
import { Message } from '../../../_models/common.model';
import {
  AccountActivationResponse,
  ActivateUserParams,
  ForgetPasswordPayload,
  LoginPayload,
  LoginSuccessful,
  NewPasswordPayload,
  NewPasswordSuccessful,
  RecoverSuccessful,
  RegisterPayload,
} from '../models/user';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private url = environment.apiUrl + 'users/';
  constructor(private http: HttpClient) {}

  login(user: LoginPayload): Observable<LoginSuccessful> {
    return this.http.post<LoginSuccessful>(`${this.url}login/`, user);
  }

  logout(): Observable<Message> {
    const token = localStorage.getItem('access_token');
    const headers = new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
    return this.http.post<Message>(`${this.url}logout/`, {}, { headers });
  }

  register(user: RegisterPayload): Observable<Message> {
    return this.http.post<Message>(`${this.url}register/`, user);
  }

  forgetPassword(user: ForgetPasswordPayload): Observable<RecoverSuccessful> {
    return this.http.post<RecoverSuccessful>(
      `${this.url}forgot-password/`,
      user,
    );
  }

  activateUser(
    user: ActivateUserParams,
  ): Observable<AccountActivationResponse> {
    return this.http.get<AccountActivationResponse>(
      `${this.url}activate/${user.uid}/${user.token}/`,
    );
  }

  resendUserActivation(email: string): Observable<AccountActivationResponse> {
    return this.http.post<AccountActivationResponse>(
      `${this.url}resend-activation-email/`,
      {
        email: email,
      },
    );
  }
  newPassword(payload: NewPasswordPayload): Observable<NewPasswordSuccessful> {
    return this.http.post<NewPasswordSuccessful>(`${this.url}reset-password/`, {
      password_reset_token: payload.password_reset_token,
      new_password: payload.password,
      confirm_new_password: payload.password,
    });
  }

  deleteUser(): Observable<Message> {
    const token = localStorage.getItem('access_token');
    const headers = new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
    return this.http.delete<Message>(`${this.url}delete/`, {
      headers: headers,
    });
  }
}
