import {
  Component,
  computed,
  inject,
  input,
  OnInit,
  output,
} from '@angular/core';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { PlotStyleOption } from '../../../../../_models/visual-data/visual-data.model';
import { NgForOf } from '@angular/common';
import { MatFormField, MatOption, MatSelect } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatLabel } from '@angular/material/form-field';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { PlotDataSettingsService } from '../../plot-data-settings.service';

@Component({
  selector: 'app-chart-content-toolbar',
  imports: [
    MatTab,
    MatTabGroup,
    NgForOf,
    MatSelect,
    MatOption,
    MatFormField,
    MatInput,
    MatLabel,
    MatSlideToggle,
    ReactiveFormsModule,
  ],
  templateUrl: './chart-content-toolbar.component.html',
  styleUrl: './chart-content-toolbar.component.css',
})
export class ChartContentToolbarComponent implements OnInit {
  chartContent = input.required<PlotStyleOption | null>();
  plotDataSettingsService = inject(PlotDataSettingsService);
  chartContentChanged = output<FormGroup>();
  form = computed(() =>
    this.plotDataSettingsService.getFormGroups(this.chartContent()),
  );

  ngOnInit() {
    this.form().valueChanges.subscribe({
      next: value => {
        console.log(value);
        this.chartContentChanged.emit(this.form());
      },
    });
  }
}
