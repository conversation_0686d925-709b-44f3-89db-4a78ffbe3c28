import { Component, Input } from '@angular/core';
import { PlotDataUI } from '../../models/plot.model';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { PlotCardHeaderComponent } from '../plot-card-header/plot-card-header.component';

@Component({
  selector: 'app-plot-card',
  imports: [
    PlotlyViaCDNModule,
    PlotCardHeaderComponent,
    PlotCardHeaderComponent,
  ],
  templateUrl: './plot-card.component.html',
  styleUrl: './plot-card.component.css',
})
export class PlotCardComponent {
  @Input({ required: true }) plotData!: PlotDataUI;
}
