{"status": "success", "message": "Data retrieved successfully.", "data": {"plot": {"data": [{"boxmean": false, "line": {"color": "#fcde9c"}, "marker": {"color": "#fcde9c"}, "name": "Bilirubin", "y": [14.5, 1.1, 1.4, 1.8, 3.4, 0.8, 1.0, 0.3, 3.2, 12.6, 1.4, 3.6, 0.7, 0.8, 0.8, 0.7, 2.7, 11.4, 0.7, 5.1, 0.6, 3.4, 17.4, 2.1, 0.7, 5.2, 21.6, 17.2, 0.7, 3.6, 4.7, 1.8, 0.8, 0.8, 1.2, 0.3, 7.1, 3.3, 0.7, 1.3, 6.8, 2.1, 1.1, 3.3, 0.6, 5.7, 0.5, 1.9, 0.8, 1.1, 0.8, 6.0, 2.6, 1.3, 1.8, 1.1, 2.3, 0.7, 0.8, 0.9, 0.6, 1.3, 22.5, 2.1, 1.2, 1.4, 1.1, 0.7, 20.0, 0.6, 1.2, 0.5, 0.7, 8.4, 17.1, 12.2, 6.6, 6.3, 0.8, 7.2, 14.4, 4.5, 1.3, 0.4, 2.1, 5.0, 1.1, 0.6, 2.0, 1.6, 5.0, 1.4, 1.3, 3.2, 17.4, 1.0, 2.0, 1.0, 1.8, 2.3, 0.9, 0.9, 2.5, 1.1, 1.1, 2.1, 0.6, 0.4, 0.5, 1.9, 5.5, 2.0, 6.7, 3.2, 0.7, 3.0, 6.5, 3.5, 0.6, 3.5, 1.3, 0.6, 5.1, 0.6, 1.3, 1.2, 0.5, 16.2, 0.9, 17.4, 2.8, 1.9, 1.5, 0.7, 0.4, 0.8, 1.1, 7.3, 1.1, 1.1, 0.9, 1.0, 2.9, 28.0, 0.7, 1.2, 1.2, 7.2, 3.0, 1.0, 0.9, 2.3, 0.5, 2.4, 0.6, 25.5, 0.6, 3.4, 2.5, 0.6, 2.3, 3.2, 0.3, 8.5, 4.0, 5.7, 0.9, 0.4, 1.3, 1.2, 0.5, 1.3, 3.0, 0.5, 0.8, 3.2, 0.9, 0.6, 1.8, 4.7, 1.4, 0.6, 0.5, 11.0, 0.8, 2.0, 14.0, 0.7, 1.3, 2.3, 24.5, 0.9, 10.8, 1.5, 3.7, 1.4, 0.6, 0.7, 2.1, 4.7, 0.6, 0.5, 0.5, 0.7, 2.5, 0.6, 0.6, 3.9, 0.7, 0.9, 1.3, 1.2, 0.5, 0.9, 5.9, 0.5, 11.4, 0.5, 1.6, 3.8, 0.9, 4.5, 14.1, 1.0, 0.7, 0.5, 2.3, 0.7, 4.5, 3.3, 3.4, 0.4, 0.9, 0.9, 13.0, 1.5, 1.6, 0.6, 0.8, 0.4, 4.4, 1.9, 8.0, 3.9, 0.6, 2.1, 6.1, 0.8, 1.3, 0.6, 0.5, 1.1, 7.1, 3.1, 0.7, 1.1, 0.5, 1.1, 3.1, 5.6, 3.2, 2.8, 1.1, 3.4, 3.5, 0.5, 6.6, 6.4, 3.6, 1.0, 1.0, 0.5, 2.2, 1.6, 2.2, 1.0, 1.0, 5.6, 0.5, 1.6, 17.9, 1.3, 1.1, 1.3, 0.8, 2.0, 6.4, 8.7, 4.0, 1.4, 3.2, 8.6, 8.5, 6.6, 2.4, 0.8, 1.2, 1.1, 2.4, 5.2, 1.0, 0.7, 1.0, 0.5, 2.9, 0.6, 0.8, 0.4, 0.4, 1.7, 2.0, 6.4, 0.7, 1.4, 0.7, 0.7, 0.8, 0.7, 5.0, 0.4, 1.3, 1.1, 0.6, 0.6, 1.8, 1.5, 1.2, 1.0, 0.7, 3.5, 3.1, 12.6, 2.8, 7.1, 0.6, 2.1, 1.8, 16.0, 0.6, 5.4, 9.0, 0.9, 11.1, 8.9, 0.5, 0.6, 3.4, 0.9, 1.4, 2.1, 15.0, 0.6, 1.3, 1.3, 1.6, 2.2, 3.0, 0.8, 0.8, 1.8, 5.5, 18.0, 0.6, 2.7, 0.9, 1.3, 1.1, 13.8, 4.4, 16.0, 7.3, 0.6, 0.7, 0.7, 1.7, 9.5, 2.2, 1.8, 3.3, 2.9, 1.7, 14.0, 0.8, 1.3, 0.7, 1.7, 13.6, 0.9, 0.7, 3.0, 1.2, 0.4, 0.7, 2.0, 1.4, 1.6, 0.5, 7.3, 8.1, 0.5, 4.2, 0.8, 2.5, 4.6, 1.0, 4.5, 1.1, 1.9, 0.7, 1.5, 0.6, 1.0, 0.7, 1.2, 0.9, 1.6, 0.8, 0.7], "type": "box", "quartilemethod": "linear"}], "layout": {"template": {"data": {"histogram2dcontour": [{"type": "histogram2dcontour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "choropleth": [{"type": "choropleth", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "histogram2d": [{"type": "histogram2d", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "heatmap": [{"type": "heatmap", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "heatmapgl": [{"type": "heatmapgl", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "contourcarpet": [{"type": "contourcarpet", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "contour": [{"type": "contour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "surface": [{"type": "surface", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "mesh3d": [{"type": "mesh3d", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "parcoords": [{"type": "parcoords", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolargl": [{"type": "scatterpolargl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "scattergeo": [{"type": "scattergeo", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolar": [{"type": "scatterpolar", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "scattergl": [{"type": "scattergl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatter3d": [{"type": "scatter3d", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermapbox": [{"type": "scattermapbox", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterternary": [{"type": "scatterternary", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattercarpet": [{"type": "scattercarpet", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "pie": [{"automargin": true, "type": "pie"}]}, "layout": {"autotypenumbers": "strict", "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "hovermode": "closest", "hoverlabel": {"align": "left"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"bgcolor": "#E5ECF6", "angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "ternary": {"bgcolor": "#E5ECF6", "aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]]}, "xaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "yaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "geo": {"bgcolor": "white", "landcolor": "#E5ECF6", "subunitcolor": "white", "showland": true, "showlakes": true, "lakecolor": "white"}, "title": {"x": 0.05}, "mapbox": {"style": "light"}}}, "plot_bgcolor": "#F8F8F8", "polar": {"radialaxis": {"gridcolor": "gray"}, "angularaxis": {"gridcolor": "gray"}}, "showlegend": true}}, "plot_style_id": 397, "plot_id": 72, "plot_title": null}, "errors": null, "pagination": null}