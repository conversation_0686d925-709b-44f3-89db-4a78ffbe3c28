import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { DataviewService } from '../../services/data-view.service';
import { ToastrService } from 'ngx-toastr';
import { Plan, StandardPlan } from '../../../../_models/plan.model';
import { StorageAddonInterface } from '../../models/data-view.model';

@Component({
  selector: 'app-buy-plan-modal',
  templateUrl: './buy-plan-modal.component.html',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
  styleUrl: './buy-plan-modal.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BuyPlanModalComponent implements OnChanges {
  @Input() openModal = false;
  @Output() addOnStorage = new EventEmitter<{
    buyPlanModel: boolean;
    reOpenUploadModel?: boolean;
  }>();
  addOnObject: Plan[] = [];
  loading = false;
  constructor(
    private dataviewService: DataviewService,
    private cdr: ChangeDetectorRef,
    private toasterService: ToastrService,
  ) {}
  ngOnChanges(): void {
    if (this.openModal) {
      this.dataviewService.getSubscriptionPlans().subscribe({
        next: response => {
          this.mapResponse(response);
          this.cdr.detectChanges();
        },
        error: error => {
          this.toasterService.error(error.error);
          this.closeModal({ reOpenUploadModel: false });
        },
      });
    }
  }

  mapResponse(response: StandardPlan) {
    this.addOnObject = response.data.map((plan: Plan) => ({ ...plan }));
  }
  closeModal(modal?: { reOpenUploadModel?: boolean }) {
    this.openModal = false;
    this.addOnStorage.emit({
      buyPlanModel: this.openModal,
      reOpenUploadModel: modal?.reOpenUploadModel,
    });
    this.cdr.markForCheck();
  }
  callAddon(stripe_price_id: string) {
    this.loading = true;
    this.dataviewService.storageAddon(stripe_price_id).subscribe({
      next: (response: StorageAddonInterface) => {
        this.loading = false;
        this.toasterService.success(response.message);
        this.closeModal({ reOpenUploadModel: true });
        this.cdr.markForCheck();
      },
      error: error => {
        this.loading = false;
        this.closeModal({ reOpenUploadModel: false });
        this.toasterService.error(error.error);
        this.cdr.markForCheck();
      },
    });
  }
}
