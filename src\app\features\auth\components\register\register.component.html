<app-loader [loading]="loading"></app-loader>

<div class="flex flex-col h-screen md:flex-row">
  <div class="relative">
    <img
      src="../../../../assets/Welcome.png"
      alt="Side Image"
      class="w-full h-[30vh] md:h-[100vh] md:w-[40vw]" />

    <div
      class="absolute inset-y-0 left-0 flex items-center justify-center ml-4 sm:ml-8">
      <div class="text-white">
        <h1 class="text-white m-0 welcome">Welcome</h1>
        <p class="mt-3">Please Enter Your Details.</p>
      </div>
    </div>
  </div>
  <div
    class="flex-1 p-5 rounded-lg flex flex-col justify-center items-center lg:ml-[-15px]">
    <form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
      <div class="image-and-heading" style="margin-bottom: 15%">
        <img src="../../assets/Logo.png" class="size-16" alt="Side Image" />
        <h1>{{ g_const.register }}</h1>
      </div>
      <div
        class="form-field"
        style="
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        ">
        <div style="width: 48%">
          <label for="firstName" style="display: block; margin-bottom: 4px">{{
            g_const.first_name
          }}</label>
          <input
            type="text"
            id="first_name"
            placeholder="First Name"
            formControlName="first_name"
            class="form-input" />
          <!-- First Name validation error messages -->
          <div
            *ngIf="
              signupForm.get('first_name')?.touched &&
              signupForm.get('first_name')?.invalid
            "
            class="text-red-500 text-sm mt-1">
            <div *ngIf="signupForm.get('first_name')?.errors?.['required']">
              {{ g_const.nameRequired }}
            </div>
          </div>
        </div>

        <div style="width: 48%">
          <label for="lastName" style="display: block; margin-bottom: 4px">{{
            g_const.last_name
          }}</label>
          <input
            type="text"
            id="lastName"
            placeholder="Last Name"
            formControlName="last_name"
            class="form-input" />
          <!-- Last Name validation error messages -->
          <div
            *ngIf="
              signupForm.get('last_name')?.touched &&
              signupForm.get('last_name')?.invalid
            "
            class="text-red-500 text-sm mt-1">
            <div *ngIf="signupForm.get('last_name')?.errors?.['required']">
              {{ g_const.nameRequired }}
            </div>
          </div>
        </div>
      </div>

      <div class="form-field" style="border-radius: 8px; margin-bottom: 30px">
        <label for="email" style="display: block; margin-bottom: 4px">{{
          g_const.email
        }}</label>
        <input
          type="email"
          id="email"
          placeholder="E-Mail"
          formControlName="email"
          class="form-input" />
        <!-- Email validation error messages -->
        <div
          *ngIf="
            signupForm.get('email')?.touched && signupForm.get('email')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="signupForm.get('email')?.errors?.['required']">
            {{ g_const.emailRequired }}
          </div>
          <div *ngIf="signupForm.get('email')?.errors as errors">
            <div *ngIf="errors['email']">
              {{ g_const.emailInvalid }}
            </div>
            <div *ngIf="!errors['email'] && errors['pattern']">
              {{ g_const.emailInvalidDomain }}
            </div>
          </div>
        </div>
      </div>

      <div class="form-field" style="margin-bottom: 30px">
        <label for="password" style="display: block; margin-bottom: 4px">{{
          g_const.password
        }}</label>
        <div
          class="flex items-center bg-white w-full p-[9px] rounded-lg border border-gray-300 box-border leading-6">
          <input
            [type]="passwordFieldType"
            id="password1"
            placeholder="Password"
            formControlName="password1"
            class="outline-none bg-white flex-1" />
          <button (click)="togglePasswordVisibility()" type="button">
            <mat-icon class="">{{
              passwordFieldType === 'password' ? 'visibility' : 'visibility_off'
            }}</mat-icon>
          </button>
        </div>
        <!-- Password validation error messages -->
        <div
          *ngIf="
            signupForm.get('password1')?.touched &&
            signupForm.get('password1')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="signupForm.get('password1')?.errors?.['required']">
            {{ g_const.passwordRequired }}
          </div>
          <div *ngIf="signupForm.get('password1')?.errors?.['minlength']">
            {{ g_const.min8charRequired }}
          </div>
        </div>
      </div>

      <div class="form-field" style="margin-bottom: 30px">
        <label
          for="repeat-password"
          style="display: block; margin-bottom: 4px"
          >{{ g_const.repeatPassword }}</label
        >
        <div
          class="flex items-center bg-white w-full p-[9px] rounded-lg border border-gray-300 box-border leading-6">
          <input
            [type]="repeatPasswordFieldType"
            id="password2"
            placeholder="Repeat Password"
            formControlName="password2"
            class="outline-none bg-white flex-1" />
          <button (click)="toggleRepeatPasswordFieldType()" type="button">
            <mat-icon>{{
              repeatPasswordFieldType === 'password'
                ? 'visibility'
                : 'visibility_off'
            }}</mat-icon>
          </button>
        </div>
        <!-- Repeat password validation error messages -->
        <div
          *ngIf="
            signupForm.get('password2')?.touched &&
            signupForm.get('password2')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="signupForm.get('password2')?.errors?.['required']">
            {{ g_const.repeatPasswordRequired }}
          </div>
        </div>
        <div
          *ngIf="
            signupForm.hasError('passwordMismatch') &&
            signupForm.get('password2')?.touched
          "
          class="text-red-500 text-sm mt-1">
          {{ g_const.passwordMismatch }}
        </div>
      </div>
      <span class="block text-sm text-gray-400 mb-2">
        By signing up, you agree to the
        <a
          href="https://www.aicuflow.com/privacy-policy"
          target="_blank"
          class="text-txt-color no-underline">
          data privacy policy</a
        >
      </span>

      <button
        mat-flat-button
        class="w-full mb-[8px] p-3 rounded-full"
        [disabled]="signupForm.invalid">
        {{ g_const.signUp }}
      </button>

      <span style="display: block; color: #b0b0b0; font-size: 15px">
        {{ g_const.alreadyHaveAccount }}
        <a
          href="javascript:void(0)"
          style="color: #296197; text-decoration: none; margin: auto"
          (click)="navigateToLogin()"
          >{{ g_const.logIn }}</a
        >
      </span>
    </form>
  </div>
</div>
