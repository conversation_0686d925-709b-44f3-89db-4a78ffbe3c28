td,
th {
  text-align: center;
  border-right: 1px solid #e5e5f4;
  border: 1px solid #ecebf8;
}

td:last-child,
th:last-child {
  border-right: none;
}

table tr:nth-child(odd) {
  background-color: #f1f3f9;
}

table th {
  background-color: #f7f9ff;
}

td {
  color: #82868e;
}

/* In styles.css or component's CSS file */
.custom-tooltip {
  max-width: 300px; /* Change this value as needed */
  white-space: pre-line; /* Ensures line breaks in tooltip text are respected */
  text-align: left; /* Optional for left alignment */
}

/* For WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 12px; /* Width of the scrollbar */
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f3f9; /* Background color of the track */
  border-radius: 10px; /* Rounding the track */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background-color: #b8c4ce; /* Scrollbar thumb color */
  border-radius: 10px; /* Rounding the thumb */
  border: 3px solid #f1f3f9; /* Space between track and thumb */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* Darker color when hovered */
}
