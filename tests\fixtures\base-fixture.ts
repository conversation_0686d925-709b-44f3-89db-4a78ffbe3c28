import { test as base } from '@playwright/test';
import { LoginPage } from '../pages/login.page';
import { SignupPage } from '../pages/signup.page';
import { ForgetPasswordPage } from '../pages/forget-password.page';
import { NewPasswordPage } from '../pages/new-password.page';

// Declare the types of fixtures
type Fixtures = {
  loginPage: LoginPage;
  signupPage: SignupPage;
  forgetPasswordPage: ForgetPasswordPage;
  newPasswordPage: NewPasswordPage;
};

// Extend the base test with our fixtures
export const test = base.extend<Fixtures>({
  // Define the loginPage fixture
  loginPage: async ({ page }, use) => {
    // Create a new LoginPage instance
    const loginPage = new LoginPage(page);

    // Configure page timeouts
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    // We'll let the LoginPage.goto() method handle navigation
    // instead of navigating here to avoid duplication

    // Use the fixture
    await use(loginPage);
  },

  // Define the signupPage fixture
  signupPage: async ({ page }, use) => {
    // Create a new SignupPage instance
    const signupPage = new SignupPage(page);

    // Configure page timeouts
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    // We'll let the SignupPage.goto() method handle navigation
    // instead of navigating here to avoid duplication

    // Use the fixture
    await use(signupPage);
  },

  // Define the forgetPasswordPage fixture
  forgetPasswordPage: async ({ page }, use) => {
    // Create a new ForgetPasswordPage instance
    const forgetPasswordPage = new ForgetPasswordPage(page);

    // Configure page timeouts
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    // Use the fixture
    await use(forgetPasswordPage);
  },

  // Define the newPasswordPage fixture
  newPasswordPage: async ({ page }, use) => {
    // Create a new NewPasswordPage instance
    const newPasswordPage = new NewPasswordPage(page);

    // Configure page timeouts
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    // Use the fixture
    await use(newPasswordPage);
  },
});

// Export expect from the base test
export { expect } from '@playwright/test';
