export const g_const = {
  /* common */
  title: 'Title',
  description: 'Description',
  register: 'Register',
  timeframe: 'Time Frame1',
  logout: 'logout',
  options: 'Options',
  reset: 'Reset',
  save: 'Save',
  cancel: 'Cancel',
  pleaseWaitProcessTakeTime: 'Please wait. This Process can take some time.',
  processing: 'Processing',
  dragDropFile: 'Drag or Drop your CSV file',
  clickHere: 'click here',
  next: 'Next',
  delete: 'Delete',
  default: 'Default',
  /* user/login/signup */
  welcome: 'Welcome!',
  newPassword: 'New Password',
  enterDetails: 'Please Enter Your Details.',
  email: 'Email',
  password: 'Password',
  logIn: 'Login',
  signUp: 'Sign Up',
  dontHaveAcc: "Don't have an account?",
  forgotPassword: 'Forgot your Password?',
  keepLogged: 'Keep me logged in',
  name: 'Name',
  repeatPassword: 'Repeat Password',
  emailInvalid: 'Please enter a valid email address',
  alreadyHaveAccount: 'Already have an account?',
  /* form Validations */
  emailRequired: 'email is required',
  emailInvalidDomain:
    'Please enter a valid email address with a proper domain.',
  repeatPasswordRequired: 'Please confirm your password',
  passwordMismatch: 'Passwords do not match',
  passwordRequired: 'Password is required',
  resetTokenRequired: 'Reset Token is required',
  min8charRequired: 'Minimum 8 characters are required',
  emailPatternInvalid: 'Enter a valid Pattern for Email',
  fillAllFields: 'Please fill all the fields',
  fillAllRequiredFields: 'Please fill all the required fields',
  nameRequired: 'Enter a valid Name',
  namePatternInvalid: 'Enter a valid Pattern for Your Name',
  first_name: 'First Name',
  last_name: 'Last Name',

  thisIsARequiredField: 'This is a required field',
  minRequiredValueIs: 'Minimum allowed value is ',
  maxRequiredValueIs: 'Maximum allowed value is ',

  /* projects */
  project: 'Project',
  projects: 'Projects',
  addProject: 'Add Project',
  projectDesc: 'Project Description',
  noFilesFound: 'No files Found...',
  onlyCSVFilesAllowed: 'Only CSV files are allowed',
  noPreviewClickFile: 'No Preview Available. Click On The File To See One.',
  files: 'Files',
  file: 'File',
  addFile: 'Add File',
  uploadedFiles: 'Uploaded Files',
  maxAccuracy: 'Max Accuracy',
  correlations: 'Correlations',
  data: 'Data',
  date: 'Date',
  trainRuns: 'Train-Runs',
  checkColumnTypes: 'Check Column Types',
  customDataProcessing: 'Custom Data Processing',
  columnName: 'Column Name',
  columnType: 'Column Type',
  hypothesis: 'Hypothesis',

  /* Overview*/
  overview: 'Overview',

  /*data view*/
  noFileSelectDiffProject:
    'No files exist in this Project. Please select a different Project or create new file.',
  numericalColumns: 'Numerical Columns',
  categoricalColumns: 'Categorical Columns',
  targetColumn: 'Target Column',
  idColumn: 'ID Column',
  selectIdColumn: 'Select ID Column',
  processData: 'Process Data',
  Statistics: 'Statistics',
  displayAll: 'Display All',
  metric: 'Metric',
  preProcessing: 'Pre-Processing',
  tables: 'Tables',
  images: 'Images',
  downloadFileName: 'myFile',
  /*explore view*/
  addMetric: 'Add Metric',
  newPlot: 'New Plot',
  plotType: 'Plot Type',
  entryPerClass: 'Number Of Entries Per Class',
  addColumn: 'Add Column',
  removeColumn: 'Remove Column',
  selectPlot: 'Select Plot',
  metricName: 'Metric Name',
  remove: 'remove',
  /*add matrix*/
  value1: 'Value 1',
  value2: 'Value 2',
  selectColumn: 'Select Column*',
  selectColumnType: 'Select Column Type',
  selectAggregation: 'Select Aggregation*',
  smartBucketing: 'Smart Bucketing',
  customMatrics: 'Custom Metrics',
  customMetricOperationDescriptions:
    'Custom metrics allow you to create calculations between columns of your data or manually entered values. Simply name your metric, select values from the drop downs, & choose the appropriate operator.You can then use custom metrics in any of the blocks you create!',
  addValue: 'Add Value',
  /*custom data processing popup*/
  handleNanVaule: 'Handle NAN Values',
  removeThreshold: 'Remove Threshold',
  columnsThreshold: 'Columns Threshold',
  rowsThreshold: 'Rows Threshold',
  fillWith: 'Fill With',
  value: 'Value',
  selectMode: 'Select Mode',
  removeColumns: 'Remove Columns',
  /* Filter Data */
  filterData: 'Filter Data',
  Done: 'Done',
  and: 'and',
  Clear: 'Clear',
  operators: 'Operators',
  categories: 'Categories',
  buildFilterSelectTags: 'Build Filters by selecting tags',
  /*  questionnaire */
  machineLearningRecommended: 'These Machine Learning models are recommended',
  forTask: 'for the Task',
  back: 'Back',
  modelName: 'Model Name',
  status: 'Status',
  /*   result */
  viewFiles: 'View All Files',
  preprocessing: 'Preprocessing',
  dataSet: 'Data Set',

  /*   modal information */
  modelSummary: 'Model Summary',
  loss: 'loss',
  Performance: 'Performance',
  MachineLearningTasks: 'Machine learning Tasks',
  topDrivers: 'Top Drivers',
  runInference: 'Run Inference',
  columnValue: 'Column Value',
  model: 'Model',
  run: 'Run',
  result: 'Result',
  classes: 'Classes',
  probabilities: 'Probabilities',
  lower: 'Lower',
  upper: 'Upper',
  results: 'Results',
  confidenceInterval: 'Confidence Interval',

  // training
  training: 'Training',

  // new plot
  settings: 'Settings',
  data_source: 'Data Source',
  column: 'Column',

  // plot settings
  save_changes: 'Save Changes',
  discard_changes: 'Discard Changes',
  chart_elements: 'Chart Elements',
  colors: 'Colours',
  palette_name_required: 'A palette name is required',
};
