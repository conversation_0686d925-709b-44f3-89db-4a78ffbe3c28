/* eslint-disable @typescript-eslint/no-explicit-any */
import { PaginationInfo } from '../../../_models/visual-data/visual-data.model';

export interface UsageInterface {
  data: {
    storage_usage: {
      total_allowed: string;
      total_available: string;
      total_used: string;
      percentage: number;
    };
    ml_points: number;
    dl_points: number;
    last_synced_with_s3_at: string;
    training_credits: number;
  };
  errors: any | null;
  message: string;
  pagination: PaginationInfo;
  status: string;
}
