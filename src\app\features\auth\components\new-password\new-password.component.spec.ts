/* eslint-disable @typescript-eslint/no-unused-vars */
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NewPasswordComponent } from './new-password.component';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../../services/auth.services';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';

describe('NewPasswordComponent', () => {
  let component: NewPasswordComponent;
  let fixture: ComponentFixture<NewPasswordComponent>;
  let authService: jest.Mocked<AuthService>;
  let router: jest.Mocked<Router>;
  let _route: ActivatedRoute;

  beforeEach(async () => {
    const authServiceMock = {
      newPassword: jest.fn(),
    } as unknown as jest.Mocked<AuthService>;
    const routerMock = {
      navigateByUrl: jest.fn(),
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, NewPasswordComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { paramMap: { get: jest.fn(() => 'test-reset-key') } },
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NewPasswordComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    _route = TestBed.inject(ActivatedRoute);

    fixture.detectChanges(); // Trigger initial data binding
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with the reset key from route params', () => {
    expect(component.resetKey).toBe('test-reset-key');
  });

  it('should initialize the form with password controls', () => {
    const passwordControl = component.newPasswordForm.get('password');
    const password2Control = component.newPasswordForm.get('password2');
    expect(passwordControl).toBeTruthy();
    expect(password2Control).toBeTruthy();
  });

  describe('Password validation', () => {
    it('should mark passwords as required', () => {
      component.newPasswordForm.get('password')?.setValue('');
      component.newPasswordForm.get('password2')?.setValue('');
      expect(
        component.newPasswordForm.get('password')?.hasError('required'),
      ).toBe(true);
      expect(
        component.newPasswordForm.get('password2')?.hasError('required'),
      ).toBe(true);
    });

    it('should require passwords to be at least 8 characters long', () => {
      component.newPasswordForm.get('password')?.setValue('short');
      component.newPasswordForm.get('password2')?.setValue('short');
      expect(
        component.newPasswordForm.get('password')?.hasError('minlength'),
      ).toBe(true);
      expect(
        component.newPasswordForm.get('password2')?.hasError('minlength'),
      ).toBe(true);
    });
  });

  describe('onSubmit', () => {
    it('should not submit if form is invalid', () => {
      component.newPasswordForm.get('password')?.setValue('');
      component.newPasswordForm.get('password2')?.setValue('');
      component.onSubmit();

      expect(authService.newPassword).not.toHaveBeenCalled();
    });

    it('should not submit if resetKey is missing', () => {
      component.resetKey = null;
      component.newPasswordForm.get('password')?.setValue('newpassword');
      component.newPasswordForm.get('password2')?.setValue('newpassword');
      component.onSubmit();

      expect(authService.newPassword).not.toHaveBeenCalled();
    });
  });

  it('should navigate to sign-up page', () => {
    component.navigateTosignUp();
    expect(router.navigate).toHaveBeenCalledWith(['/auth/register']);
  });
});
