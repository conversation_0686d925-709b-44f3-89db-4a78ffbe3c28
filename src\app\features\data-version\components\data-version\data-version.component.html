<app-loader [loading]="loading"></app-loader>
<div class="flex flex-row justify-between w-full h-26 px-6">
  <div class="flex flex-col">
    <span class="text-base">Project Name</span>
    <h2>Data Versioning</h2>
  </div>
  <div class="flex items-center space-x-4">
    <app-search-header
      [filter]="false"
      (searchPerformed)="onSearchPerformed($event)"></app-search-header>
  </div>
</div>

<div class="grid grid-cols-4 gap-7 p-4">
  <div class="col-span-1">
    <mat-nav-list>
      @for (version of dataVersions; track version.id; let i = $index) {
        <mat-list-item
          class="rounded-none relative group"
          [ngClass]="{ 'bg-blue-100': activeItem === version.id }"
          (click)="onVersionClick(version.id, version.name)">
          {{ version.name }}
          <button
            (click)="confirmDelete(version.id); $event.stopPropagation()"
            class="h-12 absolute right-4 top-0 flex items-center transition-opacity"
            aria-label="Delete version">
            <mat-icon>delete_outline</mat-icon>
          </button>
          <button
            (click)="editDataVersion(version.id); $event.stopPropagation()"
            class="h-12 absolute right-12 top-0 flex items-center transition-opacity opacity-0 group-hover:opacity-100"
            aria-label="Edit version">
            <mat-icon>edit</mat-icon>
          </button>
        </mat-list-item>
        <mat-divider class="border-gray-300"></mat-divider>
      }
    </mat-nav-list>

    <button
      (click)="openDataversionModal()"
      class="border border-gray-300 rounded-full flex items-center space-x-1 p-3 bg-transparent mt-3"
      aria-label="Add new version">
      <mat-icon>add</mat-icon>
    </button>
  </div>
  @if (showSideModel) {
    <div class="col-span-3 p-2">
      <h4>{{ versionName }}</h4>
      <mat-tab-group
        animationDuration="0ms"
        (selectedTabChange)="onTabChange($event)">
        @for (tab of tabKeys; let i = $index; track i) {
          <mat-tab [label]="tab">
            @for (section of dataVersionTabs[tab]; track section) {
              <h3>{{ section.title }}</h3>
              <p class="text-customGray">{{ tab }}</p>
              <div class="p-4">
                <p class="tracking-wider text-customGray font-normal text-sm">
                  {{ section.description }}
                </p>
                <mat-chip-set
                  aria-label="Chip selection"
                  class="bg-transparent">
                  @for (item of section.steps; track item.id) {
                    <mat-chip
                      [class.active-chip]="item.is_active"
                      (click)="
                        openEditPipelineModal(
                          +item.id,
                          section.title,
                          section.description
                        )
                      ">
                      {{ item.name }}
                    </mat-chip>
                  }
                </mat-chip-set>

                <button
                  (click)="
                    openAddPipelineModal(
                      tab === g_const.tables ? 'table' : 'image',
                      section.title,
                      section.description
                    )
                  "
                  class="border border-gray-300 rounded-full flex items-center space-x-1 p-3 bg-transparent mt-3"
                  aria-label="Add new step">
                  <mat-icon>add</mat-icon>
                </button>
              </div>
            }
          </mat-tab>
        }
      </mat-tab-group>
    </div>
  }
</div>

@if (isNewDataPipelineModal) {
  <div
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div
      class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[860px] h-[650px]">
      <div class="flex justify-between items-center my-2">
        <div>
          <div style="display: flex">
            <mat-form-field class="ml-6 w-[300px]">
              <input
                matInput
                placeholder="{{ currentMode }}"
                [(ngModel)]="pipelineName"
                required />
            </mat-form-field>
            @if (pipelineId) {
              <button mat-icon-button class="flex-shrink-0">
                <mat-icon>edit</mat-icon>
              </button>
              <button
                mat-icon-button
                class="flex-shrink-0"
                (click)="deleteUserPipelineData()">
                <mat-icon>delete_outline</mat-icon>
              </button>
            }
            <mat-checkbox color="primary" [(ngModel)]="isPipelineMarkAsActive">
              Active
            </mat-checkbox>
          </div>
          <p class="mat-subtitle" style="margin-left: 30px">
            {{ currentTab | titlecase }}s
          </p>
        </div>
        <button
          mat-icon-button
          (click)="closePipelineModal()"
          class="text-gray-400 hover:text-gray-600">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <mat-divider></mat-divider>
      <div class="mt-6">
        <p class="text-gray-700">
          {{ currentModeDescription }}
        </p>
        @if (datasetSize.selectedStep) {
          <div class="mt-6 space-y-4 m-2">
            <div class="flex flex-row items-center gap-4">
              <button
                mat-icon-button
                [matTooltip]="datasetSize.selectedStep.description"
                matTooltipPosition="above"
                matTooltipClass="custom-tooltip"
                class="flex-shrink-0">
                <mat-icon>info_outline</mat-icon>
              </button>
              <p class="text-gray-700 flex-grow">Dataset Size</p>
              @for (
                dataset of datasetSize.selectedStep.params;
                track dataset.id
              ) {
                <div class="flex flex-row gap-4 flex-grow">
                  <div class="w-auto">
                    <mat-form-field appearance="outline" class="w-full">
                      <mat-select
                        [(ngModel)]="datasetSize.selectedParams[dataset.id]"
                        placeholder="Select {{ dataset.name }}">
                        @for (option of dataset.options; track option) {
                          <mat-option [value]="option">{{ option }}</mat-option>
                        }
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              }
            </div>
          </div>
        }
        <!-- multiple steps options -->
        <div class="overflow-y-auto overflow-x-hidden max-h-[250px]">
          @for (stepData of selectedSteps; let i = $index; track stepData) {
            <div class="mt-6 space-y-4 m-2">
              <div class="flex flex-row items-center gap-4">
                <button
                  mat-icon-button
                  [matTooltip]="stepData.selectedStep?.description"
                  matTooltipPosition="above"
                  matTooltipClass="custom-tooltip"
                  class="flex-shrink-0">
                  <mat-icon>info_outline</mat-icon>
                </button>
                <mat-form-field appearance="outline" class="flex-grow">
                  <mat-select
                    [(ngModel)]="stepData.selectedStepId"
                    (selectionChange)="onStepChange(stepData.selectedStepId, i)"
                    placeholder="Select Step">
                    <!-- Loop through the available steps -->
                    @for (step of stepsData; track step.id) {
                      <mat-option [value]="step.id">{{ step.name }}</mat-option>
                    }
                  </mat-select>
                </mat-form-field>
                @if (
                  stepData.selectedStep &&
                  stepData.selectedStep.params.length > 0
                ) {
                  <div class="flex flex-row gap-4 flex-grow">
                    @for (
                      param of stepData.selectedStep.params;
                      track param.id
                    ) {
                      <div class="w-auto">
                        @if (
                          param.options.length > 0 &&
                          !isAnArray(param.options[0])
                        ) {
                          <mat-form-field appearance="outline" class="w-full">
                            <mat-select
                              [(ngModel)]="stepData.selectedParams[param.id]"
                              placeholder="Select {{ param.name }}">
                              @for (option of param.options; track option) {
                                <mat-option [value]="option">{{
                                  option
                                }}</mat-option>
                              }
                            </mat-select>
                          </mat-form-field>
                        }
                        @if (
                          param.options.length > 0 &&
                          isAnArray(param.options[0])
                        ) {
                          <!-- Dropdown for Array Options having array values-->
                          <mat-form-field appearance="outline" class="w-full">
                            <mat-select
                              [(ngModel)]="stepData.selectedParams[param.id]"
                              placeholder="Select {{ param.name }}">
                              <mat-option
                                *ngFor="let option of param.options"
                                [value]="option"
                                >{{ safeJoin(option, ' - ') }}</mat-option
                              >
                            </mat-select>
                          </mat-form-field>
                        }
                        @if (param.options.length === 0 && param.value) {
                          <!-- Text Input -->
                          <mat-form-field appearance="outline" class="w-full">
                            <input
                              matInput
                              [(ngModel)]="stepData.selectedParams[param.id]"
                              [placeholder]="'Enter ' + param.name"
                              (ngModelChange)="
                                stepData.selectedParams[param.id] =
                                  $event.trim() || null
                              " />
                          </mat-form-field>
                        }
                        @if (param.options.length === 0 && !param.value) {
                          <mat-form-field appearance="outline" class="w-full">
                            <input
                              matInput
                              [disabled]="true"
                              [placeholder]="
                                'No ' + param.name + ' available'
                              " />
                          </mat-form-field>
                        }
                      </div>
                    }
                  </div>
                }
                <button
                  mat-icon-button
                  class="flex-shrink-0"
                  (click)="removeStep(i)">
                  <mat-icon>delete_outline</mat-icon>
                </button>
              </div>
            </div>
          }
        </div>
      </div>
      <button
        class="border border-gray-300 rounded-full flex items-center space-x-1 p-3 bg-transparent mt-3"
        (click)="addNewStep()"
        aria-label="Add new step">
        <mat-icon>add</mat-icon>
      </button>
      <div class="flex flex-row absolute bottom-4 right-4 gap-3">
        <button mat-flat-button color="primary" (click)="saveSelections()">
          Save
        </button>
        @if (pipelineId) {
          <button
            mat-button
            class="bg-white text-txt-color hover:bg-gray-200"
            (click)="fetchUserPipelineById(pipelineId)">
            <mat-icon>restart_alt</mat-icon> Default
          </button>
        }
      </div>
    </div>
  </div>
}
@if (isdataVersionModal) {
  <div
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div
      class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[620px] h-[320px]">
      <div class="flex justify-between items-center p-4">
        <h6>{{ EditfileID ? 'Edit Data version' : 'New Data version' }}</h6>
        <button
          mat-icon-button
          (click)="closeDataVersionModal()"
          class="text-gray-400 hover:text-gray-600"
          aria-label="Close modal">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <mat-divider></mat-divider>
      <form [formGroup]="dataVersionForm" class="p-2.5">
        <div class="w-full py-2.5">
          <label
            for="dataversionname"
            class="flex flex-col mb-4 font-medium text-base"
            >Data version Name</label
          >
          <input
            formControlName="name"
            placeholder="Data version Name"
            class="outline-none bg-transparent border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md" />
          @if (
            dataVersionForm.get('name')?.hasError('required') &&
            dataVersionForm.get('name')?.touched
          ) {
            <div class="text-red-500">Valid name is required</div>
          }
        </div>
      </form>
      <div class="mt-4 ml-2 absolute bottom-4">
        <button
          mat-flat-button
          class="!bg-[#296197]"
          (click)="EditfileID ? updateDataVersionName() : addDataVersionName()">
          {{ EditfileID ? 'Edit' : 'Save' }}
        </button>
      </div>
    </div>
  </div>
}

@if (isfileDeletePopup) {
  <app-delete-modal
    (deleteEvent)="deleteDataVersion(+datasetVersionId)"
    (cancelEvent)="closeDeletePopup()"></app-delete-modal>
}
