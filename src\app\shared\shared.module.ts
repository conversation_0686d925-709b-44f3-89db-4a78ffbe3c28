import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LoaderComponent } from './components/loader/loader.component';
import { SideBarComponent } from './components/side-bar/side-bar.component';
import { HeaderComponent } from './components/header/header.component';
import { ComponentHeaderComponent } from './components/component-header/component-header.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ProjectModalComponent } from './components/project-modal/project-modal.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { SearchHeaderComponent } from './components/search-header/search-header.component';
import { CardsComponent } from './components/cards/cards.component';
import { MatCard, MatCardModule } from '@angular/material/card';
import { DeleteModalComponent } from './components/delete-modal/delete-modal.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
// import { DataVersionComponent } from '../components/data-version/data-version/data-version.component';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialogContent } from '@angular/material/dialog';
import { OuterLayoutComponent } from './layout/outer-layout/outer-layout.component';

@NgModule({
  declarations: [
    OuterLayoutComponent,
    LoaderComponent,
    SideBarComponent,
    HeaderComponent,
    ComponentHeaderComponent,
    ProjectModalComponent,
    SearchHeaderComponent,
    CardsComponent,
    DeleteModalComponent,
    // DataVersionComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIcon,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatMenuModule,
    MatDividerModule,
    MatListModule,
    MatTabsModule,
    MatCard,
    MatButton,
    MatDialogContent,
  ],
  exports: [
    OuterLayoutComponent,
    LoaderComponent,
    SideBarComponent,
    HeaderComponent,
    ComponentHeaderComponent,
    ProjectModalComponent,
    SearchHeaderComponent,
    CardsComponent,
    DeleteModalComponent,
  ],
})
export class SharedModule {}
