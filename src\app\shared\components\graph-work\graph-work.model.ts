export interface PlotLayout {
  height: number;
  width: string;
}

export interface HeatmapData {
  z: number[][];
  x: string[];
  y: string[];
  type: string;
  colorscale: string; // Change to your preferred colorscale
  colorbar: {
    title: string;
  };
}

export interface HeatmapLayout {
  title: string;
  xaxis: {
    title: string;
  };
  yaxis: {
    title: string;
  };
}

export interface Config {
  displayModeBar: boolean;
  responsive: boolean;
}

export interface PlotData {
  boxmean: boolean;
  line: Line;
  marker: Marker;
  name: string;
  y: number[];
  type: string;
  quartilemethod: string;
}

export interface Line {
  color: string;
}

export interface Marker {
  color: string;
}
