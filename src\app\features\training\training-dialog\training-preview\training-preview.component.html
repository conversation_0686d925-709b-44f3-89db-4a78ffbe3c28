<div>
  <h3 class="items-center flex justify-center">Preview</h3>
  <div class="flex flex-col gap-4 p-5">
    <div class="relative">
      Name :
      <span class="absolute right-0 ellipsis">{{ trainingDetails.name }}</span>
    </div>
    <div class="relative">
      Training Data :
      <span class="absolute right-0 ellipsis">{{
        trainingDetails.trainingData
      }}</span>
    </div>
    <div class="relative">
      Analysis Goal Name :
      <span class="absolute right-0 ellipsis">
        {{ trainingDetails.analysisGoalName }}</span
      >
    </div>
    <div class="relative">
      Analysis Goal Type :
      <span class="absolute right-0 ellipsis">{{
        trainingDetails.anaysisGoalType
      }}</span>
    </div>
    <div class="relative">
      Selected Target :
      <span class="absolute right-0 ellipsis">{{
        trainingDetails.selectedTarget
      }}</span>
    </div>
    <div class="relative">
      Selected Features :
      <span class="absolute right-0 ellipsis">{{
        trainingDetails.selectedFeatures
      }}</span>
    </div>
    <div class="relative">
      Train Size :
      <span class="absolute right-0 ellipsis"
        >{{ trainingDetails.train_size }}%</span
      >
    </div>
    <div class="relative">
      Test Size :
      <span class="absolute right-0 ellipsis"
        >{{ trainingDetails.test_size }}%</span
      >
    </div>
  </div>
</div>
