@if (isModalOpen) {
  <div
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div
      class="rounded-xl shadow-lg relative w-[800px] overflow-hidden file-upload-modal flex flex-col bg-white">
      <div class="flex justify-between items-center p-4 border-b">
        <h3 class="text-lg font-medium">Add Data</h3>
        <button
          mat-icon-button
          (click)="closeModal()"
          class="text-gray-400 hover:text-gray-700">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      @if (loading) {
        <div
          class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
          <app-loader [loading]="loading"></app-loader>
        </div>
      }

      <input
        #fileInput
        type="file"
        multiple
        accept=".csv,.parquet,.jpg,.jpeg,.png"
        class="hidden"
        (change)="onFileInputChange($event)" />

      <ngx-file-drop
        dropZoneLabel="Drop files or folders here"
        [multiple]="true"
        (onFileDrop)="dropped($event)">
        <ng-template
          ngx-file-drop-content-tmp
          let-openFileSelector="openFileSelector">
          <div class="flex-grow overflow-y-auto p-4 content-container">
            @if (!uploadedFilesList.length) {
              <div class="upload-file-container mb-2">
                <div class="w-full cursor-pointer text-center py-4">
                  <div class="flex justify-center items-center mb-4">
                    <div
                      class="icon-container flex justify-center items-center"
                      aria-hidden="true">
                      <mat-icon
                        class="text-5xl text-[#296197] transform scale-125"
                        fontSet="material-icons-outlined"
                        >cloud_upload</mat-icon
                      >
                    </div>
                  </div>
                  <p
                    class="text-[#73777F] mb-1"
                    (click)="openFileSelector()"
                    (keyup.enter)="openFileSelector()"
                    tabindex="0"
                    role="button"
                    aria-label="Select files to upload">
                    Drag and Drop your files and folders here, or
                    <a class="text-[#296197] cursor-pointer"> Browse File.</a>
                  </p>
                  <p class="text-[#73777F] text-sm">
                    (csv, parquet, jpg or png)
                  </p>
                </div>
              </div>
            }

            <!-- Empty state - shown when no files are selected -->
            @if (!uploadedFilesList || uploadedFilesList.length === 0) {
              <div class="text-center p-4 text-gray-500">No files selected</div>
            }

            <!-- Uploaded files list -->
            @if (uploadedFilesList && uploadedFilesList.length > 0) {
              <div class="mb-4">
                <div class="flex justify-between items-center mb-4">
                  <h4 class="text-lg font-medium">
                    Uploaded Files ({{
                      uploadedFilesList ? uploadedFilesList.length : 0
                    }})
                  </h4>
                </div>

                <!-- File list with table layout -->
                <div class="max-h-[300px] overflow-y-auto rounded p-2">
                  @if (uploadedFilesList && uploadedFilesList.length > 0) {
                    <table class="w-full border-collapse">
                      <thead>
                        <tr class="bg-gray-200">
                          <th class="p-2 text-left font-bold">File</th>
                          <th class="p-2 text-left font-bold">Size</th>
                          <th class="p-2 text-left font-bold">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        @for (
                          file of uploadedFilesList;
                          track trackByIndex($index);
                          let i = $index
                        ) {
                          <tr
                            class="border-b hover:bg-gray-50"
                            [attr.data-index]="i">
                            <td class="p-2">
                              <div class="flex items-center">
                                @if (isImageFile(file.file)) {
                                  <mat-icon class="mr-2"
                                    >image_outline</mat-icon
                                  >
                                } @else if (isCsvFile(file.file)) {
                                  <mat-icon
                                    fontSet="material-icons-outlined"
                                    class="mr-2"
                                    >table_chart</mat-icon
                                  >
                                } @else if (isParquetFile(file.file)) {
                                  <mat-icon
                                    fontSet="material-icons-outlined"
                                    class="mr-2"
                                    >data_object</mat-icon
                                  >
                                } @else {
                                  <mat-icon
                                    class="mr-2"
                                    fontSet="material-icons-outlined"
                                    >insert_drive_file</mat-icon
                                  >
                                }
                                <span class="truncate max-w-[200px]">{{
                                  file.file.name
                                }}</span>
                              </div>
                            </td>
                            <td class="p-2">
                              {{ formatFileSize(file.file.size) }}
                            </td>
                            <td class="p-2">
                              <button
                                mat-icon-button
                                (click)="removeFile(i)"
                                [disabled]="file.uploading"
                                aria-label="Remove file">
                                <mat-icon>delete_outline</mat-icon>
                              </button>
                            </td>
                          </tr>
                        }
                      </tbody>
                    </table>
                  }
                </div>

                <!-- Add more files -->
                <div class="flex justify-center mt-1 mb-1">
                  <button
                    mat-flat-button
                    color="primary"
                    (click)="openFileSelector()"
                    class="py-1">
                    <mat-icon class="mr-1">add</mat-icon> Add More Files
                  </button>
                </div>
              </div>
            }
          </div>
        </ng-template>
      </ngx-file-drop>

      <!-- Footer with buttons - improved accessibility -->
      <div class="flex justify-end space-x-2 p-4">
        <button
          mat-button
          (click)="closeModal()"
          aria-label="Cancel and close modal">
          Cancel
        </button>
        <button
          mat-flat-button
          color="primary"
          [disabled]="!uploadedFilesList.length"
          (click)="processFiles()"
          aria-label="Upload selected files">
          Upload
        </button>
      </div>
    </div>
  </div>
}

@if (isuploadModal) {
  <div
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div
      class="file-upload-modal rounded-xl shadow-lg p-4 relative w-[700px] h-[664px] bg-white">
      <div class="flex justify-between items-center object-center p-2">
        <h3>Add Data</h3>
        <button
          mat-icon-button
          (click)="closeFileUploadModal(false)"
          class="text-gray-400 hover:text-gray-600">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="fileForm" (ngSubmit)="saveChanges()">
        <div class="container p-4 overflow-hidden h-[500px]">
          @if (successfullUploadMessage) {
            <p>{{ successfullUploadMessage }}</p>
          }
          <p>
            Please check the data types of the columns and select a unique ID
            for each table.
          </p>

          <!-- Loop through the FormArray containing file forms -->
          <div
            formArrayName="uploadedFiles"
            class="overflow-y-auto max-h-[400px]">
            @if (loading) {
              <div
                class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
                <app-loader [loading]="loading"></app-loader>
                <p class="z-50 mt-12">Loading your Files and folders...</p>
              </div>
            }
            @if (isImageUpload) {
              <div>
                <h3 style="margin-top: 10%">File Name</h3>
                <h5
                  style="
                    border: 1px solid black;
                    padding: 2px;
                    border-radius: 8px;
                  ">
                  {{ this.imageName }}
                </h5>
              </div>
            }
            @for (
              panel of uploadedFiles.controls;
              track $index;
              let panelIndex = $index
            ) {
              <mat-expansion-panel
                class="m-2 mat-expansion-panel-modal upload-files"
                [formGroupName]="panelIndex"
                [expanded]="true">
                <mat-expansion-panel-header>
                  <span class="ml-0 metadata text-sm">{{
                    filePanels[panelIndex]?.fileName
                  }}</span>
                </mat-expansion-panel-header>

                <p class="text-sm">File Name</p>
                <input
                  class="outline-none border p-3 shadow-sm h-12 rounded-md w-full"
                  formControlName="fileName"
                  [placeholder]="filePanels[panelIndex]?.fileName"
                  [value]="filePanels[panelIndex]?.fileName" />

                <div class="flex items-center pt-4">
                  <!-- Add controls for columns, delimiter, header_row, etc. -->

                  <div class="p-4 space-y-4">
                    @if (filePanels[panelIndex]?.columns?.length) {
                      @for (
                        column of filePanels[panelIndex]?.columns;
                        track trackByIndex($index);
                        let i = $index
                      ) {
                        <div class="flex flex-row gap-4 items-center">
                          <div class="flex flex-col">
                            <input
                              class="outline-none border p-3 shadow-sm h-12 w-max rounded-md"
                              [value]="column.name"
                              readonly
                              type="text" />
                          </div>
                          <div class="flex flex-col">
                            <mat-select
                              class="outline-none border p-3 shadow-sm h-12 w-max min-w-[180px] rounded-md"
                              [value]="column.type">
                              <mat-option [value]="column.type">
                                {{ column.type }}
                              </mat-option>
                              @for (dt of dataTypesArray; track $index) {
                                <mat-option [value]="dt">
                                  {{ dt.name }}
                                </mat-option>
                              }
                            </mat-select>
                          </div>

                          <div class="flex items-center justify-end">
                            <mat-radio-group
                              [formControlName]="'selectedColumnIds'"
                              [name]="'radioGroup' + panelIndex"
                              [value]="
                                selectedColumnIds.includes(column.name)
                                  ? column.name
                                  : null
                              ">
                              <mat-radio-button
                                [value]="column.name"
                                [disabled]="
                                  !selectedColumnIds.includes(column.name)
                                "
                                (click)="onRadioClick(column.name, panelIndex)">
                              </mat-radio-button>
                            </mat-radio-group>
                          </div>
                        </div>
                      }
                    }
                  </div>
                </div>

                <div class="pt-4">
                  <p class="text-sm">Advanced Settings</p>
                  <p class="text-sm">
                    Use
                    <input
                      class="h-12 w-[30px] input-dele text-center"
                      formControlName="header_row"
                      [ngModel]="
                        filePanels[panelIndex]?.fileMetaData?.header_row
                      "
                      [placeholder]="
                        filePanels[panelIndex]?.fileMetaData?.header_row
                      " />
                    Record as Header
                  </p>
                  <p class="text-sm">
                    Delimiter
                    <input
                      class="h-12 w-[30px] input-dele text-center"
                      formControlName="delimiter"
                      [ngModel]="
                        filePanels[panelIndex]?.fileMetaData?.delimiter
                      "
                      [placeholder]="
                        filePanels[panelIndex]?.fileMetaData?.delimiter
                      " />
                  </p>
                  <p class="text-sm">
                    Auto-Select Data Types based on first
                    <input
                      class="h-12 w-14 input-dele text-center"
                      formControlName="data_type_rows"
                      [ngModel]="
                        filePanels[panelIndex]?.fileMetaData?.data_type_rows
                      "
                      [placeholder]="
                        filePanels[panelIndex]?.fileMetaData?.data_type_rows
                      " />
                    Rows
                  </p>
                </div>
              </mat-expansion-panel>
            }
          </div>
        </div>

        <div class="mt-6 flex justify-start space-x-4 static">
          <button mat-flat-button class="bg-btn-color" type="submit">
            Confirm
          </button>
          <button
            type="button"
            mat-button
            class="border border-[#dbdfea] rounded"
            (click)="discardChanges()">
            <mat-icon>restart_alt</mat-icon>
            Default
          </button>
        </div>
      </form>
    </div>
  </div>
}

@if (openFromInfoModal) {
  <div
    class="fixed bg-gray-100 rounded-lg shadow-md z-50 w-full max-w-md mx-auto p-6 space-y-4 text-sm text-gray-700"
    (click)="$event.stopPropagation()"
    (keyup.enter)="$event.stopPropagation()"
    tabindex="0">
    <div class="max-h-64 overflow-y-auto space-y-2">
      @for (info of formInfoArray; track $index) {
        <p class="text-gray-600">{{ info.name }} :{{ info.description }}</p>
      }
    </div>
  </div>
}
@if (addOnStorageModal) {
  <app-buy-plan-modal
    [openModal]="addOnStorageModal"
    (addOnStorage)="openAddOnStorage($event)"></app-buy-plan-modal>
}
