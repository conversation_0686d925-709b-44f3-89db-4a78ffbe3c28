<div class="flex flex-col h-screen md:flex-row">
  <div class="flex flex-col lg:flex-row items-stretch w-full mx-auto">
    <div class="relative">
      <img
        src="../../assets/Welcome.png"
        alt="Side Image"
        class="w-full h-[30vh] md:h-[100vh] md:w-[40vw]" />

      <div
        class="absolute inset-y-0 left-0 flex items-center justify-center ml-4 sm:ml-8">
        <div class="text-white">
          <h1 class="text-white m-0 welcome">Welcome</h1>
          <p class="mt-3">Please Enter Your Details.</p>
        </div>
      </div>
    </div>
    <div
      class="flex-1 p-5 rounded-lg flex flex-col justify-center items-center lg:ml-[-15px]">
      <form
        class="w-full max-w-xs lg:max-w-sm"
        [formGroup]="newPasswordForm"
        (ngSubmit)="onSubmit()">
        <div class="mb-[60px] flex flex-col">
          <img
            src="../../assets/Logo.png"
            alt="Side Image"
            class="h-20 w-20 mb-6" />
          <h1>{{ g_const.newPassword }}</h1>
        </div>

        <div class="form-field" style="margin-bottom: 30px">
          <label for="password" style="display: block; margin-bottom: 4px"
            >Reset Token</label
          >
          <input
            type="text"
            id="password_reset_token"
            placeholder="Reset Token"
            formControlName="password_reset_token"
            class="form-input" />
          <div
            *ngIf="
              newPasswordForm.get('password_reset_token')?.touched &&
              newPasswordForm.get('password_reset_token')?.invalid
            "
            class="text-red-500 text-sm mt-1">
            <div
              *ngIf="
                newPasswordForm.get('password_reset_token')?.errors?.[
                  'required'
                ]
              ">
              {{ g_const.resetTokenRequired }}
            </div>
          </div>
        </div>
        <div class="form-field" style="margin-bottom: 30px">
          <label for="password" style="display: block; margin-bottom: 4px">{{
            g_const.newPassword
          }}</label>
          <input
            type="password"
            id="password"
            placeholder="Password"
            formControlName="password"
            class="form-input" />
          <!-- Password validation error messages -->
          <div
            *ngIf="
              newPasswordForm.get('password1')?.touched &&
              newPasswordForm.get('password1')?.invalid
            "
            class="text-red-500 text-sm mt-1">
            <div *ngIf="newPasswordForm.get('password1')?.errors?.['required']">
              {{ g_const.passwordRequired }}
            </div>
            <div
              *ngIf="newPasswordForm.get('password1')?.errors?.['minlength']">
              {{ g_const.min8charRequired }}
            </div>
          </div>
        </div>

        <div class="form-field" style="margin-bottom: 30px">
          <label
            for="repeat-password"
            style="display: block; margin-bottom: 4px"
            >{{ g_const.repeatPassword }}</label
          >
          <input
            type="password"
            id="password2"
            placeholder="Repeat Password"
            formControlName="password2"
            class="form-input" />
          <!-- Repeat password validation error messages -->
          <div
            *ngIf="
              newPasswordForm.get('password2')?.touched &&
              newPasswordForm.get('password2')?.invalid
            "
            class="text-red-500 text-sm mt-1">
            <div *ngIf="newPasswordForm.get('password2')?.errors?.['required']">
              {{ g_const.repeatPasswordRequired }}
            </div>
          </div>
          <div
            *ngIf="
              newPasswordForm.hasError('passwordMismatch') &&
              newPasswordForm.get('password2')?.touched
            "
            class="text-red-500 text-sm mt-1">
            {{ g_const.passwordMismatch }}
          </div>
        </div>

        <button
          mat-flat-button
          [disabled]="newPasswordForm.invalid"
          class="w-full mt-4 p-3">
          {{ g_const.save }}
        </button>

        <span class="block text-sm text-gray-400" style="margin-top: 3%">
          {{ g_const.dontHaveAcc }}
          <a
            href="javascript:void(0)"
            class="text-txt-color no-underline"
            (click)="navigateTosignUp()"
            >{{ g_const.signUp }}</a
          >
        </span>
      </form>
    </div>
  </div>
</div>
