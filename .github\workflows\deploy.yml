name: Deploy Angular to S3

on:
  push:
    branches:
      - prod
      - test # Change to the appropriate branch if needed

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout Code
      - name: Checkout Code
        uses: actions/checkout@v3

      # Step 2: Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18 # Adjust to match your project requirements

      - name: Cache Node Modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      # Step 3: Install Dependencies
      - name: Clean and Reinstall Dependencies
        run: |
          rm -rf node_modules package-lock.json
          npm install --legacy-peer-deps

      - name: Install Angular CLI
        run: npm install -g @angular/cli@18.2.1

      # Step 4: Build Angular Application
      - name: Build Angular Application
        run: |
          if [[ "${{ github.ref_name }}" == "prod" ]]; then
            ng build --configuration production
          elif [[ "${{ github.ref_name }}" == "test" ]]; then
            ng build --configuration test
          else
            echo "Branch not configured for deployment" && exit 1
          fi

      - name: List build directory
        run: ls -R dist/

      # Step 5: Deploy to S3 Bucket
      - name: Deploy to S3
        run: |
          if [[ "${{ github.ref_name }}" == "prod" ]]; then
            aws s3 sync ./dist/aicu/browser s3://platform.aicuflow.com --region eu-central-1
          elif [[ "${{ github.ref_name }}" == "test" ]]; then
            aws s3 sync ./dist/aicu/browser s3://platform.test.aicuflow.com --region eu-central-1
          else
            echo "Branch not configured for deployment" && exit 1
          fi
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_MAIN }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_MAIN }}
