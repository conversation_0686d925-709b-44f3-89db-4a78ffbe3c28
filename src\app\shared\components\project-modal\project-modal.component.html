<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="rounded-xl shadow-lg p-4 relative w-[700px] mat-dialog overflow-hidden">
    <div class="flex justify-between items-center object-center p-2">
      <h3>{{ formData ? 'Edit' : 'New' }} {{ entityName }}</h3>
      <button
        mat-icon-button
        (click)="cancel()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="border-t-[1px] opacity-40 w-full border-gray-300 mb-4"></div>

    <!-- Form Content -->
    <div class="flex-grow overflow-y-auto max-h-[45vh]">
      <form [formGroup]="entityForm" class="p-2.5">
        <!-- Project Fields -->
        <div *ngIf="entityType === 'project'">
          <div class="w-full py-2.5">
            <label for="style" class="flex flex-col mb-4 font-medium">{{
              labelName
            }}</label>
            <input
              matInput
              [placeholder]="placeholderTitle"
              formControlName="title"
              maxlength="200"
              class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md" />
          </div>
          <div class="w-full py-2.5">
            <label for="style" class="flex flex-col mb-4 font-medium">{{
              labelDescription
            }}</label>
            <textarea
              matInput
              [placeholder]="placeholderDescription"
              formControlName="description"
              class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full rounded-md min-h-[90px]"></textarea>
          </div>
        </div>

        <!-- Hypothesis Fields -->
        <div *ngIf="entityType === 'hypothesis'">
          <div class="w-full py-2.5">
            <label for="style" class="flex flex-col mb-4 font-medium">{{
              labelName
            }}</label>
            <input
              matInput
              [placeholder]="placeholderTitle"
              formControlName="hypothesis_type"
              class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md" />
          </div>
          <div class="w-full py-2.5">
            <label for="style" class="flex flex-col mb-4 font-medium">{{
              labelDescription
            }}</label>
            <textarea
              matInput
              [placeholder]="placeholderDescription"
              formControlName="hypothesis"
              class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full rounded-md min-h-[90px]"></textarea>
          </div>
        </div>

        <div class="text-center text-red-600" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>
      </form>
    </div>

    <div class="mt-4">
      <button mat-flat-button (click)="save()">Save</button>
    </div>
  </div>
</div>
