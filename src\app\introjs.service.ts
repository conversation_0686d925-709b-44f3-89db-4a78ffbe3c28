import { Injectable } from '@angular/core';
import introJs from 'intro.js/';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, switchMap, tap } from 'rxjs';
import { environment } from './env/env';

@Injectable({
  providedIn: 'root',
})
export class IntroService {
  constructor(private http: HttpClient) {}
  private introJSUrl = environment.apiUrl + 'users';
  introJs = introJs();

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  getOnboardingStatus(): Observable<OnboardingResponse> {
    return this.http.get<OnboardingResponse>(
      `${this.introJSUrl}/onboarding-status/`,
      { headers: this.getHeaders() },
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updateOnboardingStatus(status: boolean): Observable<any> {
    return this.http.post<any>( // eslint-disable-line @typescript-eslint/no-explicit-any
      `${this.introJSUrl}/onboarding-status/`,
      { onboarding_status: status },
      { headers: this.getHeaders() },
    );
  }

  // Custom method to start Intro.js based on onboarding status
  startIntroIfNeeded(context: string): void {
    this.getOnboardingStatus()
      .pipe(
        tap(onboardingCompleted => {
          const onboardStatus = onboardingCompleted?.data?.onboarding_status;
          this.introJs.exit(onboardStatus);
          if (!onboardStatus) {
            document.cookie =
              'introjs-dontShowAgain=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
            this.startTour(context);
          }
          return onboardStatus;
        }),
        switchMap(onboardingCompleted => {
          const status = onboardingCompleted?.data?.onboarding_status;
          if (!status && context === 'overview') {
            return this.updateOnboardingStatus(true);
          }
          return of(null);
        }),
      )
      .subscribe({
        next: response => {
          console.log('response', response);
          if (response) {
            console.log('Onboarding status updated successfully:', response);
          } else {
            console.log('No onboarding status update was required.');
          }
        },
        error: error => {
          console.error('Error in onboarding flow:', error);
        },
      });
  }

  stopOnboardingProcess(onboardStatus: boolean) {
    return this.introJs.exit(onboardStatus);
  }

  startTour(context: string) {
    const getSteps = this.getStepsForContext(context);

    setTimeout(() => {
      this.introJs.setOptions({
        skipLabel: 'x',
        doneLabel: 'Finish',
        showProgress: true,
        hidePrev: true,
        //hideNext: true,
        nextToDone: true,
        dontShowAgain: true,
        dontShowAgainLabel: "don't show next time",
        steps: getSteps,
      });
      // Add onchange callback to manipulate the Next button
      this.introJs.onchange(targetElement => {
        // const currentStep = this.introJs._currentStep; // Get the current step index
        const currentStep = targetElement?.getAttribute('id'); // Get the current step index
        if (currentStep === 'step1-2' || currentStep === 'step2-5') {
          // Hide the Next button if the id matches step1-3
          const nextButton = document.querySelector(
            '.introjs-nextbutton',
          ) as HTMLElement;
          if (nextButton) {
            nextButton.style.display = 'none';
          }
        } else {
          // Show the Next button for other steps
          const nextButton = document.querySelector(
            '.introjs-nextbutton',
          ) as HTMLElement;
          if (nextButton) {
            nextButton.style.display = 'inline-block';
          }
        }
      });
      this.introJs.start();
    }, 500);
  }

  goToNextStep() {
    // Proceed to the next step in Intro.js
    this.introJs.nextStep();
  }

  private getStepsForContext(context: string) {
    switch (context) {
      case 'projects':
        return [
          { intro: 'Welcome to AICUflow' },
          { element: '#step1-1', intro: 'All your projects in a single view!' },
          { element: '#step1-2', intro: 'Click here!' },
        ];
      case 'overview':
        return [
          { element: '#step2-1', intro: 'Project related details!' },
          { element: '#step2-2', intro: 'Current -  Overview' },
          { element: '#step2-3', intro: 'Data View' },
          { element: '#step2-4', intro: 'Data Insights' },
          { element: '#step2-5', intro: 'Click here to view plots!' },
        ];

      default:
        return [];
    }
  }
}

interface OnboardingResponse {
  status: string;
  message: string;
  data: { onboarding_status: boolean };
  errors: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  pagination: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}
