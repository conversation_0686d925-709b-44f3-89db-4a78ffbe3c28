import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProjectsComponent } from './projects.component';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { SearchOptions } from '../../../_models/common.model';
import { Project } from '../models/project.model';
import { ProjectService } from '../../../services/project.service';
import { CommonModule } from '@angular/common';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { HttpClientModule } from '@angular/common/http';

describe('ProjectsComponent', () => {
  let component: ProjectsComponent;
  let fixture: ComponentFixture<ProjectsComponent>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;

  beforeEach(async () => {
    const projectServiceMock = {
      getProjects: jest.fn().mockReturnValue(
        of({
          data: [
            {
              id: 1,
              owner: 101,
              title: 'Project 1',
              description: 'Description 1',
              created_at: new Date(),
              updated_at: new Date(),
              file_count: 10,
              files_size: '1GB',
              plot_count: 5,
              model_count: 2,
              train_runs: 3,
              folder_key: 'folder_key_1',
              folder_id: 201,
              uploadedFiles: [],
              unknownFiles: undefined,
            },
            {
              id: 2,
              owner: 102,
              title: 'Project 2',
              description: 'Description 2',
              created_at: new Date(),
              updated_at: new Date(),
              file_count: 15,
              files_size: '2GB',
              plot_count: 7,
              model_count: 3,
              train_runs: 5,
              folder_key: 'folder_key_2',
              folder_id: 202,
              uploadedFiles: [],
              unknownFiles: undefined,
            },
          ],
          pagination: {
            count: 2,
            next: null,
            previous: null,
          },
        }),
      ),
      getProjectsBySearch: jest.fn(),
      getProjectsByUrl: jest.fn(),
      createProject: jest.fn(),
      deleteProject: jest.fn(),
      editProject: jest.fn(),
    };

    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    };

    const routerMock = {
      navigate: jest.fn(),
    };

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        HttpClientModule,
        MatSidenavModule,
        MatIconModule,
        MatButtonModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatCardModule,
        MatMenuModule,
        MatFormFieldModule,
      ],
      declarations: [ProjectsComponent],
      providers: [
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: Router, useValue: routerMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProjectsComponent);
    component = fixture.componentInstance;
    projectService = TestBed.inject(
      ProjectService,
    ) as jest.Mocked<ProjectService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call getProjects on init', () => {
      jest.spyOn(component, 'getProjects');
      component.ngOnInit();
      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('loadProjects', () => {
    it('should stop loading on error', () => {
      projectService.getProjectsByUrl.mockReturnValue(
        throwError(() => 'Error'),
      );
      component.loadProjects();
      expect(component.loading).toBe(false);
    });
  });

  describe('Pagination', () => {
    it('should calculate pagination list', () => {
      component.pageSize = 5;
      component.setPagination(15);
      expect(component.paginationList.length).toBe(3);
    });
  });

  describe('Project popup management', () => {
    it('should open project popup for editing', () => {
      const testProject = {
        id: 1,
        title: 'Edit Project',
        description: 'Project Description',
      } as Project;
      component.openEditForm(testProject);
      expect(component.formData).toEqual(testProject);
      expect(component.projectPopup).toBe(true);
    });

    it('should open project popup for adding a new project', () => {
      component.openAddForm();
      expect(component.formData).toBeNull();
      expect(component.projectPopup).toBe(true);
    });
  });

  describe('scroll handling', () => {
    it('should call loadProjects on window scroll', () => {
      jest.spyOn(component, 'loadProjects');
      component.onWindowScroll();
      expect(component.loadProjects).toHaveBeenCalled();
    });
  });

  describe('onSearchPerformed', () => {
    it('should call getProjects if no search options provided', () => {
      jest.spyOn(component, 'getProjects');
      component.onSearchPerformed({} as SearchOptions);

      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('addProject', () => {
    it('should call createProject and reload projects on success', () => {
      const mockProject = {
        title: 'New Project',
        description: 'Test Description',
      };
      const mockResponse = {
        data: {
          id: 1,
          owner: 101,
          title: 'New Project',
          description: 'Test Description',
          created_at: new Date(),
          updated_at: new Date(),
          file_count: 0,
          files_size: '0B',
          plot_count: 0,
          model_count: 0,
          train_runs: 0,
          folder_key: 'folder_key_1',
          folder_id: 201,
          uploadedFiles: [],
          unknownFiles: undefined,
        },
      }; // Mock a valid ResponseData<Project> object

      projectService.createProject.mockReturnValue(of(mockResponse)); // Return the correct mock response

      jest.spyOn(component, 'getProjects');

      component.formData = mockProject as Project;
      component.addProject();

      expect(projectService.createProject).toHaveBeenCalledWith(mockProject);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should show an error if createProject fails', () => {
      projectService.createProject.mockReturnValue(throwError(() => 'Error'));

      component.addProject();

      expect(toastrService.error).toHaveBeenCalledWith('Failed to add project');
    });
  });

  describe('deleteProject', () => {
    it('should call deleteProject and reload projects on success', () => {
      const mockResponse = { message: 'Project deleted successfully' };
      projectService.deleteProject.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getProjects');

      component.deleteProject(1);

      expect(projectService.deleteProject).toHaveBeenCalledWith(1);
      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('selectProject', () => {
    it('should navigate to the project overview page', () => {
      component.selectProject(1);
      expect(router.navigate).toHaveBeenCalledWith(['/project', 1, 'Overview']);
    });
  });
});
