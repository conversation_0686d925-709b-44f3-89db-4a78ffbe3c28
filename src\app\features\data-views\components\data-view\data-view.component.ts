import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { DataviewService } from '../../services/data-view.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { SearchOptions } from '../../../../_models/common.model';
import { DomSanitizer } from '@angular/platform-browser';
import { BackendResponse } from '../../../../_models/visual-data/visual-data.model';
import { ProjectData } from '../../../dashborad/models/project.model';
import { FileData } from '../../models/data-view.model';
import { findSubfolderById } from '../../services/folder.service';
import { SearchService } from '../../services/search-data.service';
import { Observable } from 'rxjs';

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_SEARCH_FILES = true;

@Component({
  selector: 'app-data-view',
  templateUrl: './data-view.component.html',
  styleUrls: ['./data-view.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewComponent implements OnInit, OnDestroy {
  //Project Details
  projectName = '';
  projectId = 0;
  project_id = localStorage.getItem('project_id');
  folder_id = localStorage.getItem('folder_id');
  initialFolderId = '';
  loadingSubfolderId: string | null = null;

  // Data Management
  data: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  dataSource = new MatTableDataSource<FileData>([]); // FileData was any before
  files: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  displayedColumns: string[] = [];
  sortOption = '';
  searchOptions: SearchOptions = {};

  // File Preview
  fileName: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  fileUrl: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  safeUrl: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  safeFileUrl: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  resolution: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  numColumns: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  numRows: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  fileSize: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  selectedFile = '';
  isImageFile = false;

  // UI State
  isTableVisible = false;
  isImageVisible = false;
  showAllChips = false;
  previewLoading = false;
  isComponentLoaded = false;

  // Popup and Expanded Panels
  isfileDeletePopup = false;
  fileID: number | string | '' = '';
  public expandedPanels: Record<string, boolean> = {};

  // Event Listener
  private beforeUnloadListener: () => void;

  constructor(
    private dataViewService: DataviewService,
    private router: Router,
    private toastrService: ToastrService,
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
    private searchService: SearchService,
  ) {
    this.beforeUnloadListener = this.setFolderIdBeforeUnload.bind(this);
  }

  //#region  Lifecycle Hooks
  ngOnInit(): void {
    this.isComponentLoaded = true;
    if (this.project_id) {
      this.projectId = Number(this.project_id);
    }
    this.loadingSubfolderId = this.folder_id;
    if (!this.projectId) this.router.navigate(['/dashboard/projects']);
    this.getProjectData(this.projectId);
    if (this.folder_id) {
      this.initialFolderId = this.folder_id;
      this.getAllDataView(this.folder_id);
      localStorage.setItem('rootFolderId', this.initialFolderId);
    }
    window.addEventListener('beforeunload', this.beforeUnloadListener);
  }

  ngOnDestroy(): void {
    this.initialFolderId = localStorage.getItem('rootFolderId') || '';
    localStorage.setItem('folder_id', this.initialFolderId);
    window.removeEventListener('beforeunload', this.beforeUnloadListener);
  }

  //#endregion

  //#region Project and Folder Management
  getProjectData(project_id: number) {
    this.dataViewService.getProjectName(project_id).subscribe(
      (response: BackendResponse<ProjectData>) => {
        this.isComponentLoaded = false;
        if (response.data.title) this.projectName = response.data.title;
      },
      error => {
        this.isComponentLoaded = false;
        console.error('Error:', error);
        if (error.status === 401 && error.statusText === 'Unauthorized') {
          this.router.navigate(['auth/login']);
        }
      },
    );
  }

  // All Data View
  getAllDataView(folderId: string): void {
    this.fetchFolderData(folderId).subscribe({
      next: response => {
        this.isComponentLoaded = false;
        if (response.status === 'success') {
          this.updateFolderDataAll(
            response.data,
            response.pagination.count,
            folderId,
          );
        } else {
          console.error('Error retrieving data:', response.message);
        }
      },
      error: error => {
        this.isComponentLoaded = false;
        this.handleFetchError(error);
      },
    });
  }

  private setFolderIdBeforeUnload(): void {
    localStorage.setItem('folder_id', this.initialFolderId);
  }

  togglePanel(folderId: number) {
    this.expandedPanels[folderId] = !this.expandedPanels[folderId];
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private fetchFolderData(folderId: string): Observable<any> {
    return this.dataViewService.getAllDataView(
      folderId,
      DEFAULT_PAGE,
      DEFAULT_PAGE_SIZE,
      DEFAULT_SEARCH_FILES,
      this.sortOption,
    );
  }

  private updateFolderDataAll(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
    count: number,
    folderId: string,
  ): void {
    localStorage.setItem('folder_id', folderId);
    this.data = [
      {
        folder_id: data.folder_id,
        folder_name: data.folder_name,
        files: data.files,
        subfolders: data.subfolders || [],
        count: count,
      },
    ];
    this.cdr.detectChanges(); // Ensure the view updates
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private handleFetchError(error: any): void {
    console.error('Error:', error);
    if (error.status === 401 && error.statusText === 'Unauthorized') {
      this.toastrService.error('Token is expired please login again!');
      this.router.navigate(['auth/login']);
    }
  }

  // Subfolder Data
  getSubfolderData(subfolderId: string): void {
    if (!subfolderId) return;

    this.fetchSubfolderData(subfolderId).subscribe({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      next: (response: { status: string; data: any; message: any }) => {
        this.isComponentLoaded = false;
        if (response.status === 'success') {
          this.updateParentFolder(response.data, subfolderId);
        } else {
          console.error('Error retrieving subfolder data:', response.message);
        }
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      error: (error: any) => {
        this.isComponentLoaded = false;
        console.error('Error fetching subfolder data:', error);
      },
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private fetchSubfolderData(subfolderId: string): Observable<any> {
    return this.dataViewService.getAllDataView(
      subfolderId,
      DEFAULT_PAGE,
      DEFAULT_PAGE_SIZE,
      DEFAULT_SEARCH_FILES,
      this.sortOption,
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private updateParentFolder(subfolderData: any, subfolderId: string): void {
    const parentFolder = findSubfolderById(this.data, subfolderId);

    if (parentFolder) {
      parentFolder.files = subfolderData.files || [];
      parentFolder.subfolders = subfolderData.subfolders || [];
      localStorage.setItem('folder_id', subfolderId);
      this.cdr.detectChanges();
    }
  }

  afterUpload() {
    const folder_id = localStorage.getItem('rootFolderId');
    if (!folder_id) this.router.navigate(['/dashboard/projects']);
    if (folder_id) {
      this.getAllDataView(folder_id);
    }
  }

  //#endregion

  //#region Search and Sorting

  // On Search Performed
  onSearchPerformed(searchOptions: SearchOptions): void {
    const folderId = Number(localStorage.getItem('project_id'));
    if (searchOptions.title) {
      this.handleTitleSearch(folderId, searchOptions.title);
    } else if (searchOptions.minDate && searchOptions.maxDate) {
      this.handleDateSearch(
        folderId,
        searchOptions.minDate,
        searchOptions.maxDate,
      );
    } else {
      this.getAllDataView(this.initialFolderId);
    }
  }

  private handleTitleSearch(folderId: number, title: string): void {
    this.dataViewService.getFilterData(folderId, title).subscribe({
      next: data => {
        const { data: processedData, files } =
          this.searchService.processSearchResponse(data);
        this.updateSearchResults(processedData, files);
      },
      error: error =>
        this.searchService.handleSearchError(
          'Error during title search',
          error,
        ),
    });
  }

  private handleDateSearch(
    folderId: number,
    minDate: string,
    maxDate: string,
  ): void {
    this.dataViewService
      .getFilteredDataByDate(folderId, minDate, maxDate)
      .subscribe({
        next: data => {
          const { data: processedData, files } =
            this.searchService.processSearchResponse(data);
          this.updateSearchResults(processedData, files);
        },
        error: error =>
          this.searchService.handleSearchError(
            'Error during date search',
            error,
          ),
      });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private updateSearchResults(processedData: any[], files: any[]): void {
    this.data = processedData;
    this.files = files;
    this.dataSource.data = files;
    this.cdr.detectChanges();
  }

  handleSortOptionChange(sortOption: string) {
    this.sortOption = sortOption;
    if (sortOption === 'initialSelect') {
      this.sortOption = '';
    }
    this.getAllDataView(this.initialFolderId);
    this.dataViewService
      .sortDataView(this.loadingSubfolderId, 1, 10, true, sortOption, 'desc')
      .subscribe(
        response => {
          if (response.status === 'success' && this.loadingSubfolderId) {
            this.isComponentLoaded = false;
            const subfolderData = response.data;
            const subfolder = findSubfolderById(
              this.data,
              this.loadingSubfolderId,
            );

            if (subfolder) {
              subfolder.files = subfolderData.files || [];
              subfolder.subfolders = subfolderData.subfolders || [];
              this.isComponentLoaded = false;
              this.cdr.detectChanges();
            }
          }
        },
        error => {
          this.isComponentLoaded = false;
          console.error('Error:', error);
        },
      );
  }

  //#endregion

  //#region  File Preview
  // Preview File
  PreviewFiles(fileID: number): void {
    this.setPreviewLoading(true);
    this.dataViewService.PreviewFiles(fileID).subscribe({
      next: response => {
        this.setPreviewLoading(false);
        if (response.status === 'success') {
          this.handlePreviewResponse(response);
          this.cdr.markForCheck();
        } else {
          console.error('Error retrieving data:', response.message);
        }
      },
      error: error => {
        console.error('Error:', error);
        this.setPreviewLoading(false);
        this.setVisibilityFlags({ table: false, image: false });
        this.cdr.markForCheck();
      },
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private handlePreviewResponse(response: any): void {
    const metadata = response.data.metadata;

    if (response.data.json_data) {
      // Handle image preview
      this.safeFileUrl = response.data.json_data.layout.images[0].source;
      this.fileName = metadata.filename;
      this.fileSize = metadata.file_size;
      this.resolution = metadata.file_size;

      this.setVisibilityFlags({ table: false, image: true });
    } else {
      // Handle table preview
      this.displayedColumns = response.data.columns;
      this.dataSource.data = response.data.data;

      this.fileName = metadata.filename;
      this.numColumns = metadata.num_columns;
      this.numRows = metadata.num_rows;
      this.fileSize = metadata.file_size;

      this.setVisibilityFlags({ table: true, image: false });
    }
  }

  private setPreviewLoading(isLoading: boolean): void {
    this.previewLoading = isLoading;
  }

  private setVisibilityFlags(flags: { table: boolean; image: boolean }): void {
    this.isTableVisible = flags.table;
    this.isImageVisible = flags.image;
  }

  showTable(fileID: number) {
    this.PreviewFiles(fileID);
  }

  viewFile(fileID: number) {
    localStorage.setItem('folder_id', this.initialFolderId);
    this.router.navigate([
      `/project/${this.projectId}/data-view/${fileID}/file`,
    ]);
  }
  //#endregion

  //#region Deletion Operations
  DeleteFile(fileID: number): void {
    this.setVisibilityFlags({ table: false, image: false });
    this.dataViewService.DeleteFiles(fileID).subscribe({
      next: response => {
        if (response.status === 'success') {
          this.isComponentLoaded = false;
          this.toastrService.success(response.message);
          this.isfileDeletePopup = false;
          this.closePopup();
          if (this.initialFolderId) this.getAllDataView(this.initialFolderId);
          this.updateFolderData(fileID);
          this.cdr.detectChanges();
        } else {
          console.error('Error deleting file:', response.message);
        }
      },
      error: error => {
        console.error('Error:', error);
      },
    });
  }

  private updateFolderData(fileID: number): void {
    this.data.forEach(folder => {
      folder.files = this.filterFiles(folder.files, fileID);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      folder.subfolders.forEach((subfolder: { files: any[] }) => {
        subfolder.files = this.filterFiles(subfolder.files, fileID);
      });
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private filterFiles(files: any[], fileID: number): any[] {
    return files.filter(file => file.id !== fileID);
  }

  confirmDelete(fileID: number | string): void {
    if (fileID !== undefined && fileID !== null) {
      this.fileID = fileID;
      this.isfileDeletePopup = true;
    } else {
      console.error('Invalid File ID');
    }
  }

  closePopup() {
    this.isfileDeletePopup = false;
  }

  DeleteFolder(folderID: number) {
    this.dataViewService.DeleteFolder(folderID).subscribe(
      response => {
        if (response.status === 'success') {
          this.isComponentLoaded = false;
          if (this.initialFolderId) this.getAllDataView(this.initialFolderId);
          this.toastrService.success(response.message);
        } else {
          console.error('Error retrieving data:', response.message);
        }
      },
      error => {
        this.toastrService.error(error);
        console.error('Error:', error);
      },
    );
  }
  //#endregion
}
