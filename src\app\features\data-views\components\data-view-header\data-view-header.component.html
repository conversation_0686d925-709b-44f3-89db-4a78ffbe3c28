<div class="flex flex-row justify-between w-full h-26 px-6">
  <div class="flex flex-col" style="max-width: 450px">
    <span
      #titleElement
      class="ellipsis"
      [matTooltip]="projectName"
      matTooltipPosition="above"
      [matTooltipDisabled]="!showTooltip"
      >{{ projectName }}</span
    >
    <h2>Data View</h2>
  </div>

  <div class="flex items-center space-x-4">
    <div class="">
      <app-search-header
        (searchPerformed)="onSearchPerformed($event)"></app-search-header>
    </div>
    <div class="hidden md:block">
      <mat-select
        [(ngModel)]="selectedSortOption"
        (selectionChange)="onSortOptionChange($event.value)"
        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 w-[230px]">
        <!-- Show the 'Select an option to sort' only if no option is selected -->
        <mat-option
          *ngIf="!selectedSortOption || selectedSortOption === 'initialSelect'"
          value="initialSelect"
          disabled>
          Select an option to sort
        </mat-option>

        <!-- Show sorting options -->
        <mat-option value="file_type">
          <span>Sorted by Data Type</span>
          <mat-icon>import_export</mat-icon>
        </mat-option>
        <mat-option value="created_at">
          <span>Sorted by Upload Date</span>
          <mat-icon>import_export</mat-icon>
        </mat-option>
        <mat-option value="file_name">
          <span>Sorted by File Name</span>
          <mat-icon>import_export</mat-icon>
        </mat-option>

        <!-- Show 'Remove sort' only if an option is selected -->
        <mat-option
          *ngIf="selectedSortOption && selectedSortOption !== 'initialSelect'"
          value="">
          Remove sort
        </mat-option>
      </mat-select>
    </div>
    <div>
      <button
        mat-flat-button
        class="whitespace-nowrap text-ellipsis"
        (click)="openModal()">
        <mat-icon>add</mat-icon>
        Add Data
      </button>
    </div>
  </div>
</div>

<app-file-upload-modal
  (isuploadModalEvent)="eventPass($event)"
  [isModalOpen]="isModalOpen">
</app-file-upload-modal>
