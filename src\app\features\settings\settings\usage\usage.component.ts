import { Component, OnInit } from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { MatIcon } from '@angular/material/icon';
import { MatProgressBar } from '@angular/material/progress-bar';
import { Clipboard } from '@angular/cdk/clipboard';
import { SettingsService } from '../../services/settings.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-usage',
  imports: [SharedModule, MatIcon, MatProgressBar],
  templateUrl: './usage.component.html',
  styleUrl: './usage.component.css',
})
export class UsageComponent implements OnInit {
  constructor(
    private clipboard: Clipboard,
    private settingService: SettingsService,
    private toasterService: ToastrService,
  ) {}

  loading = true;
  workspaceUsageList = [
    {
      title: 'Storage Usage',
      icon: 'content_copy',
      percentage: 0,
      total_allowed: '',
      total_available: '',
      total_used: '',
      onClick: (value: string) => this.copyToClipboard(value),
    },
  ];
  copyToClipboard(value: string): void {
    this.clipboard.copy(value);
  }

  ngOnInit(): void {
    this.settingService.getSubscriptionUsage().subscribe({
      next: res => {
        const storageUsage = this.workspaceUsageList.find(
          item => item.title === 'Storage Usage',
        );
        if (storageUsage) {
          Object.assign(storageUsage, {
            total_used: res.data.storage_usage.total_used,
            total_available: res.data.storage_usage.total_available,
            total_allowed: res.data.storage_usage.total_allowed,
            percentage: res.data.storage_usage.percentage * 100,
          });
          this.loading = false;
        }
      },
    });
  }
}
