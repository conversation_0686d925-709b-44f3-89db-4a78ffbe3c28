# Review

## stats

- 16k lines of code
- no unit tests

```text
wc -l src/**/*.spec.ts --> 963 total
wc -l src/**/*.ts      --> 10751 total
wc -l  src/**/*.html   --> 5323 total
```

## AuthGuard

The JWT Token should only be stored into the session storage, not the local storage.
The local storage is not secure and can be accessed by any script on the page.
The session storage is only accessible by the current page and will be cleared when the page is closed.

Better returning a UrlTree instead of navigating via the router method.

## Authentication

The authentication token header should be set with an interceptor. All services are using the same
private method to add the needed headers.

## \_models

The models should be automatically generated from the backend API. When using django ninja
there is an option to generate the OpenAPI schema. This schema can be used to generate the
client side http client and the models.

## I18N

The `global_const.ts` should be replaced by a proper i18n solution like ngx-translate or transloco.

## Local development setup

At the moment there is no local development setup.

## misc

- Form fields ids and labels are not consistent.
- best practice context- and presentation components are missing.
- very aggressive usage of `any` type.
- there are more than a few unused variables
- possible memory leaks, by subscribing to observables without unsubscribing.
- main components could be split into smaller components.
- make use of angular material modal dialog.
- plots are buggy. Editing plots does not work.

## Questions

- There is a light and dark mode variant. Is the dark mode required? How much effort was taken to make dark-mode
  consistent?

### Appendix

Ploty error:

```text
9:06:59 [vite] Error when evaluating SSR module /@fs/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js?v=e6c18a7a:
|- ReferenceError: self is not defined
    at node_modules/plotly.js-dist-min/plotly.min.js (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:13:7)
    at __require2 (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/chunk-NQ4HTGF6.js:44:50)
    at eval (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:145848:16)
    at async instantiateModule (file:///Users/<USER>/Projekte/w11k/aicu/frontend-v2/node_modules/vite/dist/node/chunks/dep-DyBnyoVI.js:52914:5)

09:06:59 [vite] Error when evaluating SSR module /chunk-7KISUBQ7.mjs:
|- ReferenceError: self is not defined
    at node_modules/plotly.js-dist-min/plotly.min.js (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:13:7)
    at __require2 (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/chunk-NQ4HTGF6.js:44:50)
    at eval (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:145848:16)
    at async instantiateModule (file:///Users/<USER>/Projekte/w11k/aicu/frontend-v2/node_modules/vite/dist/node/chunks/dep-DyBnyoVI.js:52914:5)

09:06:59 [vite] Error when evaluating SSR module /chunk-BZUTTAFL.mjs:
|- ReferenceError: self is not defined
    at node_modules/plotly.js-dist-min/plotly.min.js (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:13:7)
    at __require2 (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/chunk-NQ4HTGF6.js:44:50)
    at eval (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:145848:16)
    at async instantiateModule (file:///Users/<USER>/Projekte/w11k/aicu/frontend-v2/node_modules/vite/dist/node/chunks/dep-DyBnyoVI.js:52914:5)

ERROR ReferenceError: self is not defined
    at node_modules/plotly.js-dist-min/plotly.min.js (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:13:7)
    at __require2 (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/chunk-NQ4HTGF6.js:44:50)
    at eval (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:145848:16)
    at async instantiateModule (file:///Users/<USER>/Projekte/w11k/aicu/frontend-v2/node_modules/vite/dist/node/chunks/dep-DyBnyoVI.js:52914:5)
ERROR ReferenceError: self is not defined
    at node_modules/plotly.js-dist-min/plotly.min.js (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:13:7)
    at __require2 (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/chunk-NQ4HTGF6.js:44:50)
    at eval (/Users/<USER>/Projekte/w11k/aicu/frontend-v2/.angular/cache/18.2.10/AICU/vite/deps_ssr/plotly__js-dist-min.js:145848:16)
    at async instantiateModule (file:///Users/<USER>/Projekte/w11k/aicu/frontend-v2/node_modules/vite/dist/node/chunks/dep-DyBnyoVI.js:52914:5)
```

Files sorted by lines top 20:

```text
❯ wc -l src/**/*.ts src/**/*.html | sort -r | head -21
     964 src/app/components/training/training/training.component.html
     806 src/app/components/data-views/file-upload-modal/file-upload-modal.component.ts
     776 src/app/components/data-views/files/files.component.ts
     750 src/app/components/visual-data/plot/edit-plot-data.component.ts
     579 src/app/components/data-version/data-version/data-version.component.ts
     560 src/app/components/visual-data/plot/edit-plot-data.component.html
     537 src/app/services/dataview.service.ts
     478 src/app/components/visual-data/visual-data/visual-data.component.ts
     475 src/app/components/data-views/files/files.component.html
     404 src/app/components/data-views/data-view/data-view.component.ts
     298 src/app/components/dashborad/projects/projects.component.ts
     286 src/app/components/training/training-header/training-header.component.html
     254 src/app/components/visual-data/filter-modal/filter-modal.component.html
     244 src/app/components/visual-data/filter-modal/filter-modal.component.ts
     242 src/app/components/overview/overview.component.ts
     241 src/app/components/settings/settings/settings.component.html
     240 src/app/components/data-views/file-upload-modal/file-upload-modal.component.html
     230 src/app/_models/data-type.enum.ts
     223 src/app/services/project.service.ts
     205 src/app/components/visual-data/visual-data/visual-data.component.html
```
