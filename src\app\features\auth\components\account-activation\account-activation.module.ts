import { NgModule } from '@angular/core';
import { CommonModule, JsonPipe } from '@angular/common';
import { AccountActivationComponent } from './account-activation.component';
import { AccountActivationRoutingModule } from './account-activation-routing.module';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../../../shared/shared.module';

@NgModule({
  declarations: [AccountActivationComponent],
  imports: [
    CommonModule,
    AccountActivationRoutingModule,
    MatButtonModule,
    FormsModule,
    JsonPipe,
    SharedModule,
  ],
  exports: [AccountActivationComponent],
})
export class AccountActivationModule {}
