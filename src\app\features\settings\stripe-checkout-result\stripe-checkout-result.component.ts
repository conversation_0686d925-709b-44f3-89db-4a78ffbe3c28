import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { StripeService } from '../services/stripe.service';
import { PlanSubscription } from '../../../_models/plan.model';
import confetti from 'canvas-confetti';

@Component({
  selector: 'app-stripe-checkout-result',
  imports: [CommonModule, RouterModule],
  templateUrl: './stripe-checkout-result.component.html',
  styleUrl: './stripe-checkout-result.component.css',
})
export class StripeCheckoutResultComponent implements OnInit, OnDestroy {
  // Store the interval ID for cleanup
  private confettiInterval: ReturnType<typeof setInterval> | null = null;
  // session id
  sessionId: string | null = null;

  // the subscripton plan details
  subscription: PlanSubscription | null = null;

  constructor(
    private route: ActivatedRoute,
    private stripeService: StripeService,
  ) {}

  ngOnInit(): void {
    this.sessionId = this.route.snapshot.queryParamMap.get('session_id');
    if (this.sessionId) {
      this.verifySubscriptionStatus();
    } else {
      this.reportToSupport();
    }
  }

  /**
   * To verfify the subscription status.
   * @returns
   */
  async verifySubscriptionStatus(): Promise<void> {
    try {
      const status = await this.stripeService.verifySubscriptionStatus();
      if (status) {
        this.subscription = status;
        if (status.is_active) {
          // Small delay to ensure the DOM is ready
          setTimeout(() => this.triggerConfetti(), 500);
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * To report the issue to the support.
   */
  async reportToSupport(): Promise<void> {
    try {
      await this.stripeService.reportToSupport();
    } catch (error) {
      console.error(error);
    }
  }

  triggerConfetti(): void {
    const randomInRange = (min: number, max: number) => {
      return Math.random() * (max - min) + min;
    };

    const rainConfig = {
      startVelocity: 15,
      spread: 20,
      ticks: 500,
      zIndex: -1,
      gravity: 1.2,
      angle: 90,
      drift: 0.2,
      scalar: 0.8,
    };

    // Create a single, continuous rain effect with infinite duration
    for (let i = 0; i < 5; i++) {
      confetti({
        ...rainConfig,
        particleCount: 40,
        origin: { x: randomInRange(0.1, 0.9), y: 0 },
      });
    }

    // Store the interval ID so we can clear it when component is destroyed
    this.confettiInterval = setInterval(() => {
      const particleCount = 25;

      for (let i = 0; i < 5; i++) {
        confetti({
          ...rainConfig,
          particleCount: particleCount / 5,
          origin: { x: randomInRange(0.1, 0.9), y: 0 },
        });
      }
    }, 200);
  }

  /**
   * Clean up resources when the component is destroyed
   */
  ngOnDestroy(): void {
    // Clear the confetti interval to prevent memory leaks
    if (this.confettiInterval) {
      clearInterval(this.confettiInterval);
    }
  }
}
