import { Injectable } from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { Router } from '@angular/router';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private router: Router) {}

  intercept(
    req: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON>and<PERSON>,
  ): Observable<HttpEvent<unknown>> {
    const token = localStorage.getItem('access_token');

    if (token) {
      if (req.url.includes('aicu-frankfurt-s3-backend.s3.amazonaws.com')) {
        return next.handle(req);
      }
      const cloned = req.clone({
        headers: req.headers.set('Authorization', 'Bearer ' + token),
      });
      return next.handle(cloned).pipe(
        tap({
          error: (err: unknown) => {
            if (err instanceof HttpErrorResponse) {
              if (err.status === 401) {
                localStorage.clear();
                this.router.navigate(['auth/login']);
              }
            }
          },
        }),
      );
    } else {
      return next.handle(req);
    }
  }
}
