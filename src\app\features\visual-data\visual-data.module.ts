import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VisualDataRoutingModule } from './visual-data-routing.module';
import { PlotDataComponent } from './plot/plot-data.component';
import { VisualDataComponent } from './visual-data/visual-data.component';
import { Mat<PERSON><PERSON>on, MatIconButton } from '@angular/material/button';
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCard<PERSON>ooter,
  MatCardHeader,
  MatCardSubtitle,
  MatCardTitle,
} from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { GraphWorkComponent } from '../../shared/components/graph-work/graph-work.component';
import { SharedModule } from '../../shared/shared.module';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormField, Mat<PERSON>abel } from '@angular/material/form-field';
import {
  MatOptgroup,
  MatOption,
  MatSelect,
  MatSelectTrigger,
} from '@angular/material/select';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MatButtonToggle,
  MatButtonToggleGroup,
} from '@angular/material/button-toggle';
import { PlotDataSettingsComponent } from './settings/plot-data-settings.component';
import { MatExpansionModule } from '@angular/material/expansion';
import {
  MatChip,
  MatChipListbox,
  MatChipOption,
} from '@angular/material/chips';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { FilterModalComponent } from './filter-modal/filter-modal.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { GroupByPipe } from './plot/pipe';
import { PlotDescriptionComponent } from './plot-description/plot-description.component';
import { MatInput } from '@angular/material/input';
import { ColorPaletteComponent } from './settings/color-palette/color-palette.component';
import { ToolWidgetComponent } from './settings/tool-widget/tool-widget.component';
import { ChartContentToolbarComponent } from './settings/tool-widget/chart-content-toolbar/chart-content-toolbar.component';
import { ColorPickerToolbarComponent } from './settings/tool-widget/color-picker-toolbar/color-picker-toolbar.component';
import { GridsterModule } from 'angular-gridster2';
import { ColorOptionComponent } from './settings/color-option/color-option.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { EditPlotDataComponent } from './edit-plot/edit-plot-data.component';
import { MatTooltip } from '@angular/material/tooltip';

@NgModule({
  declarations: [
    PlotDataComponent,
    VisualDataComponent,
    PlotDataSettingsComponent,
    FilterModalComponent,
    GroupByPipe,
    PlotDescriptionComponent,
    EditPlotDataComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    VisualDataRoutingModule,
    SharedModule,
    PlotlyViaCDNModule,
    NgSelectModule,
    GraphWorkComponent,
    GridsterModule,

    // Angular Material Module Imports
    MatMenu,
    MatMenuItem,
    MatIcon,
    MatButton,
    MatCard,
    MatCardActions,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatCardSubtitle,
    MatCardFooter,
    MatMenuTrigger,
    MatCardFooter,
    MatCheckbox,
    MatFormField,
    MatLabel,
    MatSelect,
    MatOption,
    MatRadioGroup,
    MatRadioButton,
    MatOptgroup,
    MatButtonToggleGroup,
    MatButtonToggle,
    MatSelectTrigger,
    MatExpansionModule,
    MatPaginatorModule,
    MatChip,
    MatTabGroup,
    MatTab,
    MatIconButton,
    MatInput,
    ColorPaletteComponent,
    ToolWidgetComponent,
    ChartContentToolbarComponent,
    ColorPickerToolbarComponent,
    MatMenuItem,
    MatChipOption,
    MatChipListbox,
    ColorOptionComponent,
    MatTooltip,
  ],
})
export class VisualDataModule {}
