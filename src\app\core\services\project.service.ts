import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../env/env';
import { ProjectData } from '../../features/dashborad/models/project.model';
import { BackendResponse } from '../../_models/visual-data/visual-data.model';

@Injectable({
  providedIn: 'root',
})
export class ProjectService {
  constructor(private http: HttpClient) {}

  getProjectName(projectID: number) {
    return this.http.get<BackendResponse<ProjectData>>(
      `${environment.apiUrl}projects/projects/${projectID}/?list_files=false`,
      { headers: this.getHeaders() },
    );
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }
}
