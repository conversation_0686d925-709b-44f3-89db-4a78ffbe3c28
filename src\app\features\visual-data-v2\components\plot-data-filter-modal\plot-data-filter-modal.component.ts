import { Component, inject, Inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { PlotFilterDataUI } from '../../models/plot.model';
import {
  ColumnChoices,
  FilterData,
  FilterOperation,
} from '../../../data-views/models/data-view.model';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';

// TO-DO : Move this Material related imports to Module
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  ColumnValues,
  FilterOperators,
} from '../../../data-views/models/data-view.model';
import { PlotService } from '../../services/plot.service';
import { BackendResponse } from '../../../../_models/visual-data/visual-data.model';
import { visualDataStore } from '../../store/visual-data.store';

type ColumnConfig = FormGroup<{
  columnHeader: FormControl<string>;
  operator: FormControl<string>;
  value: FormGroup<{
    singleValue: FormControl<string>;
    minValue: FormControl<number>;
    maxValue: FormControl<number>;
  }>;
  logicOperator: FormControl<string>;
  isExistingFilter: FormControl<boolean>;
}>;

type FilterPlotForm = FormGroup<{
  columnFilters: FormArray<ColumnConfig>;
}>;

@Component({
  selector: 'app-plot-data-filter-modal',
  imports: [
    MatAutocompleteModule,
    MatDialogModule,
    MatIconModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './plot-data-filter-modal.component.html',
  styleUrl: './plot-data-filter-modal.component.css',
})
export class PlotDataFilterModalComponent implements OnInit {
  filterPlotDataForm!: FilterPlotForm;
  availableOperators: FilterOperators | null = null;
  availableValues: ColumnValues | null = null;
  columnChoicesTransformed: [string, string[]][] = [];
  existingFilterData: FilterOperation[] = [];
  isLoading = false;
  visualDataStore = inject(visualDataStore);

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: PlotFilterDataUI,
    private popupRef: MatDialogRef<PlotDataFilterModalComponent>,
    private fb: FormBuilder,
    private plotService: PlotService,
  ) {}

  ngOnInit(): void {
    this.loadColumnDataAndCreateForm();
  }

  async loadColumnDataAndCreateForm() {
    let columnChoicesApiRes;
    let columnValuesApiRes;
    let exisitingFilterApiRes: BackendResponse<FilterData> | null = null;

    try {
      this.isLoading = true;

      // API CALLS
      columnChoicesApiRes = await this.plotService.getColumnChoiceAsync(
        this.data.fileId,
      );
      columnValuesApiRes = await this.plotService.getColumnValuesAsync(
        this.data.fileId,
      );

      if (this.data.filterInstanceId !== null) {
        exisitingFilterApiRes = await this.plotService.getFilterAsync(
          this.data.filterInstanceId,
        );
      }

      //STORE COLUMN CHOICES AND VALUES DATA IN LOCAL VARIABLES
      this.columnChoicesTransformed = this.transformColumnChoiceResponse(
        columnChoicesApiRes?.data?.data,
      );
      this.availableValues = columnValuesApiRes.data.values;
      this.availableOperators = columnValuesApiRes.data.operators;

      //EXISTIN FILTER:
      this.existingFilterData =
        exisitingFilterApiRes?.data?.filter_operations || [];

      // BASED ON THE DATA LOADED FROM SERVER, UPDATE THE FORM
      let columnFiltersFormArray: ColumnConfig[];

      if (this.existingFilterData?.length > 0) {
        columnFiltersFormArray = this.existingFilterData.map(
          (filter: FilterOperation) => {
            return this.generateColumnFilter(filter);
          },
        );
      } else {
        columnFiltersFormArray = [this.generateColumnFilter(null)];
      }

      this.filterPlotDataForm = this.fb.group({
        columnFilters: this.fb.array<ColumnConfig>(columnFiltersFormArray),
      }) as FilterPlotForm;
    } catch (err) {
      console.error('Error fetching data:', err);
      return;
    } finally {
      this.isLoading = false;
    }
  }

  transformColumnChoiceResponse(
    columnChoices: ColumnChoices['data'],
  ): [string, string[]][] {
    const transformedData: [string, string[]][] = [];
    for (const category in columnChoices) {
      const key = category as keyof typeof columnChoices;
      const choices = columnChoices[key];
      if (
        Array.isArray(choices) &&
        choices.every(choice => typeof choice === 'string')
      ) {
        transformedData.push([key, choices as string[]]);
      }
    }
    return transformedData;
  }

  generateColumnFilter(columnInfo: FilterOperation | null): ColumnConfig {
    let group: ColumnConfig;
    if (columnInfo === null) {
      group = this.fb.group({
        columnHeader: this.fb.control('', Validators.required),
        operator: this.fb.control(
          { value: '', disabled: false },
          Validators.required,
        ),
        value: this.fb.group({
          singleValue: this.fb.control('', Validators.required),
          minValue: this.fb.control(0),
          maxValue: this.fb.control(10),
        }),
        logicOperator: this.fb.control('AND', Validators.required),
        isExistingFilter: this.fb.control(false),
      }) as ColumnConfig;
    } else {
      let singleValue = '';
      let minValue = 0;
      let maxValue = 10;
      if (columnInfo.filter_operator === 'between') {
        //"filter_value": "3, 500"
        [minValue, maxValue] = columnInfo.filter_value.split(',').map(Number);
      } else {
        //"filter_value": "D",
        singleValue = columnInfo.filter_value;
      }

      group = this.fb.group({
        columnHeader: this.fb.control(
          columnInfo.filter_column,
          Validators.required,
        ),
        operator: this.fb.control(
          { value: columnInfo.filter_operator, disabled: false },
          Validators.required,
        ),
        value: this.fb.group({
          singleValue: this.fb.control(singleValue, Validators.required),
          minValue: this.fb.control(minValue),
          maxValue: this.fb.control(maxValue),
        }),
        logicOperator: this.fb.control(columnInfo.logic, Validators.required),
        isExistingFilter: this.fb.control(true),
      }) as ColumnConfig;
    }
    return group;
  }
  closeModal() {
    this.popupRef.close();
  }

  addColumnFilter() {
    console.log(this.filterPlotDataForm);
    this.filterPlotDataForm.controls.columnFilters.push(
      this.generateColumnFilter(null),
    );
  }

  removeColumnFilter(index: number) {
    this.filterPlotDataForm.controls.columnFilters.removeAt(index);
  }

  getCategoryBasedOnSelectedColumnHeader(columnHeader: string) {
    for (const [category, choices] of this.columnChoicesTransformed) {
      if (choices.includes(columnHeader)) {
        return category;
      }
    }
    return null;
  }

  getOperatorBasedSelectedColumnHeader(colummHeader: string) {
    const category = this.getCategoryBasedOnSelectedColumnHeader(colummHeader);
    if (category && this.availableOperators) {
      return Object.keys(this.availableOperators[category]);
    }
    return [];
  }

  _getOptionsforCatergoricalColumn(colummHeader: string) {
    const categoriesValues = this.availableValues?.Categories;
    const options = categoriesValues?.find(
      category => category.name === colummHeader,
    )?.options;
    return options || [];
  }

  _createInputForAssociateFilterAPI() {
    const filters = this.filterPlotDataForm.value.columnFilters?.map(filter => {
      const logicOperator = filter.logicOperator;
      const selectedColumn = filter.columnHeader;
      const operator = filter.operator;
      const value =
        filter.operator === 'between'
          ? `${filter.value?.minValue}, ${filter.value?.maxValue}`
          : filter.value?.singleValue;

      return {
        filter_column: selectedColumn,
        filter_operator: operator,
        filter_value: value,
        logic: logicOperator,
      };
    });
    const filterData = JSON.stringify({ filters }, null, 2);
    return filterData;
  }

  async onSubmit() {
    try {
      this.isLoading = true;
      const filterData = this._createInputForAssociateFilterAPI();
      await this.plotService.userPlotAssociateFilterAsync(
        this.data.plotId,
        filterData,
      );

      /*
      TO-DO:
      Talk with Shivam to get API for single plot, instead of loading all plots data
      */
      this.visualDataStore.loadUserPlotData({ currentPage: 0 });
    } catch (err) {
      // TO-DO : Show error message in toaster
      console.error('Error in onSubmit:', err);
      this.isLoading = false;
    } finally {
      this.isLoading = false;
      this.closeModal();
    }
  }

  async removeAllFiltersFromPlot() {
    try {
      this.isLoading = true;
      await this.plotService.removeFilterAsync(this.data.filterInstanceId);

      /*
      TO-DO:
      Talk with Shivam to get API for single plot, instead of loading all plots data
      */
      this.visualDataStore.loadUserPlotData({ currentPage: 0 });
    } catch (err) {
      // TO-DO : Show error message in toaster
      console.error('Error in Removing all Fillters:', err);
      this.isLoading = false;
    } finally {
      this.isLoading = false;
      this.closeModal();
    }
  }
}
