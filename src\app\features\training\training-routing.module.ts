import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TrainingComponent } from './training/training.component';
import { TrainingResultComponent } from './training-result/training-result.component';
import { TrainingLangingPageComponent } from './training-langing-page/training-langing-page.component';
import { TrainingResultsComponent } from './training-results/training-results.component';
const routes: Routes = [
  {
    path: 'training',
    component: TrainingLangingPageComponent,
  },
  {
    path: 'training-project',
    component: TrainingComponent,
  },
  {
    path: 'training-result',
    component: TrainingResultComponent,
  },
  {
    path: 'training-results/:id',
    component: TrainingResultsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TrainingRoutingModule {}
