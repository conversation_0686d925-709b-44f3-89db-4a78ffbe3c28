import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProjectsComponent } from './components/projects/projects.component';

const routes: Routes = [
  {
    path: 'projects',
    component: ProjectsComponent,
    pathMatch: 'full',
  },
  { path: '', redirectTo: 'projects', pathMatch: 'full' },
  { path: 'overview', redirectTo: 'overview', pathMatch: 'full' },
  { path: 'data-version', redirectTo: 'data-version', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}
