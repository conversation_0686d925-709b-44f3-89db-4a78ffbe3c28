import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DataVersionComponent } from './data-version.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { DataversionService } from '../../../../services/dataversion.service';
import { throwError } from 'rxjs';
import { MatTabChangeEvent } from '@angular/material/tabs';

describe('DataVersionComponent', () => {
  let component: DataVersionComponent;
  let fixture: ComponentFixture<DataVersionComponent>;
  let toastrService: jest.Mocked<ToastrService>;
  let dataversionService: jest.Mocked<DataversionService>;

  beforeEach(async () => {
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
    };

    const dataversionServiceMock = {
      getDatasetVersion: jest.fn(),
      addDataVersionName: jest.fn(),
      deleteDataVersion: jest.fn(),
      updateDataVersion: jest.fn(),
      getPipeLineSteps: jest.fn(),
      getUserPipeLineDataById: jest.fn(),
      deleteUserPipleLineSteps: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [DataVersionComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: DataversionService, useValue: dataversionServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DataVersionComponent);
    component = fixture.componentInstance;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    dataversionService = TestBed.inject(
      DataversionService,
    ) as jest.Mocked<DataversionService>;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('deleteDataVersion', () => {
    it('should show error if deleting a dataset version fails', () => {
      dataversionService.deleteDataVersion.mockReturnValue(
        throwError(() => new Error('Error')),
      );

      component.deleteDataVersion(1);

      expect(toastrService.error).toHaveBeenCalledWith(
        'An error occurred while deleting the dataset version. Please try again.',
      );
    });
  });

  describe('openDataversionModal', () => {
    it('should open modal if versions are less than 5', () => {
      component.dataVersions = [
        {
          id: 1,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
        {
          id: 2,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
      ];
      component.openDataversionModal();
      expect(component.isdataVersionModal).toBe(true);
    });

    it('should show error if versions exceed limit', () => {
      component.dataVersions = [
        {
          id: 1,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
        {
          id: 2,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
        {
          id: 3,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
        {
          id: 4,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
        {
          id: 5,
          version_number: 0,
          name: '',
          created_at: '',
          project: 0,
          user_pipeline: [],
        },
      ];
      component.openDataversionModal();
      expect(toastrService.error).toHaveBeenCalledWith(
        'Limit exceeded: You can only add up to 5 data Versions.',
      );
      expect(component.isdataVersionModal).toBe(false);
    });
  });

  describe('onTabChange', () => {
    it('should update currentTab and reset filtered data', () => {
      jest.spyOn(component, 'resetFilteredData');
      const event = { index: 1 } as MatTabChangeEvent;

      component.onTabChange(event);

      expect(component.currentTab).toBe('image');
      expect(component.resetFilteredData).toHaveBeenCalledWith('image');
    });
  });

  describe('saveSelections', () => {
    it('should show error if pipeline name is empty', () => {
      component.pipelineName = '';
      component.saveSelections();
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please enter a valid pipeline name and complete all fields before saving.',
      );
    });

    it('should show error if no steps are selected', () => {
      component.pipelineName = 'Pipeline 1';
      component.selectedSteps = [];
      component.saveSelections();
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please add atleast one step.',
      );
    });
  });
});
