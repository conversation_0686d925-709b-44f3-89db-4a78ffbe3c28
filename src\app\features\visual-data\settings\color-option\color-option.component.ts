import {
  Component,
  computed,
  effect,
  ElementRef,
  input,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { ColorPaletteComponent } from '../color-palette/color-palette.component';
import { MatError, MatInput } from '@angular/material/input';
import { ColorPalette } from '../../../../_models/visual-data/visual-data.model';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { g_const } from '../../../../_utility/global_const';

@Component({
  selector: 'app-color-option',
  imports: [ColorPaletteComponent, MatInput, ReactiveFormsModule, MatError],
  templateUrl: './color-option.component.html',
  styleUrl: './color-option.component.css',
})
export class ColorOptionComponent {
  palette = input.required<ColorPalette>();
  colorPaletteSelected = input<boolean>(false);
  editMode = input.required<boolean>();
  colorPaletteNameEdited = signal<boolean>(false);
  openColorPicker = output<void>();
  saveChangedName = output<string>();
  paletteNameInput = viewChild<ElementRef>('paletteNameInput');
  paletteNameControl = computed(
    () => new FormControl(this.palette().name, Validators.required),
  );
  constructor() {
    effect(() => {
      if (this.paletteNameInput() !== null) {
        this.paletteNameInput()?.nativeElement.focus();
      }
    });
  }

  activateNameEditMode(event: Event) {
    event.stopPropagation();
    this.colorPaletteNameEdited.set(true);
  }

  openColorPickerClicked() {
    this.openColorPicker.emit();
  }

  saveName(event: KeyboardEvent) {
    event.stopPropagation();
    const paletteName = this.paletteNameControl().value;
    if (
      event.key === 'Enter' &&
      paletteName !== null &&
      this.paletteNameControl().valid
    ) {
      this.saveChangedName.emit(paletteName);
      this.colorPaletteNameEdited.set(false);
    }
  }

  deactivatePaletteNameInput() {
    this.colorPaletteNameEdited.set(false);
  }

  protected readonly g_const = g_const;
}
