import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataViewComponent } from './components/data-view/data-view.component';
import { DataViewRoutingModule } from './data-view-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FilesComponent } from './components/files/files.component';
import { FileHeaderComponent } from './components/file-header/file-header.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatOptionModule } from '@angular/material/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatRadioButton, MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { FileUploadModalComponent } from './components/file-upload-modal/file-upload-modal.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { NgxFileDropModule } from 'ngx-file-drop';
import { DataViewHeaderComponent } from './components/data-view-header/data-view-header.component';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { ImagesComponent } from './components/images/images.component';
import { GraphWorkComponent } from '../../shared/components/graph-work/graph-work.component';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { MatChipsModule } from '@angular/material/chips';
import { FolderPanelComponent } from './components/folder-panel/folder-panel.component';
import { ImagesStatisticsComponent } from './components/images/images-statistics/images-statistics.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ReplaceUnderscorePipe } from './pipes/replace-underscore.pipe';
import { CapitalizePipe } from './pipes/capitalize.pipe';
import { BuyPlanModalComponent } from './components/buy-plan-modal/buy-plan-modal.component';

@NgModule({
  declarations: [
    FilesComponent,
    FileHeaderComponent,
    FileUploadModalComponent,
    DataViewHeaderComponent,
    DataViewComponent,
    ImagesComponent,
    FolderPanelComponent,
    ImagesStatisticsComponent,
    BuyPlanModalComponent,
  ],
  imports: [
    CommonModule,
    ReplaceUnderscorePipe,
    CapitalizePipe,
    DataViewRoutingModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatInputModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    NgxFileDropModule,
    MatIcon,
    MatButtonModule,
    MatButtonToggleModule,
    MatFormField,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    FormsModule,
    MatDialogModule,
    MatPaginatorModule,
    MatRadioButton,
    MatOptionModule,
    MatTableModule,
    MatMenuModule,
    MatChipsModule,
    NgxFileDropModule,
    MatExpansionModule,
    MatRadioModule,
    SharedModule,
    ReactiveFormsModule,
    GraphWorkComponent,
    PlotlyViaCDNModule,
  ],
  exports: [
    ReplaceUnderscorePipe,
    CapitalizePipe,
    FilesComponent,
    FileHeaderComponent,
    FileUploadModalComponent,
    DataViewHeaderComponent,
    DataViewComponent,
    ImagesComponent,
    GraphWorkComponent,
    FolderPanelComponent,
    BuyPlanModalComponent,
  ],
})
export class DataViewModule {}
