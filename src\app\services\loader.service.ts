import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoaderService {
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  public isCommonLoaderRequired = true;

  startLoading(): void {
    if (this.isCommonLoaderRequired) {
      this.isLoadingSubject.next(true);
    }
  }

  stopLoading(): void {
    this.isLoadingSubject.next(false);
  }

  toggleLoading(): void {
    this.isLoadingSubject.next(!this.isLoadingSubject.value);
  }

  get isLoading$(): Observable<boolean> {
    return this.isLoadingSubject.asObservable();
  }
}
