<div align="center">
  <p>
    <a align="center" href="" target="_blank">
      <img
        width="850"
        src="docs\AICU_banner.png"
      >
    </a>
  </p>
  <br>

[backend](https://github.com/AICU-HEALTH/backend_apis/) | [frontend](https://github.com/AICU-HEALTH/frontend-v2) | [api collection](https://github.com/AICU-HEALTH/api_collection) | [design](https://www.figma.com/design/0znoiYntDR9wxRUN3k6hba/AICU-UI?node-id=1564-41204&m=dev) | [mlmodels](https://github.com/AICU-HEALTH/ml-training) | [dlmodels](https://github.com/AICU-HEALTH/training-cluster-ecs)

  <br>

  <div align="center">
      <a href="https://cdn.worldvectorlogo.com/logos/angular-3.svg">
          <img
            src="https://cdn.worldvectorlogo.com/logos/angular-3.svg"
            width="25%"
          />
      </a>
  </div>
</div>

  <br>

---

# 👋 General Development Practice

- All the individual features, bug-fixes, etc., are done in a separate branch.
- This separate branch is taken out from the latest `test` branch.
- Develop on the new branch, TEST, test, TesT! the application before raising the PR --> `test` branch.
  > **Naming Convention for Branches**: `\<developer-name>\<feature-name>`

## 🚀 Steps for Development

1. **Create a Feature Branch from `dev`**

   - Begin your task by creating a feature branch from the `dev` branch.

   ```sh
   git checkout dev
   git pull origin dev
   git checkout -b \<developer-name>\<feature-name>
   ```

2. **Develop, Test, and Push Changes**

   - Develop and thoroughly test your feature in the feature branch.

   ```sh
   git push origin \<developer-name>\<feature-name>
   ```

   - After completing the tas merge `dev` branch in your `\<developer-name>\<feature-name>` branch.
   - Ensure AGAIN everything works as expected with the `dev` merge.

3. **Raise a Pull Request (PR)**

   - After thorough testing submit a PR from `\<developer-name>\<feature-name>` to the `dev` branch.
   - This submits your changes for review

4. **Verify on `dev`**

   - Ensure everything works as expected on the deployed`dev` environment.
   - The Frontend/ Backend Developers should be updated about the changes.
   - Make sure the Postman collection is available for everyone and you do not overwrite someone elses changes
   - If the concept needs adaptation make sure to work with Frontend & Backend (& Design)
   - Ensure we have documentation for how to test this new feature and test cases

5. **Test on `test` and then Users access the feature once in `prod` Environment**
   - Next the merge and verification is repeated with test and once successfull with the prod branch.
   - Make sure the Frontend & Backend work togetehr on teh changes

---

## ☁️ Branch and Environment Mapping

### Backend

<div align="center" style="background-color:#2D2D2D; padding: 20px; border-radius: 10px;">
  <img src="https://upload.wikimedia.org/wikipedia/commons/7/75/Django_logo.svg" alt="Django" width="120" style="margin: 10px; vertical-align: middle;" />
  <img src="https://docs.celeryq.dev/en/stable/_static/celery_512.png" alt="Celery" width="120" style="margin: 10px; vertical-align: middle;" />
  <img src="https://www.rabbitmq.com/img/rabbitmq-logo-with-name.svg" alt="RabbitMQ" width="120" style="margin: 10px; vertical-align: middle;" />
</div>

| Branch | Environment URL                                    |
| ------ | -------------------------------------------------- |
| `dev`  | [Dev Backend](https://dev-backend.aicuflow.com/)   |
| `test` | [Test Backend](https://test-backend.aicuflow.com/) |
| `prod` | [Prod Backend](https://prod-backend.aicuflow.com/) |

#### **How the application is build in the backend**

In our CI/CD pipelines, the process generally follows these steps:

1. **PreBuild**: Setup steps such as installing dependencies or configuring variables.
2. **Build**: Compilation or packaging of the application.
3. **PreDeploy**: Preparation tasks before deployment.
4. **Deploy**: Deploying the application to the server/hosting infrastructure.
5. **PostDeploy**: Cleanup tasks, notifications, or validation (e.g., smoke tests).

### Frontend

<div align="center" style="background-color:#2D2D2D; padding: 20px; border-radius: 10px;">
  <img src="https://cdn.worldvectorlogo.com/logos/angular-3.svg" alt="Angular" width="120" style="margin: 10px; vertical-align: middle;" />
</div>

| Branch | Environment URL                                     |
| ------ | --------------------------------------------------- |
| `dev`  | [Dev Frontend](http://localhost:4200/)              |
| `test` | [Test Frontend](http://platform.test.aicuflow.com/) |
| `prod` | [Prod Frontend](https://platform.aicuflow.com/)     |

#### **Technology Stack**

| Technology       | Version                                                        | Documentation                                    |
| ---------------- | -------------------------------------------------------------- | ------------------------------------------------ |
| **Frontend**     | [Angular 18.2.3](https://angular.dev/)                         | [Angular Docs](https://angular.dev/)             |
| **Node.js**      | [Node.js 18.18.0](https://nodejs.org/en/blog/release/v18.18.0) | [Node.js Docs](https://nodejs.org/en/)           |
| **UI Prototype** | [TailwindCSS 3.4.10](https://tailwindcss.com/)                 | [TailwindCSS Docs](https://tailwindcss.com/)     |
| **UI System**    | [Angular Material 18.2.5](https://material.angular.io/)        | [Angular Material](https://material.angular.io/) |

#### **How the frontend of the application is build and deployed**

We need the production setting otherwise this is server-side rendering and we need regular client-side Angular application for static hosting.

For the frontend to point to different deplyoed backends, we run the build with different configurations that are specified under angular.json.

```bash
ng build --configuration production
ng build --configuration test
```

and then the ready application is just uploaded to an S3 Bucket on AWS:

```bash
aws s3 sync ./dist/aicu/browser s3://platform.aicuflow.com --region eu-central-1 # prod

aws s3 sync ./dist/aicu/browser s3://platform.test.aicuflow.com --region eu-central-1 # test
```

<br>

This structure ensures a seamless workflow and proper testing at each stage of the development lifecycle. 🚀

<br>

---

# ✅ What's included

> 🧹 Dependencies

- [x] Angular : 18.2.3
- [x] Angular CLI : 18.2.3
- [x] Tailwindcss : 3.4.10
- [x] NgRx Store : 18.0.2
- [x] NgRx Effects : 18.0.2

> 🦸 Features

- [x] Routing
- [x] Responsive Layout
- [x] Dynamic Common Components
- [x] Services
- [x] Reactive Form
- [x] Search / Grid / Pagination
- [x] RxJS
- [x] NgRx Store
- [x] Plotly Charts
- [x] Dynamic common forms are used
- [x] Used constants for the static texts

<br>

<br>

# 🚀 Installation & Start Guide

Follow these steps to install and run the project locally:

## Prerequisites

### 1. **Install [Node.js](https://nodejs.org/en) (v18+)**:

- Verify the installation:

```bash
node -v
npm -v
```

### 2. **Install [Angular CLI](https://nodejs.org/en) globally**:

```bash
npm install -g @angular/cli@18.2.3
```

### 2. **Verify the Angular CLI version**:

```bash
ng version
```

## Steps to Setup the Project

### 1. Clone the repository:

```bash
git clone https://github.com/AICU-HEALTH/frontend-v2.git
cd frontend-v2
```

### 2. Install Project Dependencies:

```bash
npm cache clean --force
npm install
npm update
npm install -g npm@latest
npm install -f
```

> If you encounter errors during npm install, use:

```bash
 npm install --legacy-peer-deps
```

### 3. Run the Development Server:

```bash
ng s
ng serve
```

### 4. Open your browser and navigate to: [http://localhost:4200](http://localhost:4200)

```bash
ng s
ng serve
```

### 7. Production Build [http://localhost:4000](http://localhost:4000)

To create a production build and serve it:

```bash
ng build --configuration production #prod
ng build --configuration test --verbose  #test
```

```bash
aws s3 sync ./dist/aicu/browser s3://platform.aicuflow.com --region eu-central-1   # prod

aws s3 sync ./dist/aicu/browser s3://platform.test.aicuflow.com --region eu-central-1  # test
```

<br>
<br>

# 🐞 Runing unit tests with Jest

Run npx jest to execute the unit tests via Jest.

- `npx jest`
- `npx jest --coverage`
- `npx jest src/app/components/auth/login --debug`

OLD with karma:

Run ng test to execute the unit tests via Karma.

- `ng test`

If you want to generate a code coverage report, run the command with the flag --code-coverage.

- `ng test --code-coverage`

Run the tests for one specific component:

- `ng test --code-coverage --include='**/login.component.spec.ts'`

<br>
<br>

# 👀 Runing linting

To check your code for style and syntax errors, use ESLint with the Angular CLI. This ensures your code follows best practices and maintains a consistent style across the project.

This command will check your TypeScript and HTML files against the configured ESLint rules and display any issues in the console.

- `ng lint`

To let ESLint automatically correct fixable issues, add the --fix flag:

- `ng lint --fix`

Configuration: Linting rules are set in the eslint.config.js file located in the root directory of the project.
Prettier Integration: The linter will also check for consistent code formatting.

<br>
<br>

```bash
  "scripts": {
    "ng": "ng",
    "start": "ng serve",
    "build": "ng build --configuration production",
    "watch": "ng build --watch --configuration development",
    "test": "jest",
    "prettier": "npx prettier --write .",
    "prepare": "husky",
    "lint": "ng lint",
    "serve:ssr:AICU": "node dist/aicu/server/server.mjs",
    "schema": "npx openapi-typescript ./src/app/schema/schema.yaml -o ./src/app/schema/schema.ts"
  },
```

# License Compliance Checker

This guide provides an overview of how to check the licenses of dependencies and ensure compliance, especially for commercial use.

## Overview of Licenses

### **Permissive Licenses**

Licenses such as the **MIT License**, **BSD License**, and **Apache License** allow commercial use with minimal restrictions. Typically, they require:

- Proper attribution.
- Inclusion of license texts in your documentation or distribution.

### **Conditional Licenses**

Some licenses require additional compliance steps for commercial use:

1. **GNU Lesser General Public License (LGPL)**:
   - Allows use in proprietary software.
   - Requires modifications to the library itself to be released under the LGPL.
2. **Mozilla Public License (MPL)**:
   - Requires modifications to the MPL-licensed files to be made available under the same license.

### Example: Using `chardet-5.2.0` (LGPL)

The `chardet` library is licensed under **LGPL v2.1+**. Here's what you need to know:

- ✅ **Permitted:**
  1. You can use `chardet` in proprietary or commercial projects.
  2. You can dynamically link to the library without issues.
- ❗ **Requirements:**
  1. If you modify the `chardet` library itself, you **must release those modifications** under the LGPL.
  2. You **do not need to release** your proprietary software that uses `chardet`, provided you don’t modify the library.

To seperate the potentially problematic licences automatically run

```bash
license-checker --production --exclude \"MIT, ISC, MIT*, BSD-3-Clause, BSD-2-Clause, BSD*, Apache-2.0, Unlicense, Public Domain\" > docs/licenses/conditional_licences.json
```

and then check conditional_licences.json.

---

## Tool for License Verification

Use the `license-checker` Python tool to extract and check the licenses of installed packages in your virtual environment.

### Installation

```bash
npm install -g license-checker
```

### Usage

Generate a detailed JSON report of licenses:

```bash
license-checker --json > docs/licenses/licenses.json

license-checker --production --exclude "MIT, ISC, MIT*, BSD-3-Clause, BSD-2-Clause, BSD*, Apache-2.0, Unlicense, Public Domain" > docs/licenses/conditional_licences.json


license-checker --production --onlyAllow \"MIT, ISC, MIT*, BSD-3-Clause, BSD-2-Clause, BSD*, Apache-2.0, Unlicense, Public Domain\"

```

We can add this to package.json in scripts

```bash


    "license-check-exclude": "license-checker --production --exclude \"MIT, ISC, MIT*, BSD-3-Clause, BSD-2-Clause, BSD*, Apache-2.0, Unlicense, Public Domain\"",
	  "license-check-onlyAllow": "license-checker --production --onlyAllow \"MIT, ISC, MIT*, BSD-3-Clause, BSD-2-Clause, BSD*, Apache-2.0, Unlicense, Public Domain\""

aws s3 sync ./dist/aicu/browser s3://platform.aicuflow.com --region eu-central-1

aws s3 sync ./dist/aicu/browser s3://platform.test.aicuflow.com --region eu-central-1

```

This command will:

- List all installed packages and their licenses.
- Include the license texts for review.
- Save the output to requirements/licenses.json.

---

## Stay Compliant

1. Use the report generated by pip-licenses to review the licenses of your dependencies.
2. Ensure your usage aligns with the terms of each license, especially for conditional licenses like LGPL or MPL.
3. When in doubt, consult legal experts to confirm compliance with specific licenses.

# 👋 Git Support

1. initialize git repository git init
2. Add the Remote Repository

```bash
git remote add origin https://github.com/AICU-HEALTH/frontend-v2.git
git remote -v
git branch #List Local Branches
git branch -r #List Remote Branches
git branch -a #List Both Local and Remote Branches:
```

3. Fetch Existing Remote Branches

```bash
git fetch origin
```

4. Set the Default Branch

```bash
git branch -M prod
git pull origin prod
```

5. Push Local Changes to Remote

```bash
git add .
git commit -m "Describe changes"
git push -u origin prod
```

### Running introjs for onboarding

```bash
npm install intro.js --save --legacy-peer-deps
```

this makes changes in the package-lock.json
