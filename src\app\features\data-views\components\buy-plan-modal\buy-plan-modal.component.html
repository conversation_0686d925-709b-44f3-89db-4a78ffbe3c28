<div
  *ngIf="openModal"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div class="rounded-xl shadow-lg p-4 relative w-[800px] h-[348px] mat-dialog">
    <div class="flex justify-between items-center object-center p-2">
      <h3>Purcharse Storage</h3>
      <button
        mat-icon-button
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <hr />

    <div
      *ngIf="loading"
      class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
      <app-loader [loading]="loading"></app-loader>
    </div>

    <div class="flex justify-evenly items-center gap-[10px] h-2/3 w-full">
      @for (option of addOnObject; track option) {
        <div>
          <mat-card class="flex-1">
            <mat-card-header class="px-5 py-3 border-b flex-1 w-full">
              <mat-card-title class="flex gap-2 items-center justify-between">
                <div class="flex gap-2 items-center">
                  <mat-icon>{{ 'download_done' }}</mat-icon>
                  <span class="font-large text-base font-bold">
                    {{ option.name }}</span
                  >
                </div>
                <mat-icon>navigate_next</mat-icon>
              </mat-card-title>
            </mat-card-header>
            <mat-card-content class="px-6 pt-3 pb-3">
              <p class="text-sm">
                Monthly Cost <b>{{ option.original_monthly_price }} </b>
                <span>&euro;</span>
              </p>

              <button
                mat-flat-button
                class="w-full mt-3"
                (click)="callAddon(option.stripe_price_id)">
                Buy Plan
              </button>
            </mat-card-content>
          </mat-card>
        </div>
      }
    </div>
  </div>
</div>
