mat-list-item:hover button {
  opacity: 1;
}

button {
  transition: opacity 0.1s; /* Smooth transition for visibility */
}

.preprocessing-container {
  margin-top: 15px;
}

.preprocessing-step {
  margin-bottom: 15px; /* Spacing between steps */
  padding: 10px;
}

.step-name {
  font-weight: bold;
  font-size: 1.1em;
}

.step-description {
  margin-bottom: 5px;
}

.params {
  margin-top: 5px; /* Spacing above the parameters */
}

.param-row {
  margin-bottom: 5px; /* Spacing between parameters */
}

.form-select {
  margin-left: 10px;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.custom-tooltip {
  max-width: 300px;
  white-space: pre-line;
  text-align: left;
}
