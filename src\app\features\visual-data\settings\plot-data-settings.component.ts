import {
  Component,
  computed,
  inject,
  input,
  OnInit,
  output,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValueChangeEvent,
} from '@angular/forms';
import { PlotService } from '../../../services/plot.services';
import { g_const } from '../../../_utility/global_const';
import {
  ColorPalette,
  PlotStyle,
  PlotStyleOption,
} from '../../../_models/visual-data/visual-data.model';
import { filter, take } from 'rxjs';
import {
  PlotDataSettingsService,
  PlotStyleForm,
} from './plot-data-settings.service';
import { ProjectData } from '../../dashborad/models/project.model';
import { ColorSettingsService } from './color-settings.service';

@Component({
  selector: 'app-plot-data-settings',
  templateUrl: './plot-data-settings.component.html',
  styleUrls: ['./plot-data-settings.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class PlotDataSettingsComponent implements OnInit {
  projectDetails = input.required<ProjectData>();
  // selectedProjectPlotStyle = input.required<number | null>();
  readonly panelOpenState = signal(false);
  cancelEvent = output<boolean>();
  saveChanges = output<boolean>();
  plotStyles = signal<PlotStyle[] | null>(null);
  preselectedPlotStyle = computed(() =>
    this.plotStyles()?.find(
      ps => ps.id === this.projectDetails().field_plot_styles,
    ),
  );
  allPlotStyleOptions = signal<PlotStyleOption[]>([]);
  availablePlotStyleOptions = computed(() =>
    this.allPlotStyleOptions()?.filter(po => po.children.length > 0),
  );
  currentlySelectedColorPalette = signal<ColorPalette | null>(null);
  colorPalettes = signal<ColorPalette[]>([]);
  formValues = signal<PlotStyleForm | null>(null);
  payload: { name: string; colors: string[] } | undefined;
  loading = false;
  chartContent = signal<PlotStyleOption | null>(null);
  activeToolWidget = signal<null | 'ColorPicker' | 'ChartContent'>(null);
  colorPaletteEdited = signal<ColorPalette | null>(null);
  form;

  colorSettingsService = inject(ColorSettingsService);

  constructor(
    private plotDataSettingsService: PlotDataSettingsService,
    private fb: FormBuilder,
    private plotService: PlotService,
  ) {
    this.form = this.generateForm(this.preselectedPlotStyle());
  }

  ngOnInit(): void {
    this.loading = true;
    this.getPlotStyles(this.projectDetails());
    this.getColorPalettes();
  }

  generateForm(selectedPlotStyle?: PlotStyle) {
    const preselectedColorPalette = this.colorPalettes().find(
      cp => cp.id === selectedPlotStyle?.color_palette,
    );
    const staticControls = {
      selectedPlotStyle: new FormControl<PlotStyle>(selectedPlotStyle!, {
        nonNullable: true,
      }),
      selectedColorPalette: new FormControl<ColorPalette>(
        preselectedColorPalette!,
        { nonNullable: true },
      ),
    };

    if (selectedPlotStyle !== undefined) {
      this.setFormOptionsAndValues(selectedPlotStyle);
    }
    const dynamicControls: Record<string, FormGroup> =
      this.allPlotStyleOptions()?.reduce(
        (controls, chartContent) => {
          controls[chartContent.name] =
            this.plotDataSettingsService.getFormGroups(chartContent);
          return controls;
        },
        {} as Record<string, FormGroup>,
      );

    const formGroup = this.fb.group({
      ...staticControls,
      ...dynamicControls,
    });

    formGroup.controls.selectedPlotStyle.events
      .pipe(filter(event => event instanceof ValueChangeEvent))
      .subscribe((event: ValueChangeEvent<PlotStyle>) => {
        this.setFormOptionsAndValues(event.value);
      });

    formGroup.controls.selectedColorPalette.events
      .pipe(filter(event => event instanceof ValueChangeEvent))
      .subscribe((event: ValueChangeEvent<ColorPalette>) => {
        this.currentlySelectedColorPalette.set(event.value);
      });

    formGroup.events
      .pipe(filter(event => event instanceof ValueChangeEvent))
      .subscribe((event: ValueChangeEvent<PlotStyleForm>) =>
        this.formValues.set(event.value),
      );

    return formGroup;
  }

  setFormOptionsAndValues(plotStyle: PlotStyle) {
    if (plotStyle.options !== undefined) {
      this.allPlotStyleOptions.set(plotStyle.options);
    }

    const preselectedColorPalette = this.colorPalettes().find(
      cp => cp.id === plotStyle.color_palette,
    );

    this.form.controls.selectedColorPalette.setValue(
      preselectedColorPalette ?? this.colorPalettes()[0],
    );
  }

  getPlotStyles(projectDetails: ProjectData): void {
    const project_id = Number(localStorage.getItem('project_id'));
    this.plotService
      .getPlotStyles(project_id)
      .pipe(take(1))
      .subscribe({
        next: response => {
          this.plotStyles.set(response.data.plot_styles);

          const preselectedPlotType = response.data.plot_styles.find(
            ds => ds.id === projectDetails.field_plot_styles,
          );
          if (preselectedPlotType) {
            this.form = this.generateForm(preselectedPlotType);
          }

          this.loading = false;
        },
        error: error => {
          this.loading = false;
          console.error('Error fetching plot data', error);
        },
      });
  }

  showHideChart(chipData: PlotStyleOption) {
    if (chipData.id === this.chartContent()?.id) {
      this.chartContent.set(null);
      this.activeToolWidget.set(null);
    } else {
      this.chartContent.set(chipData);
      this.activeToolWidget.set('ChartContent');
    }
  }

  updateColorPalette(newColor: string) {
    const selectedColorPalette = this.form.controls.selectedColorPalette.value;
    const currentlySelectedColorTileId =
      this.colorSettingsService.currentlySelectedColorTileId();
    const adaptedColorPalette = this.colorPalettes().find(
      cp => cp.id === selectedColorPalette.id,
    );

    if (adaptedColorPalette && currentlySelectedColorTileId) {
      if (currentlySelectedColorTileId.id < 0) {
        this.colorPalettes.update(cpArray =>
          cpArray.map(cp => {
            if (cp.id === adaptedColorPalette.id) {
              return {
                ...cp,
                colors: [...cp.colors, newColor],
              };
            }
            return cp;
          }),
        );
      } else {
        this.colorPalettes.update(cpArray =>
          cpArray.map(cp => {
            return {
              ...cp,
              colors: cp.colors.map((c, i) => {
                if (
                  cp.id === adaptedColorPalette.id &&
                  i === currentlySelectedColorTileId.index
                ) {
                  return newColor;
                }
                return c;
              }),
            };
          }),
        );
      }
      this.currentlySelectedColorPalette.set(
        this.colorPalettes().find(cp => cp.id === selectedColorPalette.id) ??
          null,
      );
      this.showColorHidePicker(null);
    }
  }

  showColorHidePicker(colorPalette: ColorPalette | null) {
    if (colorPalette === null) {
      this.activeToolWidget.set(null);
      this.colorPaletteEdited.set(null);
      this.colorSettingsService.setCurrentlySelectedColorInPicker(null);
      this.colorSettingsService.currentlySelectedColorTileId.set(null);
    } else {
      this.activeToolWidget.set('ColorPicker');
      this.colorPaletteEdited.set(colorPalette);
    }
  }

  adaptCurrentSelectedColorInPicker(colorHex: string) {
    this.colorSettingsService.setCurrentlySelectedColorInPicker(colorHex);
  }

  getColorPalettes(): void {
    this.plotService
      .getColorPalette()
      .pipe(take(1))
      .subscribe({
        next: response => {
          this.colorPalettes.set(response.data.color_palettes);
        },
        error: error => {
          console.error('Error fetching plot data', error);
        },
      });
  }

  cancel() {
    this.cancelEvent.emit(true);
  }

  changePaletteName(palettId: number, paletteName: string) {
    const selectedColorPalette = this.form.controls.selectedColorPalette.value;
    selectedColorPalette.name = paletteName;
  }

  handleChartContentChange(
    charContent: PlotStyleOption,
    chartContentFormGroup: FormGroup,
  ) {
    this.form.get(charContent.name)?.patchValue(chartContentFormGroup.value);
  }

  handleColorPaletteChange() {
    this.colorSettingsService.currentlySelectedColorTileId.set(null);
    this.activeToolWidget.set(null);
    this.colorSettingsService.setCurrentlySelectedColorInPicker(null);
  }

  saveSettings() {
    this.savePlotTypeVals();
    this.saveColorPalettes();

    // this.plotService
    //   .changeSettings(convertedValues.id, convertedValues)
    //   .subscribe({
    //     next: x => console.log(x),
    //     error: e => console.error('error updating settings: ', e),
    //   });
  }

  savePlotTypeVals() {
    const formValues: PlotStyleForm = {
      selectedPlotStyle: this.form.value.selectedPlotStyle!,
      selectedColorPalette: this.form.value.selectedColorPalette!,
    };
    const convertedValues = this.plotDataSettingsService.convertFormToPlotStyle(
      this.projectDetails().id,
      formValues,
    );

    console.log('send values', convertedValues);
  }

  saveColorPalettes() {
    console.log('send colorPalettes', this.colorPalettes());
  }

  protected readonly g_const = g_const;
}
