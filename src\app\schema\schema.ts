/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/appoptions/DropDown/advancedML/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_advancedML_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/advancedML/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_advancedML_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/advancedOptions/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_advancedOptions_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/advancedOptions/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_advancedOptions_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datacleaning/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datacleaning_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datacleaning/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datacleaning_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datatypes/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datatypes_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datatypes/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datatypes_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datawrangling/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datawrangling_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appoptions/DropDown/datawrangling/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['appoptions_DropDown_datawrangling_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/create-dataset-version/{project_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['dataversion_create_dataset_version_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/dataset-version/{dataset_version_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataversion_dataset_version_retrieve'];
    put: operations['dataversion_dataset_version_update'];
    post?: never;
    delete: operations['dataversion_dataset_version_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/dataset-version/project/{project_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataversion_dataset_version_project_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/delete-pipeline/{pipeline_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete: operations['dataversion_delete_pipeline_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/pipeline-step/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataversion_pipeline_step_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/pipeline-steps/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataversion_pipeline_steps_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataversion/update-pipeline/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put: operations['dataversion_update_pipeline_update'];
    post: operations['dataversion_update_pipeline_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch: operations['dataversion_update_pipeline_partial_update'];
    trace?: never;
  };
  '/dataversion/user-pipeline/{pipeline_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataversion_user_pipeline_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/column-ID/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_column_ID_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/column-choices/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_column_choices_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/column-values/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_column_values_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/custom/processing/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Mathematical operations on columns and filtering (Needs to be tested) */
    post: operations['dataview_custom_processing_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/custom/processing/delete-cols/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Mathematical operations on columns and filtering (Needs to be tested) */
    post: operations['dataview_custom_processing_delete_cols_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/custom/processing/list-nan/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Get columns list with datatypes. */
    get: operations['dataview_custom_processing_list_nan_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/data/statistics/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['dataview_data_statistics_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/delete-cols/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete: operations['dataview_delete_cols_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/display/file/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Display Original Data with pagination. */
    get: operations['dataview_display_file_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/download/data/statistics/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_download_data_statistics_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/download/data/statistics/image-plot/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_download_data_statistics_image_plot_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/download/file/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Download Processed or Original Data. */
    get: operations['dataview_download_file_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/downloadImageStatsistics/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_downloadImageStatsistics_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/file/{file_id}/rename/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    /** @description Rename a file */
    put: operations['dataview_file_rename_update'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/filter/{filter_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /** @description Delete filter based on filter ID. */
    delete: operations['dataview_filter_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/filter/data/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Filter processed Data. */
    post: operations['dataview_filter_data_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/filter/file/{filter_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Retrieve filter options for a given filter ID. */
    get: operations['dataview_filter_file_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/image/statistics/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['dataview_image_statistics_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/math-advanced/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Math with Columns API */
    post: operations['dataview_math_advanced_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/processed/column-ID/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['dataview_processed_column_ID_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/set-column-ID/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['dataview_set_column_ID_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/dataview/set-file-metdadata/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['dataview_set_file_metdadata_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/ColumnInfo/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['files_ColumnInfo_retrieve'];
    /** @description Edit column information associated with a CSV file. */
    put: operations['files_ColumnInfo_update'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description List all S3 files for the authenticated user. */
    get: operations['files_Files_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /** @description Delete a specific file by project ID and file ID. */
    delete: operations['files_Files_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/folder/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description List S3 files by project and folder for the authenticated user. */
    get: operations['files_Files_folder_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/process/{file_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['files_Files_process_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/process/batch/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['files_Files_process_batch_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/project/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description List S3 files by project for the authenticated user. */
    get: operations['files_Files_project_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/upload-folder/{folder_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description 1. Accepts a zip file containing the folder and files
     *     2. Unzips the file in a temporary directory
     *     3. Uploads the individual files (checks type of file) to S3.
     *     4. Creates entries of the folder and files in the DB. */
    post: operations['files_Files_upload_folder_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/upload-folder/{folder_id}/status/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Checks the status of the uploaded folder to S3 by comparing the files and subfolders
     *     in the folder structure to those in the S3 bucket.
     *
     *     Args:
     *     - folder_structure (dict): A JSON structure with folder_id, files, and subfolders.
     *
     *     Returns:
     *     - Response object with the status of the upload and any missing files or folders. */
    post: operations['files_Files_upload_folder_status_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/upload-status/{folder_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['files_Files_upload_status_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Files/upload-url/{folder_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['files_Files_upload_url_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Folders/{folder_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['files_Folders_retrieve'];
    put: operations['files_Folders_update'];
    post: operations['files_Folders_create'];
    delete: operations['files_Folders_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/Folders/hierarchy/{folder_id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description On the basis of user, we just need to give the folder id and relevant files and folders under that are displayed
     *     Pagination is applied to folders as well as files
     *     Args:
     *         request:
     *         folder_id: id of the folder to see the contents of
     *
     *     Returns: list of files and folders */
    get: operations['files_Folders_hierarchy_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/files/StoreAPIRequestInfo/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description This API allows you to store information about an API request, including the
     *     project ID, file ID, target column, ID column, and a list of columns passed
     *     as features. The information is stored in the database for further processing. */
    post: operations['files_StoreAPIRequestInfo_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/get_api_endpoints/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Unified API to generate various plots including Summary of Target Variable, Pair Plots,
     *     and Linear Correlation Plots. */
    get: operations['get_api_endpoints_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/Projects/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['projects_Projects_retrieve'];
    put?: never;
    post: operations['projects_Projects_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/Projects/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['projects_Projects_retrieve_2'];
    put: operations['projects_Projects_update'];
    post?: never;
    delete: operations['projects_Projects_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/Projects/{id}/Files/filter/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['projects_Projects_Files_filter_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/Projects/{id}/Hypothesis/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['projects_Projects_Hypothesis_retrieve_2'];
    put?: never;
    post: operations['projects_Projects_Hypothesis_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/Projects/Hypothesis/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['projects_Projects_Hypothesis_retrieve'];
    put: operations['projects_Projects_Hypothesis_update'];
    post?: never;
    delete: operations['projects_Projects_Hypothesis_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['training_results_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['training_results_advanced_plots_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/decision/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description SHAP decision plots show how complex models arrive at their predictions (i.e., how models make decisions).
     *     plotting one decision plot per class
     *
     *     Args:
     *         request:
     *
     *     Returns: */
    get: operations['training_results_advanced_plots_decision_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/degree-of-importance/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['training_results_advanced_plots_degree_of_importance_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/dependence/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a SHAP dependence plot, colored by an interaction feature. Plots the value of the feature on the x-axis
     *     and the SHAP value of the same feature on the y-axis. This shows how the model depends on the given feature,
     *     and is like a richer extenstion of the classical parital dependence plots. Vertical dispersion of the data points
     *     represents interaction effects. Grey ticks along the y-axis are data points where the feature’s value was NaN
     *
     *     Args:
     *         request:
     *
     *     Returns: base64 image encoding along with graph interpretation */
    get: operations['training_results_advanced_plots_dependence_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/dependence/classes/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description API to return the file classes and the data_type. Also need to return the numerical type of columns for the
     *     dependence plot
     *     Args:
     *         request:
     *
     *     Returns: classes or categories of the dataset, their datatype and the list of numericla columns */
    get: operations['training_results_advanced_plots_dependence_classes_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/force/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Visualize the given SHAP values with an additive force layout. plotting one decision plot per class
     *     Args:
     *         request: takes in the training id param along with the id, target and feature columns
     *
     *     Returns: base64 encoding of the plot in a string format, along with the type of the plo, interpretation and
     *     total number of pages */
    get: operations['training_results_advanced_plots_force_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/advanced-plots/important-features/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Quite a lot errors while saving Shap summmary plots.
     *     1. save the plot to s3 and then give s3 url (Issue: if user asks for the plot again, the old key cannot be used,
     *     either use timestamp'
     *     2. use image_base64 content (not sure if angular can render this on the webpage) Does not need any saving of the graphs
     *     https://stackoverflow.com/questions/48286094/serving-matplotlib-graphs-with-django-without-saving */
    get: operations['training_results_advanced_plots_important_features_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/dataset/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description 1. Check User
     *     2. Get session variables
     *     3. Get all the files associated with a project
     *     4. For each file get the list of columns that file has
     *     5. Add pagination to get 3 such file and column information first. */
    get: operations['training_results_dataset_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/training-results/preprocessing/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['training_results_preprocessing_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/activate/{uid}/{token}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['users_activate_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/change-password/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_change_password_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/delete/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete: operations['users_delete_destroy'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/forgot-password/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_forgot_password_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/login/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_login_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/logout/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_logout_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/register/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_register_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/users/reset-password/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['users_reset_password_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/color-palette/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_color_palette_list'];
    put?: never;
    post: operations['visualization_color_palette_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/color-palette/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_color_palette_retrieve'];
    put: operations['visualization_color_palette_update'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/plot/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Fetch All Plots API retrieves a list of all plot names. */
    get: operations['visualization_plot_list'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/plot-style/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_plot_style_list'];
    put?: never;
    post: operations['visualization_plot_style_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/plot-style-option/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_plot_style_option_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch: operations['visualization_plot_style_option_partial_update'];
    trace?: never;
  };
  '/visualization/plot-style-option/{id}/children/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_plot_style_option_children_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/plot-style/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['visualization_plot_style_retrieve'];
    put?: never;
    post?: never;
    delete: operations['visualization_plot_style_destroy'];
    options?: never;
    head?: never;
    patch: operations['visualization_plot_style_partial_update'];
    trace?: never;
  };
  '/visualization/plot/{slug}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Fetch All Plots API retrieves a list of all plot names. */
    get: operations['visualization_plot_retrieve'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/user-plot/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Handles retrieval of user's old plots with optional filters and pagination. */
    get: operations['visualization_user_plot_list'];
    put?: never;
    /** @description Handles creation of a new User Plot. */
    post: operations['visualization_user_plot_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/user-plot/{id}/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Retrieve UserPlot Info API */
    get: operations['visualization_user_plot_retrieve'];
    /** @description Update UserPlot API */
    put: operations['visualization_user_plot_update'];
    post?: never;
    /** @description Delete UserPlot API */
    delete: operations['visualization_user_plot_destroy'];
    options?: never;
    head?: never;
    patch: operations['visualization_user_plot_partial_update'];
    trace?: never;
  };
  '/visualization/user-plot/{id}/associate-filters/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Associates filters with a specific plot.
     *     Args:
     *         request: The request object containing filter data.
     *         plot_id: The ID of the plot to associate the filters with.
     *
     *     Payload:
     *         {
     *           "filters": [
     *             {
     *               "filter_column": "Status",
     *               "filter_operator": "eq",
     *               "filter_value": "D",
     *               "logic": "AND"
     *             },
     *             ...
     *           ]
     *         } */
    post: operations['visualization_user_plot_associate_filters_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/user-plot/{id}/description/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['visualization_user_plot_description_create'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/visualization/user-plot/display-layout/': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch: operations['visualization_user_plot_display_layout_partial_update'];
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    Aggregation: {
      aggregation_type: string;
      aggregation_description: string;
    };
    /** @enum {unknown} */
    BlankEnum: '';
    ColorPalette: {
      readonly id: number;
      /** @description Name of the color palette (e.g., 'Custom', 'Viridis') */
      name: string;
      /** @description List of colors in the palette (stored as a JSON list) */
      colors?: unknown;
    };
    ColorPaletteRequest: {
      /** @description Name of the color palette (e.g., 'Custom', 'Viridis') */
      name: string;
      /** @description List of colors in the palette (stored as a JSON list) */
      colors?: unknown;
    };
    DataCleaningAndPreparation: {
      readonly id: number;
      name: string;
      options?: unknown;
      description: string;
      applicable_data_types: unknown;
      benefits: unknown;
      limitations: unknown;
      example_code: unknown;
    };
    DataType: {
      readonly id: number;
      name: string;
      description?: string | null;
      options?: unknown;
    };
    /**
     * @description * `int` - Integer
     *     * `float` - Float
     *     * `string` - String
     *     * `bool` - Boolean
     *     * `int or null` - Integer or Null
     *     * `float or string` - Float or String
     * @enum {string}
     */
    DataTypeEnum:
      | 'int'
      | 'float'
      | 'string'
      | 'bool'
      | 'int or null'
      | 'float or string';
    DataWrangling: {
      readonly id: number;
      name: string;
      description: string;
      applicable_data_types: unknown;
      benefits: unknown;
      limitations: unknown;
      example_code: unknown;
    };
    /**
     * @description * `image` - Image
     *     * `table` - Table
     * @enum {string}
     */
    DatasetTypeEnum: 'image' | 'table';
    /**
     * @description * `image` - Image
     *     * `csv` - CSV
     *     * `parquet` - Parquet
     *     * `tsv` - TSV
     *     * `xml` - XML
     *     * `txt` - TSV
     *     * `yaml` - YAML
     *     * `yml` - YAML
     * @enum {string}
     */
    FileTypeEnum:
      | 'image'
      | 'csv'
      | 'parquet'
      | 'tsv'
      | 'xml'
      | 'txt'
      | 'yaml'
      | 'yml';
    Hypothesis: {
      readonly id: number;
      readonly project: number;
      hypothesis_type: string;
      hypothesis: string;
      /** Format: date-time */
      readonly created_at: string;
      /** Format: date-time */
      readonly updated_at: string;
    };
    HypothesisRequest: {
      hypothesis_type: string;
      hypothesis: string;
    };
    LoginRequest: {
      /** Format: email */
      email: string;
      password: string;
      /** @default false */
      keep_me_logged_in: boolean;
    };
    MachineLearningModel: {
      readonly id: number;
      readonly options: components['schemas']['Option'][];
      name: string;
      machine_learning_tasks?:
        | (
            | components['schemas']['MachineLearningTasksEnum']
            | components['schemas']['BlankEnum']
            | components['schemas']['NullEnum']
          )
        | null;
      category: components['schemas']['MachineLearningModelCategoryEnum'];
      description?: string | null;
      /** Format: date-time */
      readonly created_at: string;
      /** Format: date-time */
      readonly updated_at: string;
      suitable_for?: number | null;
    };
    /**
     * @description * `Supervised Learning: Regression` - Supervised Learning: Regression
     *     * `Optimization Algorithms` - Optimization Algorithms
     *     * `Supervised Learning: Classification` - Supervised Learning: Classification
     *     * `Model Evaluation` - Model Evaluation
     *     * `Unsupervised Learning: Clustering` - Unsupervised Learning: Clustering
     *     * `Dimensionality Reduction` - Dimensionality Reduction
     *     * `Association Analysis` - Association Analysis
     * @enum {string}
     */
    MachineLearningModelCategoryEnum:
      | 'Supervised Learning: Regression'
      | 'Optimization Algorithms'
      | 'Supervised Learning: Classification'
      | 'Model Evaluation'
      | 'Unsupervised Learning: Clustering'
      | 'Dimensionality Reduction'
      | 'Association Analysis';
    /**
     * @description * `Regression` - Regression
     *     * `Classification` - Classification
     *     * `Clustering` - Clustering
     *     * `Dimensionality Reduction` - Dimensionality Reduction
     *     * `Time Series Forecasting` - Time Series Forecasting
     *     * `Association Analysis` - Association Analysis
     * @enum {string}
     */
    MachineLearningTasksEnum:
      | 'Regression'
      | 'Classification'
      | 'Clustering'
      | 'Dimensionality Reduction'
      | 'Time Series Forecasting'
      | 'Association Analysis';
    /** @enum {unknown} */
    NullEnum: null;
    Option: {
      readonly id: number;
      name: string;
      value?: string | null;
      data_type?: components['schemas']['DataTypeEnum'];
      description?: string | null;
      options?: unknown;
      ml_model?: number;
    };
    PatchedPlotStyleOptionRequest: {
      /** @description The name of the option (e.g., 'Color Palette', 'Marker Size'). */
      name?: string;
      /** @description The current value of the option. */
      value?: string;
      data_type?: string;
      description?: string;
      options?: unknown;
      parent?: number | null;
      display_type?: string;
    };
    PatchedPlotStyleRequest: {
      /** @description Name of the plot style (e.g., 'High Impact Research') */
      name?: string;
      color_palette?: number | null;
      /** @description Indicates if this is a global, user, or project-wide style
       *
       *     * `global` - Global
       *     * `user` - User
       *     * `project` - Project
       *     * `local` - Local */
      style_type?: components['schemas']['StyleTypeEnum'];
      /** @description User who owns the style */
      user?: number | null;
      project?: number | null;
      options?: components['schemas']['PlotStyleOptionRequest'][];
    };
    PatchedUserPlotRequest: {
      selected_options?: components['schemas']['UserOptionRequest'][];
      selected_settings?: components['schemas']['UserSettingRequest'][];
      description?: string | null;
      interpretation?: string | null;
      /** @description Plot-specific style for this plot */
      plot_style?: number | null;
      plot_title?: string | null;
      display_layout?: unknown;
    };
    PipelineStep: {
      name: string;
      readonly id: number;
      /** Format: int64 */
      order: number;
      description: string;
      step_type: components['schemas']['StepTypeEnum'];
      dataset_type: components['schemas']['DatasetTypeEnum'];
      params: components['schemas']['PipelineStepOption'][];
    };
    PipelineStepOption: {
      name: string;
      readonly id: number;
      value: string;
      data_type: string;
      description: string;
      options?: unknown;
    };
    Plot: {
      plot_name: string;
      plot_description: string;
      icon?: string;
      category?: components['schemas']['PlotCategoryEnum'];
      readonly option_set: components['schemas']['Option'][];
      readonly setting_set: components['schemas']['Setting'][];
      readonly aggregation_set: components['schemas']['Aggregation'][];
      readonly smartbucketing_set: components['schemas']['SmartBucketing'][];
      slug?: string | null;
    };
    /**
     * @description * `Visualise` - Visualise
     *     * `Explain` - Explain
     *     * `Standard` - Standard
     * @enum {string}
     */
    PlotCategoryEnum: 'Visualise' | 'Explain' | 'Standard';
    PlotStyle: {
      readonly id: number;
      /** @description Name of the plot style (e.g., 'High Impact Research') */
      name: string;
      color_palette?: number | null;
      /** @description Indicates if this is a global, user, or project-wide style
       *
       *     * `global` - Global
       *     * `user` - User
       *     * `project` - Project
       *     * `local` - Local */
      style_type?: components['schemas']['StyleTypeEnum'];
      /** @description User who owns the style */
      user?: number | null;
      project?: number | null;
      options: components['schemas']['PlotStyleOption'][];
    };
    PlotStyleOption: {
      readonly id: number;
      /** @description The name of the option (e.g., 'Color Palette', 'Marker Size'). */
      name: string;
      /** @description The current value of the option. */
      value: string;
      data_type?: string;
      description?: string;
      options?: unknown;
      parent?: number | null;
      readonly children: string;
      display_type?: string;
    };
    PlotStyleOptionRequest: {
      /** @description The name of the option (e.g., 'Color Palette', 'Marker Size'). */
      name: string;
      /** @description The current value of the option. */
      value: string;
      data_type?: string;
      description?: string;
      options?: unknown;
      parent?: number | null;
      display_type?: string;
    };
    PlotStyleRequest: {
      /** @description Name of the plot style (e.g., 'High Impact Research') */
      name: string;
      color_palette?: number | null;
      /** @description Indicates if this is a global, user, or project-wide style
       *
       *     * `global` - Global
       *     * `user` - User
       *     * `project` - Project
       *     * `local` - Local */
      style_type?: components['schemas']['StyleTypeEnum'];
      /** @description User who owns the style */
      user?: number | null;
      project?: number | null;
      options: components['schemas']['PlotStyleOptionRequest'][];
    };
    Project: {
      readonly id: number;
      readonly owner: number;
      title: string;
      description: string;
      /** Format: date-time */
      readonly created_at: string;
      /** Format: date-time */
      readonly updated_at: string;
      /** Format: int64 */
      file_count?: number;
      /** Format: int64 */
      files_size?: number | null;
      /** Format: int64 */
      plot_count?: number;
      /** Format: int64 */
      model_count?: number;
      /** Format: int64 */
      train_runs?: number;
      folder_key?: string;
      readonly folder_id: string;
      readonly hypotheses: components['schemas']['Hypothesis'][];
      /** @description The plot styles associated with the project */
      field_plot_styles?: number | null;
    };
    ProjectRequest: {
      title: string;
      description: string;
      /** Format: int64 */
      file_count?: number;
      /** Format: int64 */
      files_size?: number | null;
      /** Format: int64 */
      plot_count?: number;
      /** Format: int64 */
      model_count?: number;
      /** Format: int64 */
      train_runs?: number;
      folder_key?: string;
      /** @description The plot styles associated with the project */
      field_plot_styles?: number | null;
    };
    S3File: {
      readonly id: number;
      readonly user: number;
      readonly project: number;
      file_key: string;
      file_name: string;
      /** Format: date-time */
      readonly created_at: string;
      /** Format: date-time */
      readonly updated_at: string;
      /** Format: int64 */
      file_size?: number | null;
      file_type: components['schemas']['FileTypeEnum'];
      extension?: string | null;
      status?: string | null;
      /** Format: int64 */
      original_file_id?: number | null;
      /** Format: int64 */
      processed_file_id?: number | null;
      readonly polymorphic_ctype: number | null;
      folder?: number | null;
    };
    Setting: {
      setting_name: string;
      setting_description: string;
      multiple?: boolean;
      settings_type?: components['schemas']['SettingsTypeEnum'];
    };
    /**
     * @description * `Dropdown_Settings` - Dropdown_Settings
     *     * `Quartile_methods` - Quartile_methods
     *     * `Switch_buttons` - Switch_buttons
     * @enum {string}
     */
    SettingsTypeEnum:
      | 'Dropdown_Settings'
      | 'Quartile_methods'
      | 'Switch_buttons';
    SmartBucketing: {
      smart_bucketing_name: string;
      smart_bucketing_value: string;
      bucketing_type: string;
      bucketing_description: string;
    };
    /**
     * @description * `preprocessing` - Preprocessing
     *     * `augmentation` - Augmentation
     * @enum {string}
     */
    StepTypeEnum: 'preprocessing' | 'augmentation';
    /**
     * @description * `global` - Global
     *     * `user` - User
     *     * `project` - Project
     *     * `local` - Local
     * @enum {string}
     */
    StyleTypeEnum: 'global' | 'user' | 'project' | 'local';
    UserOption: {
      readonly option_name: string;
      selected_column_name: string;
      readonly selected_aggregation: string;
      readonly selected_smart_bucketing: string;
    };
    UserOptionRequest: {
      selected_column_name: string;
    };
    UserPlot: {
      readonly plot_name: string;
      readonly file_id: string;
      readonly file_name: string;
      selected_options: components['schemas']['UserOption'][];
      selected_settings: components['schemas']['UserSetting'][];
      /** Format: date-time */
      readonly selection_date: string;
      description?: string | null;
      interpretation?: string | null;
      /** @description Plot-specific style for this plot */
      plot_style?: number | null;
      plot_title?: string | null;
      display_layout?: unknown;
    };
    UserPlotRequest: {
      selected_options: components['schemas']['UserOptionRequest'][];
      selected_settings: components['schemas']['UserSettingRequest'][];
      description?: string | null;
      interpretation?: string | null;
      /** @description Plot-specific style for this plot */
      plot_style?: number | null;
      plot_title?: string | null;
      display_layout?: unknown;
    };
    UserSetting: {
      readonly setting_name: string;
      selected_setting_value?: boolean;
    };
    UserSettingRequest: {
      selected_setting_value?: boolean;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  appoptions_DropDown_advancedML_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['MachineLearningModel'][];
        };
      };
    };
  };
  appoptions_DropDown_advancedML_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['MachineLearningModel'];
        };
      };
    };
  };
  appoptions_DropDown_advancedOptions_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Option'][];
        };
      };
    };
  };
  appoptions_DropDown_advancedOptions_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Option'];
        };
      };
    };
  };
  appoptions_DropDown_datacleaning_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataCleaningAndPreparation'][];
        };
      };
    };
  };
  appoptions_DropDown_datacleaning_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataCleaningAndPreparation'];
        };
      };
    };
  };
  appoptions_DropDown_datatypes_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataType'][];
        };
      };
    };
  };
  appoptions_DropDown_datatypes_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataType'];
        };
      };
    };
  };
  appoptions_DropDown_datawrangling_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataWrangling'][];
        };
      };
    };
  };
  appoptions_DropDown_datawrangling_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['DataWrangling'];
        };
      };
    };
  };
  dataversion_create_dataset_version_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        project_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_dataset_version_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        dataset_version_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_dataset_version_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        dataset_version_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_dataset_version_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        dataset_version_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_dataset_version_project_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        project_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_delete_pipeline_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        pipeline_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_pipeline_step_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PipelineStep'];
        };
      };
    };
  };
  dataversion_pipeline_steps_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PipelineStep'][];
        };
      };
    };
  };
  dataversion_update_pipeline_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_update_pipeline_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_update_pipeline_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataversion_user_pipeline_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        pipeline_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_column_ID_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_column_choices_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_column_values_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_custom_processing_create: {
    parameters: {
      query?: {
        /** @description Column Threshold. */
        column_threshold?: number;
        /** @description Current Page */
        page?: number;
        /** @description Custom Page Size. Default is 15. */
        page_size?: number;
        /** @description Remove Duplicates. */
        remove_duplicates?: boolean;
        /** @description Row Threshold. */
        row_threshold?: number;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_custom_processing_delete_cols_create: {
    parameters: {
      query?: {
        /** @description Current Page */
        page?: number;
        /** @description Custom Page Size. Default is 15. */
        page_size?: number;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_custom_processing_list_nan_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_data_statistics_create: {
    parameters: {
      query: {
        /** @description Processed Data */
        processed?: boolean;
        /** @description Type of statistics to retrieve: 'numeric' or 'non-numeric' */
        type: string;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_delete_cols_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_display_file_retrieve: {
    parameters: {
      query?: {
        /** @description Current Page */
        page?: number;
        /** @description Custom Page Size. Default is 15. */
        page_size?: number;
        /** @description Preview Data i.e. a small subset of the data */
        preview?: boolean;
        /** @description Processed Data */
        processed?: boolean;
        /** @description Randomize the data */
        random?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_download_data_statistics_retrieve: {
    parameters: {
      query: {
        /** @description Type of statistics to retrieve: 'numeric' or 'non-numeric' */
        type: string;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_download_data_statistics_image_plot_retrieve: {
    parameters: {
      query: {
        /** @description Name of the plot to download */
        plot_name: string;
        /** @description Show figures for developer */
        show_fig?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_download_file_retrieve: {
    parameters: {
      query?: {
        /** @description Processed Data */
        processed?: boolean;
        /** @description Randomize the data */
        random?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_downloadImageStatsistics_retrieve: {
    parameters: {
      query?: {
        /** @description Comma-separated list of plot names to download (e.g. "Histogram,Sobel Edge Detection") */
        plot_names?: string;
        /** @description Show figures for developer */
        show_fig?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_file_rename_update: {
    parameters: {
      query?: {
        /** @description ID of the file to rename */
        file_id?: number;
        /** @description New name for the file */
        new_name?: string;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_filter_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        filter_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_filter_data_create: {
    parameters: {
      query?: {
        /** @description If you need to debug this function. */
        debug?: boolean;
        /** @description Current Page */
        page?: number;
        /** @description Custom Page Size. Default is 15. */
        page_size?: number;
        /** @description Processed Data */
        processed?: boolean;
        /** @description Column to sort by */
        sort_column?: string;
        /** @description Sort order: asc for ascending, desc for descending */
        sort_order?: string;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_filter_file_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        filter_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_image_statistics_create: {
    parameters: {
      query?: {
        /** @description Pagination cursor i.e. page */
        page?: number;
        /** @description Pagination limit i.e. page_size */
        page_size?: number;
        /** @description Show figures for developer */
        show_fig?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': Record<string, unknown>;
        'application/x-www-form-urlencoded': Record<string, unknown>;
        'multipart/form-data': Record<string, unknown>;
      };
    };
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_math_advanced_create: {
    parameters: {
      query?: {
        /** @description Current Page */
        page?: number;
        /** @description Custom Page Size. Default is 15. */
        page_size?: number;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_processed_column_ID_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_set_column_ID_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  dataview_set_file_metdadata_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_ColumnInfo_retrieve: {
    parameters: {
      query?: {
        /** @description Flag to exclude the ID column from the results */
        exclude_id?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_ColumnInfo_update: {
    parameters: {
      query?: {
        /** @description Flag to update all and then delete existing or update only given and keep rest */
        update_all?: boolean;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_destroy: {
    parameters: {
      query: {
        /** @description File ID to be deleted */
        file_id: number;
        /** @description Project ID to which file belongs */
        project_id: number;
      };
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_folder_list: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description Project ID to get files for */
        id: number;
        /** @description Folder ID to get files for */
        id_folder: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['S3File'][];
        };
      };
      /** @description Invalid input */
      400: {
        headers: Record<string, unknown>;
        content?: never;
      };
      /** @description File not found */
      404: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_process_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        file_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_process_batch_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_project_list: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description Project ID to get files for */
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['S3File'][];
        };
      };
      /** @description Invalid input */
      400: {
        headers: Record<string, unknown>;
        content?: never;
      };
      /** @description File not found */
      404: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_upload_folder_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_upload_folder_status_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_upload_status_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': unknown;
      };
    };
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Files_upload_url_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': unknown;
      };
    };
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Folders_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Folders_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Folders_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Folders_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_Folders_hierarchy_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        folder_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  files_StoreAPIRequestInfo_create: {
    parameters: {
      query: {
        /** @description Columns that should be added as features. */
        columns_passed: Record<string, never>[];
        /** @description file_id */
        file_id?: number;
        /** @description project_id */
        project_id?: number;
        /** @description target_column */
        target_column?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  get_api_endpoints_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_retrieve: {
    parameters: {
      query?: {
        /** @description description_contains */
        description_contains?: string;
        /** @description Set to true to list projects with S3 objects */
        list_files?: boolean;
        /** @description max_created_at */
        max_created_at?: string;
        /** @description min_created_at */
        min_created_at?: string;
        /** @description Set to true to search and filter projects */
        search_projects?: boolean;
        /** @description title_contains */
        title_contains?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Project'];
        };
      };
    };
  };
  projects_Projects_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ProjectRequest'];
        'application/x-www-form-urlencoded': components['schemas']['ProjectRequest'];
        'multipart/form-data': components['schemas']['ProjectRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Project'];
        };
      };
    };
  };
  projects_Projects_retrieve_2: {
    parameters: {
      query?: {
        /** @description Set to true to list projects with S3 objects */
        list_files?: boolean;
      };
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_Files_filter_retrieve: {
    parameters: {
      query?: {
        /** @description description_contains */
        description_contains?: string;
        /** @description Set to true to list projects with S3 objects */
        list_files?: boolean;
        /** @description max_created_at */
        max_created_at?: string;
        /** @description min_created_at */
        min_created_at?: string;
        /** @description Set to true to search and filter projects */
        search_files?: boolean;
        /** @description title_contains */
        title_contains?: string;
      };
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['S3File'];
        };
      };
    };
  };
  projects_Projects_Hypothesis_retrieve_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_Hypothesis_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_Hypothesis_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_Hypothesis_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  projects_Projects_Hypothesis_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_decision_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_degree_of_importance_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_dependence_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_dependence_classes_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_force_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_advanced_plots_important_features_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_dataset_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  training_results_preprocessing_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_activate_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        token: string;
        uid: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_change_password_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_delete_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_forgot_password_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_login_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['LoginRequest'];
        'application/x-www-form-urlencoded': components['schemas']['LoginRequest'];
        'multipart/form-data': components['schemas']['LoginRequest'];
      };
    };
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_logout_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_register_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  users_reset_password_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @description The token sent to reset the password */
          password_reset_token: string;
          /** @description The new password for the user */
          new_password: string;
          /** @description The new password confirmation */
          confirm_new_password: string;
        };
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': unknown;
        };
      };
      400: {
        headers: Record<string, unknown>;
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  visualization_color_palette_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['ColorPalette'][];
        };
      };
    };
  };
  visualization_color_palette_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ColorPaletteRequest'];
        'application/x-www-form-urlencoded': components['schemas']['ColorPaletteRequest'];
        'multipart/form-data': components['schemas']['ColorPaletteRequest'];
      };
    };
    responses: {
      201: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['ColorPalette'];
        };
      };
    };
  };
  visualization_color_palette_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['ColorPalette'];
        };
      };
    };
  };
  visualization_color_palette_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ColorPaletteRequest'];
        'application/x-www-form-urlencoded': components['schemas']['ColorPaletteRequest'];
        'multipart/form-data': components['schemas']['ColorPaletteRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['ColorPalette'];
        };
      };
    };
  };
  visualization_plot_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Plot'][];
        };
      };
    };
  };
  visualization_plot_style_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyle'][];
        };
      };
    };
  };
  visualization_plot_style_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['PlotStyleRequest'];
        'application/x-www-form-urlencoded': components['schemas']['PlotStyleRequest'];
        'multipart/form-data': components['schemas']['PlotStyleRequest'];
      };
    };
    responses: {
      201: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyle'];
        };
      };
    };
  };
  visualization_plot_style_option_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyleOption'];
        };
      };
    };
  };
  visualization_plot_style_option_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['PatchedPlotStyleOptionRequest'];
        'application/x-www-form-urlencoded': components['schemas']['PatchedPlotStyleOptionRequest'];
        'multipart/form-data': components['schemas']['PatchedPlotStyleOptionRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyleOption'];
        };
      };
    };
  };
  visualization_plot_style_option_children_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyleOption'];
        };
      };
    };
  };
  visualization_plot_style_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyle'];
        };
      };
    };
  };
  visualization_plot_style_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  visualization_plot_style_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['PatchedPlotStyleRequest'];
        'application/x-www-form-urlencoded': components['schemas']['PatchedPlotStyleRequest'];
        'multipart/form-data': components['schemas']['PatchedPlotStyleRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['PlotStyle'];
        };
      };
    };
  };
  visualization_plot_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        slug: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['Plot'];
        };
      };
    };
  };
  visualization_user_plot_list: {
    parameters: {
      query?: {
        /** @description Pagination cursor */
        cursor?: number;
        /** @description Pagination limit */
        limit?: number;
        /** @description Filter plots up to this date (inclusive) */
        max_selection_date?: string;
        /** @description Filter plots from this date (inclusive) */
        min_selection_date?: string;
        /** @description Project ID */
        project_id?: number;
        /** @description Search text in description or interpretation */
        search_text?: string;
        /** @description Show figure for developer */
        show_fig?: boolean;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'][];
        };
      };
    };
  };
  visualization_user_plot_create: {
    parameters: {
      query?: {
        /** @description File ID */
        file_id?: number;
        /** @description Show figure for developer */
        show_fig?: boolean;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['UserPlotRequest'];
        'multipart/form-data': components['schemas']['UserPlotRequest'];
      };
    };
    responses: {
      201: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['UserPlotRequest'];
        'multipart/form-data': components['schemas']['UserPlotRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: Record<string, unknown>;
        content?: never;
      };
    };
  };
  visualization_user_plot_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['PatchedUserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['PatchedUserPlotRequest'];
        'multipart/form-data': components['schemas']['PatchedUserPlotRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_associate_filters_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['UserPlotRequest'];
        'multipart/form-data': components['schemas']['UserPlotRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_description_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['UserPlotRequest'];
        'multipart/form-data': components['schemas']['UserPlotRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
  visualization_user_plot_display_layout_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['PatchedUserPlotRequest'];
        'application/x-www-form-urlencoded': components['schemas']['PatchedUserPlotRequest'];
        'multipart/form-data': components['schemas']['PatchedUserPlotRequest'];
      };
    };
    responses: {
      200: {
        headers: Record<string, unknown>;
        content: {
          'application/json': components['schemas']['UserPlot'];
        };
      };
    };
  };
}
