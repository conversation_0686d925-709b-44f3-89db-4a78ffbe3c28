import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'groupBy',
})
export class GroupByPipe<T> implements PipeTransform {
  transform(array: T[], key: keyof T): { key: string; value: T[] }[] {
    if (array.length === 0) {
      return [];
    }

    const grouped: Record<string, T[]> = {};
    array.forEach(item => {
      const groupKey = String(item[key]);
      if (!grouped[groupKey]) {
        grouped[groupKey] = [];
      }
      grouped[groupKey].push(item);
    });

    return Object.entries(grouped).map(([group, value]) => ({
      key: group,
      value,
    }));
  }
}
