import { Component } from '@angular/core';
import { g_const } from '../../../_utility/global_const';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-training-header',
  imports: [
    MatIconModule,
    MatButtonModule,
    CommonModule,
    MatTabsModule,
    MatCheckboxModule,
  ],
  templateUrl: './training-header.component.html',
  styleUrl: './training-header.component.css',
})
export class TrainingHeaderComponent {
  activeTabIndex = 0;
  totalTabs = 4;

  onTabChange(event: MatTabChangeEvent): void {
    this.activeTabIndex = event.index;
  }

  nextStep(): void {
    if (this.activeTabIndex < this.totalTabs - 1) {
      this.activeTabIndex++;
    }
  }

  // Handle "Back" button click
  previousStep(): void {
    if (this.activeTabIndex > 0) {
      this.activeTabIndex--;
    }
  }

  isTrainingModal = false;
  isModalOpen = false;
  g_const = g_const;
  files: File[] = [];
  openModal() {
    console.log('modal:::', this.isModalOpen);
    // this.isModalOpen = !this.isModalOpen;
    this.isModalOpen = true;
  }
  closeModal() {
    this.isModalOpen = false;
    this.isTrainingModal = false;
  }

  trainingModal() {
    this.isTrainingModal = true;
  }
}
