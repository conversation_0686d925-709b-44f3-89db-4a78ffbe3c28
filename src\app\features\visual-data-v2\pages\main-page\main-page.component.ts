import { Component, inject, OnInit } from '@angular/core';
import { visualDataStore } from '../../store/visual-data.store';
import { VisualDataHeaderComponent } from '../../components/visual-data-header/visual-data-header.component';
import { PlotsContainerComponent } from '../../components/plots-container/plots-container.component';

@Component({
  selector: 'app-main-page',
  imports: [VisualDataHeaderComponent, PlotsContainerComponent],
  templateUrl: './main-page.component.html',
  styleUrl: './main-page.component.css',
})
export class MainPageComponent implements OnInit {
  visualDataStore = inject(visualDataStore);

  ngOnInit(): void {
    console.log('Main page loaded');
  }
}
