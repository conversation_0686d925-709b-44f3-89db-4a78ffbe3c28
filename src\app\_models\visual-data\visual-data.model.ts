import { components } from '../../schema/schema';
import { GridsterItem } from 'angular-gridster2';

//TODO One of Two BackendResponse Types
export interface BackendResponse<T> {
  data: T;
  status: string;
  message: string;
  errors: string | null;
  pagination: PaginationInfo | null;
}

//TODO GET visualization/ColorPalette/
export interface ColorPalettes {
  color_palettes: ColorPalette[];
}

export interface PaginationInfo {
  count: number;
  next: string;
  previous: string;
}

export type ColorPalette = Omit<
  components['schemas']['ColorPalette'],
  'colors'
> & {
  colors: string[];
};

// TODO GET visualization/PlotStyle/${project_id}/
export interface PlotStyles {
  plot_styles: PlotStyle[];
}

//TODO POST visualization/PlotStyle/${project_id}/
export type PlotStyle = Omit<components['schemas']['PlotStyle'], 'options'> & {
  options: PlotStyleOption[];
};

//TODO children is string in schema.yml
export type PlotStyleOption = Omit<
  components['schemas']['PlotStyleOption'],
  'children'
> & {
  children: Children[];
};

//TODO not yet in schema.yml
export interface Children {
  id: number;
  name: string;
  value: string;
  data_type: string;
  description: string;
  options: PlotStyleOption[];
  parent: number;
  children: Children[];
}

export interface ColorContainer {
  id: number;
  index: number;
  colorHex: string;
}

//TODO GET visualization/UserPlot/${plot_id}/
export type UserPlot = Omit<
  components['schemas']['UserPlot'],
  'display_layout' | 'file_id'
> & {
  plot_id: number;
  file_id: number;
  filter_active: boolean;
  filter_instance_id: number | null;
  plot: PlotlyPlotSchema;
  display_layout: Partial<GridsterItem>;
  favorite?: boolean;
};

export interface PlotlyPlotSchema {
  data: Plotly.Data[];
  layout: Plotly.Layout;
}

export interface PlotResponse {
  response: { data: Plotly.Data[]; layout: Plotly.Layout };
  message?: string;
}

//TODO GET visualization/user-plot
export interface PlotResponseAll {
  plots: UserPlot[];
  meta_data: {
    total: number;
    max_page: number;
  };
}

export const ToggleButtonData: Record<string, ToggleButtonDefinition[]> = {
  make_area_chart: [
    { setting: 'make_area_chart', label: 'Area', value: true },
    { setting: 'make_area_chart', label: 'Line', value: false },
  ],
  make_bar_chart: [
    { setting: 'make_bar_chart', label: 'Bar', value: true },
    { setting: 'make_bar_chart', label: 'Coloumn', value: false },
  ],
};

export interface ToggleButtonDefinition {
  setting: string;
  label: string;
  value: boolean;
}
