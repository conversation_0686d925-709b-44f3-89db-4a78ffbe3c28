<app-training-header></app-training-header>
<!-- <div class="flex flex-col justify-between w-full h-26 px-6">
  <div class="flex flex-row justify-between">
    <p>Linear Regression</p>
    <p>24.02.2024</p>
  </div>

  <div
    class="w-full bg-white h-[68px] border-1 rounded-xl flex flex-row items-center pr-4"
    
    >
    <div>
      <button
        class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1"
        
      >
        more_vert
      </button>

    </div>
    <div class="flex justify-center flex-col">
      <p class="font-normal leading-6 m-0">XGBoost Classifier</p>
      <p class="m-0">status</p>
    </div>
    <button class="ml-auto">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
  <div
    class="w-full bg-white h-[68px] border-1 rounded-xl flex flex-row items-center mt-1 pr-4"
  >
    <div>
      <button
        class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1"
      >
        more_vert
      </button>
    </div>
    <div class="flex justify-center flex-col">
      <p class="font-normal leading-6 m-0">Random Forecast Classifier</p>
      <p class="m-0">status</p>
    </div>
    <button class="ml-auto">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
</div> -->

<!-- training-result module -->

<main class="grid grid-cols-2 xl:grid-cols-3 gap-7 p-4">
  <section class="rounded-xl h-[300px] flex-1 flex flex-col p-2">
    <mat-nav-list>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 1001"
        (click)="openAddForm(1001)"
        >Pipeline</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 2"
        (click)="openAddForm(2)"
        >Inference</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 3"
        (click)="openAddForm(3)"
        >Performance Evaluation</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 4"
        (click)="openAddForm(4)"
        >Finetuning</mat-list-item
      >
      <mat-divider class="border-gray-300"></mat-divider>
      <mat-list-item
        class="rounded-none"
        [activated]="activeItem === 5"
        (click)="openAddForm(5)"
        >Advanced Analysis</mat-list-item
      >
    </mat-nav-list>
  </section>
  <section class="col-span-1 xl:col-span-2" *ngIf="isPipeLine">
    <mat-tab-group
      animationDuration="0ms"
      (selectedTabChange)="onTabChange($event)"
      [selectedIndex]="activeTabIndex">
      <mat-tab label="Dataset Information">
        <div class="mt-6">
          <p class="m-0">
            836 rows processed in total 2 files for this training
          </p>
          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-medium font-sans leading-6">Columns</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <button class="custom-button w-[90px] mr-2">Encoding</button>
          <button class="custom-button w-[79px] mr-2">Scaling</button>
          <button class="custom-button w-[82px] mr-2">Outliers</button>
          <button class="custom-button w-[121px] mr-2">NaN Handling</button>
          <button class="custom-button w-[128px] mr-2">Anonymization</button>

          <p class="mt-6">
            836 rows processed in total 2 files for this training
          </p>

          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-medium font-sans leading-6">Images</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <button class="custom-button w-[90px] mr-2">Encoding</button>
          <button class="custom-button w-[79px] mr-2">Scaling</button>
          <button class="custom-button w-[82px] mr-2">Outliers</button>
        </div>
      </mat-tab>
      <mat-tab label="Preprocessing">
        <div class="mt-6">
          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-medium font-sans leading-6">Columns</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>

          <div class="flex flex-row">
            <button
              mat-stroked-button
              class="bg-[#296197] border-none text-white rounded-lg mr-2 px-[58px]">
              Custom Sauce
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Encoding
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Scaling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Outliers
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              NaN Handling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Anonymization
            </button>
          </div>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Custom
          </button>
          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-medium font-sans leading-6">Image Data</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <div class="flex flex-row">
            <button
              mat-stroked-button
              class="bg-[#296197] border-none text-white rounded-lg px-[58px] mr-2">
              Custom Sauce
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Resize
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Normalize
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              ShiftScaleRotate
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              NaN Handling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Anonymization
            </button>
          </div>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Random Brightness Contrast
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            GrayScale
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Conversion
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Label
          </button>
        </div>
        <mat-card appearance="outlined" class="bg-white h-[294px] mt-4">
          <div
            class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
            <div>
              <button
                class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                more_vert
              </button>
            </div>
            <div class="flex justify-center flex-col">
              <p class="font-medium font-sans leading-6 m-0">
                Preprocessing Methods
              </p>
              <p class="m-0 s-[14px]">Overview of PreProcessing Methods</p>
            </div>
            <div class="ml-auto flex justify-center">
              <p class="font-medium font-sans leading-6 m-0 px-4">
                Show Information
              </p>

              <button class="flex justify-center px-4">
                <mat-icon>crop_free</mat-icon>
              </button>
            </div>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="Augmentation">
        <div class="mt-6">
          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-normal font-sans">Columns</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>

          <div class="flex flex-row">
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Encoding
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Scaling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Outliers
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              NaN Handling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Anonymization
            </button>
          </div>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Custom
          </button>
          <div class="flex items-center space-x-2 mt-4">
            <p class="mt-3 font-normal font-sans">Image Data</p>

            <mat-icon color="material-symbols-outlined"> info</mat-icon>
          </div>
          <div class="flex flex-row">
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Resize
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Normalize
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              ShiftScaleRotate
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              NaN Handling
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Anonymization
            </button>
          </div>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Random Brightness Contrast
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            GrayScale
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Conversion
          </button>
          <button mat-stroked-button class="bg-white rounded-lg mr-2 mt-2">
            Label
          </button>
        </div>
        <mat-card appearance="outlined" class="bg-white h-[294px] mt-4">
          <div
            class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
            <div>
              <button
                class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                more_vert
              </button>
            </div>
            <div class="flex justify-center flex-col">
              <p class="font-medium font-sans leading-6 m-0">
                Preprocessing Methods
              </p>
              <p class="m-0 s-[14px]">Overview of PreProcessing Methods</p>
            </div>
            <div class="ml-auto flex justify-center">
              <p class="font-medium font-sans leading-6 m-0 px-4">
                Show Information
              </p>

              <button class="flex justify-center px-4">
                <mat-icon>crop_free</mat-icon>
              </button>
            </div>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="Datasplit">
        <h5 class="mt-6">Split</h5>
        <main>
          <mat-card appearance="outlined" class="bg-white h-[294px] mt-4">
            <div
              class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
              <div>
                <button
                  class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                  more_vert
                </button>
              </div>
              <div class="flex justify-center flex-col">
                <p class="font-medium font-sans leading-6 m-0">
                  Preprocessing Methods
                </p>
                <p class="m-0 s-[14px]">Overview of PreProcessing Methods</p>
              </div>
              <div class="ml-auto flex justify-center">
                <p class="font-medium font-sans leading-6 m-0 px-4">
                  Show Information
                </p>

                <button class="flex justify-center px-4">
                  <mat-icon>crop_free</mat-icon>
                </button>
              </div>
            </div>
          </mat-card>
        </main>
      </mat-tab>
      <mat-tab label="Model">
        <div class="mt-6">
          <div class="flex flex-row items-center space-x-2 mt-4">
            <h5 class="m-0 font-sans">Model</h5>

            <mat-icon color="material-symbols-outlined">info</mat-icon>
          </div>
          <div class="flex flex-row">
            <button
              mat-stroked-button
              class="bg-[#296197] border-none text-white rounded-lg mr-2 px-4">
              ResNet50
            </button>
            <button
              mat-stroked-button
              class="bg-[#296197] border-none text-white rounded-lg mr-2 px-4">
              TabularLayers
            </button>
            <button mat-stroked-button class="bg-white rounded-lg mr-2">
              Custom
            </button>
          </div>
          <div class="flex flex-row justify-between mt-4">
            <div class="w-[409px] bg-white h-[520px] rounded-2xl"></div>
            <div class="w-[200px] bg bg-white h-20 rounded-2xl"></div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <section
    class="col-span-1 xl:col-span-2"
    *ngIf="isInference; &quot;w-full&quot;">
    <main class="flex flex-row justify-between">
      <div>
        <p class="font-sans font-medium text-[28px] leading-[28px] m-0 mb-2">
          Inference
        </p>
        <p>Run your model on new data</p>
      </div>
      <div>
        <button
          mat-flat-button
          class="custom-Height buttonBg text-white bg-[#1d4d7a]"
          (click)="customModal()">
          Run Modal
        </button>
      </div>
    </main>
    <p class="font-sans font-medium text-[28px] leading-[28px]">
      Previous Inference Results by the Model
    </p>

    <h4>Tabular Data</h4>
    <div>
      <p>Please choose an row for details.</p>
      <table class="h-[182px] bg-white w-full rounded-lg">
        <p class="p-4">table will be there</p>
      </table>
    </div>
    <section class="flex flex-row mt-6">
      <div class="w-full">
        <p class="font-sans font-medium">Image Data</p>
        <p>Please choose an image for details.</p>
        <p class="font-medium font-sans">Choose Image</p>
        <div
          class="w-full px-3 border-1 bg-white rounded-xl flex flex-row justify-between">
          <p class="my-[14px]">xmin, ymin, xmax, ymax</p>
          <button class="ml-auto">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>
        <main class="mt-4 w-full">
          <div class="flex flex-row items-center justify-between">
            <p class="m-0">Train</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              1000
            </button>
            <p class="m-0">Test</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              18
            </button>
            <p class="m-0">Validation</p>
            <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
              1000
            </button>
          </div>
        </main>
      </div>

      <div class="ml-auto">
        <mat-card class="w-[300px] shadow-lg px-4 ml-4">
          <mat-card-content class="m-0 p-0">
            <div class="flex flex-row">
              <p class="font-sans font-medium m-0">View</p>
              <mat-radio-button class="ml-auto"></mat-radio-button>
            </div>
          </mat-card-content>
          <img
            mat-card-image
            src="https://cdn.pixabay.com/photo/2024/01/15/11/36/batman-8510027_640.png"
            alt="Sample Image"
            class="w-full h-48 object-cover rounded-md mb-4" />
        </mat-card>
      </div>
    </section>
  </section>
  <section class="col-span-1 xl:col-span-2" *ngIf="isPerformanceEvaluation">
    <p class="font-sans font-medium text-[28px] leading-[28px]">
      Performance Evaluation
    </p>
    <p>
      Inspect training metrics to asses the success of the training and evaluate
      the performance of the model. Make sure to test the inference on new data
      to evaluate the generalization capabilities of your model.
    </p>
    <mat-card appearance="outlined" class="bg-white h-[294px] mt-4">
      <div
        class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
        <div>
          <button
            class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
            more_vert
          </button>
        </div>
        <div class="flex justify-center flex-col">
          <p class="font-medium font-sans leading-6 m-0">
            Preprocessing Methods
          </p>
          <p class="m-0 s-[14px]">Overview of PreProcessing Methods</p>
        </div>
        <div class="ml-auto flex justify-center">
          <p class="font-medium font-sans leading-6 m-0 px-4">
            Show Information
          </p>

          <button class="flex justify-center px-4">
            <mat-icon>crop_free</mat-icon>
          </button>
        </div>
      </div>
    </mat-card>
  </section>
  <section class="w-[817px]" *ngIf="isFinetuning">
    <div>
      <p class="font-sans font-medium text-[28px] leading-[28px] m-0">
        Finetuning
      </p>
      <p class="mt-1">Finetune your model with new methods.</p>
    </div>

    <div class="mt-6">
      <p class="mt-3 font-medium font-sans leading-6 m-0">Hyperparameters</p>
      <p class="mt-1">
        Choose the amount of hyperparameters that should be tested. Keep in mind
        that finetuning can take a lot of resources. Make sure to keep the tests
        to a required minimum. You can try multiple times and set different
        initital values.
      </p>
    </div>
    <div class="flex flex-row justify-between gap-2">
      <button mat-stroked-button class="custom-button-2">Nucleous</button>
      <button mat-stroked-button class="custom-button-2">Custom Sauce</button>
      <button mat-stroked-button class="custom-button-2">Mitochondrium</button>
      <button mat-stroked-button class="custom-button-2">Custom D</button>
    </div>
    <mat-icon class="material-symbols-outlined mat-icon-1 mt-4"> add </mat-icon>
    <div class="flex flex-col justify-between w-full h-26 mt-4">
      <div class="flex flex-row justify-between">
        <p>Linear Regression</p>
      </div>

      <div
        class="w-full bg-white h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
        <div>
          <button
            class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
            more_vert
          </button>
        </div>
        <div class="flex justify-center flex-col">
          <p class="font-normal leading-6 m-0">F1 Score 76 %</p>
          <p class="m-0">status</p>
        </div>
        <button class="ml-auto">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
      <div
        class="w-full bg-white h-[68px] border-1 rounded-xl flex flex-row items-center mt-1 pr-4">
        <div>
          <button
            class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
            more_vert
          </button>
        </div>
        <div class="flex justify-center flex-col">
          <p class="font-normal leading-6 m-0">F1 Score 79 %</p>
          <p class="m-0">status</p>
        </div>
        <button class="ml-auto">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>
  </section>
  <section *ngIf="isAdvancedAnalysis" class="col-span-1 lg:col-span-2">
    <div>
      <p class="font-sans font-medium text-[28px] leading-[28px] m-0">
        Advanced Analysis
      </p>
    </div>
    <mat-tab-group
      animationDuration="0ms"
      (selectedTabChange)="onTabChange($event)"
      [selectedIndex]="activeTabIndex">
      <mat-tab label="Summary">
        <div class="mt-6 !p-0">
          <p class="mt-3 font-medium font-sans leading-6">Columns</p>

          <div
            class="w-full bg-white h-[68px] border-1 rounded-xl flex flex-row items-center mt-1 pr-4">
            <div>
              <button
                class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                polyline
              </button>
            </div>
            <div class="flex justify-center">
              <p class="mt-3 font-medium font-sans leading-6">
                Logistic Regression
              </p>
            </div>
            <div class="ml-auto flex flex-row justify-center items-center">
              <p>mAP:98.0 %</p>
              <p>mAP:98.0 %</p>
              <p>mAP:98.0 %</p>
            </div>
          </div>

          <p class="mt-3 font-medium font-sans leading-6">Top Drivers</p>
          <div class="space-x-2 mt-4">
            <div class="flex flex-row h-6 gap-2">
              <p>Age</p>
              <mat-progress-bar
                mode="determinate"
                class="custom-progress-bar"
                value="30"></mat-progress-bar>
            </div>
            <div class="flex flex-row gap-2">
              <p>Age</p>
              <mat-progress-bar
                mode="determinate"
                value="60"></mat-progress-bar>
            </div>

            <div class="flex flex-row gap-2">
              <p>Age</p>
              <mat-progress-bar
                mode="determinate"
                value="40"
                style="height: 16px"></mat-progress-bar>
            </div>
          </div>
          <div class="flex flex-row mt-4">
            <mat-card
              appearance="outlined"
              class="bg-white h-[294px] w-[527px] mr-6">
              <div
                class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
                <div>
                  <button
                    class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                    more_vert
                  </button>
                </div>
                <div class="flex justify-center flex-col">
                  <p class="font-medium font-sans leading-6 m-0">Title</p>
                  <p class="m-0 s-[14px]">Filename.csv</p>
                </div>
                <div class="ml-auto flex justify-center">
                  <p class="font-medium font-sans leading-6 m-0 px-4">
                    Show Information
                  </p>

                  <button class="flex justify-center px-4">
                    <mat-icon>crop_free</mat-icon>
                  </button>
                </div>
              </div>
            </mat-card>
            <div class="w-[265px] h-200px bg-white rounded-lg"></div>
          </div>
        </div>
      </mat-tab>
      <mat-tab label="Model Inherent Explainability">
        <div class="mt-6">
          <mat-card appearance="outlined" class="bg-white h-[500px] mt-4">
            <div
              class="w-full bg-gray-100 h-[68px] border-1 rounded-xl flex flex-row items-center pr-4">
              <div>
                <button
                  class="material-icons-outlined rounded-full bg-slate-200 mr-4 w-10 h-10 p-2 gap-2 m-1">
                  more_vert
                </button>
              </div>
              <div class="flex justify-center flex-col">
                <p class="font-medium font-sans leading-6 m-0">Title</p>
                <p class="m-0 s-[14px]">Filename.csv</p>
              </div>
              <div class="ml-auto flex justify-center">
                <p class="font-medium font-sans leading-6 m-0 px-4">
                  Show Information
                </p>

                <button class="flex justify-center px-4">
                  <mat-icon>crop_free</mat-icon>
                </button>
              </div>
            </div>
          </mat-card>
        </div>
      </mat-tab>
      <mat-tab label="Model Agnostic Explainability">
        <div class="mt-6"></div>
      </mat-tab>
    </mat-tab-group>
  </section>
</main>

<!-- custome modal -->
<main
  *ngIf="isCustomModalOpne"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="bg-[#F1F3F9] rounded-xl shadow-lg relative w-[860px] h-[597px] px-4 py-4">
    <div class="flex justify-between items-center object-center my-4">
      <h6 class="font-medium">New Training with Hyperparameter Tuning</h6>

      <button
        mat-icon-button
        (click)="closeCustomModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <section class="h-[429px]">
      <mat-tab-group
        animationDuration="0ms"
        (selectedTabChange)="onTabChange($event)"
        [selectedIndex]="activeTabIndex">
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="material-symbols-outlined"> looks_one </mat-icon>
            <p class="ml-[10px] m-0">Initial Values</p>
          </ng-template>
          <div class="mt-6">
            <h5 class="m-0">Initial Values</h5>
            <p class="m-0">Choose your main goals of the analysis</p>
            <main class="mt-4 flex flex-row">
              <div>
                <p class="font-sans font-medium leading-7">Hyperparamater</p>
                <div
                  class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[540px]">
                  <div class="flex justify-center">
                    <p class="font-normal leading-6 m-0">lr0</p>
                  </div>
                  <button class="ml-auto flex items-center">
                    <mat-icon>keyboard_arrow_down</mat-icon>
                  </button>
                </div>
                <div
                  class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[540px] mt-2">
                  <div class="flex justify-center">
                    <p class="font-normal leading-6 m-0">lr0</p>
                  </div>
                  <button class="ml-auto flex items-center">
                    <mat-icon>keyboard_arrow_down</mat-icon>
                  </button>
                </div>
                <div
                  class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[540px] mt-2">
                  <div class="flex justify-center">
                    <p class="font-normal leading-6 m-0">lr0</p>
                  </div>
                  <button class="ml-auto flex items-center">
                    <mat-icon>keyboard_arrow_down</mat-icon>
                  </button>
                </div>
              </div>
              <div class="ml-2">
                <p class="font-sans font-medium leading-7">Hyperparamater</p>
                <div class="flex items-center">
                  <div
                    class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[160px]">
                    <p class="font-normal leading-6 m-0">0.001</p>
                  </div>
                  <button class="h-12 ml-4">
                    <mat-icon>delete_outline</mat-icon>
                  </button>
                </div>
                <div class="flex items-center mt-2">
                  <div
                    class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[160px]">
                    <p class="font-normal leading-6 m-0">0.001</p>
                  </div>
                  <button class="h-12 ml-4">
                    <mat-icon>delete_outline</mat-icon>
                  </button>
                </div>
                <div class="flex items-center mt-2">
                  <div
                    class="bg-white border-1 rounded-xl flex flex-row items-center px-4 h-12 w-[160px]">
                    <p class="font-normal leading-6 m-0">0.001</p>
                  </div>
                  <button class="h-12 ml-4">
                    <mat-icon>delete_outline</mat-icon>
                  </button>
                </div>
              </div>
            </main>
            <!-- <div class="flex flex-row items-center space-x-2 mt-4">

              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-normal font-sans">Task Overview</p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div> -->
          </div>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="material-symbols-outlined"> looks_two </mat-icon>
            <p class="ml-[10px] m-0">Value Ranges</p>
          </ng-template>
          <div class="mt-6">
            <h5 class="m-0">Value Ranges</h5>
            <p>
              Based on the chosen analysis goal Object Detection, the model will
              predict
            </p>
            <div class="flex flex-row justify-between gap-2 mt-4 py-4">
              <button mat-stroked-button class="custom-button-2">
                Nucleous
              </button>
              <button mat-stroked-button class="custom-button-2">
                Custom Sauce
              </button>
              <button mat-stroked-button class="custom-button-2">
                Mitochondrium
              </button>
              <button mat-stroked-button class="custom-button-2">
                Custom D
              </button>
            </div>
          </div>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="material-symbols-outlined"> looks_3 </mat-icon>
            <p class="ml-[10px] m-0">Optimization Method</p>
          </ng-template>
          <h6 class="mt-6 m-0">Optimization Method</h6>
          <p class="mt-1">Choose one optimization methods.</p>

          <main>
            <div class="flex flex-row items-center gap-1">
              <p class="mt-3 font-medium font-sans leading-7 m-0">
                Standard Algorithms Hyperparameter Finetuning
              </p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
            <p class="">These methods were applied during the last training.</p>

            <div class="flex flex-row mt-1 gap-1 py-1">
              <button mat-stroked-button class="custom-button-3 px-4">
                Grid Search
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Random Search
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Bayesian Optimization
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Tree-structured Parzen estimators (TPE)
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Custom
              </button>
            </div>
          </main>
          <main>
            <div class="flex flex-row items-center gap-1">
              <p class="mt-3 font-medium font-sans leading-7 m-0">
                Standard Algorithms Hyperparameter Finetuning
              </p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
            <p class="">These methods were applied during the last training.</p>

            <div class="flex flex-row mt-1 gap-1 py-1">
              <button mat-stroked-button class="custom-button-3 px-4">
                Grid Search
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Random Search
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Bayesian Optimization
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Tree-structured Parzen estimators (TPE)
              </button>
              <button mat-stroked-button class="custom-button-3 px-4">
                Custom
              </button>
            </div>
          </main>
        </mat-tab>
        <mat-tab label="Recommendations">
          <ng-template mat-tab-label>
            <mat-icon> looks_4 </mat-icon>
            <p class="ml-[10px] m-0">Recommendations</p>
          </ng-template>
          <div class="mt-6">
            <h5 class="m-0 font-sans">Recommendations</h5>
            <p>
              Hyperparameter Finetuning can take some time as multiple trainign
              runs need to be executed. Compare results of multiple runs and
              adjust the hyperparameters based on your results:
            </p>
            <div class="ml-4">
              <p class="font-sans font-medium left-7 m-0">Learning Rate:</p>
              <p class="mt-2">
                If the model is not converging (too slowly), adjust the learning
                rate. Reduce it if the model is oscillating or overshooting;
                increase it if the learning rate is too small.
              </p>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </section>

    <footer class="flex flex-row justify-between">
      <div class="flex justify-start mt-4">
        <button
          mat-flat-button
          class="bg-white text-txt-color"
          (click)="previousStep()">
          back
        </button>
      </div>
      <div class="mt-4 flex items-center">
        <p class="m-0 font-sans font-medium">
          Step {{ activeTabIndex + 1 }} of {{ totalTabs }}
        </p>
      </div>
      <div class="flex justify-start mt-4">
        <button
          mat-flat-button
          class="!bg-[#296197]"
          (click)="trainingModal()"
          (click)="nextStep()">
          Next
        </button>
      </div>
    </footer>
  </div>
</main>
