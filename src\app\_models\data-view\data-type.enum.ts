export class DataTypes {
  CharField = {
    description:
      'Used for short, single-line text fields, such as names or titles.',
    options: null,
  };
  FloatField = {
    description: 'Used for storing floating-point numbers (decimals).',
    options: [
      {
        precision: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      },
    ],
  };
  TextField = {
    description:
      'Used for longer, multi-line text fields, such as descriptions or comments.',
    options: [
      {
        richText: false,
      },
    ],
  };
  IntegerField = {
    description: 'Used for storing whole numbers (integers).',
    options: null,
  };

  BooleanField = {
    description:
      'Used for storing True or False values. Similar to a CheckBox.',
    options: [
      {
        Text: {
          trueLabel: 'True',
          falseLabel: 'False',
        },
        Checkbox: {
          trueLabel: 'Empty',
          falseLabel: 'Checked',
        },
        Circle: {
          trueLabel: 'Outlined',
          falseLabel: 'Filled',
        },
      },
    ],
  };
  DateField = {
    description: 'Used for storing date values (without time).',
    options: [
      {
        dateFormatOptions: [
          'YYYY-MM-DD',
          'MM/DD/YYYY',
          'DD/MM/YYYY',
          'YYYY/MM/DD',
          'MM-DD-YYYY',
          'DD-MM-YYYY',
          'YYYY.MM.DD',
        ],
      },
    ],
  };
  DateTimeField = {
    descriptio: 'Used for storing date and time values.',
    options: [
      {
        dateTimeFormatOptions: [
          'YYYY-MM-DD HH:MM:SS',
          'MM/DD/YYYY HH:MM:SS',
          'DD/MM/YYYY HH:MM:SS',
          'YYYY/MM/DD HH:MM:SS',
          'MM-DD-YYYY HH:MM:SS',
          'DD-MM-YYYY HH:MM:SS',
          'YYYY.MM.DD HH:MM:SS',
        ],
      },
    ],
  };
  Year = {
    description: 'Used for representing a year (e.g., 2022).',
    options: null,
  };
  TimeField = {
    description: 'Used for representing time values (e.g., 15:30:00).',
    options: [
      {
        timeFormats: ['HH:MM:SS', 'HH:MM', 'hh:mm:ss A', 'hh:mm A'],
      },
    ],
  };
  DurationField = {
    descriptio: 'Used for storing time durations or intervals.',
    options: [
      {
        timeFormats: [
          'h:mm',
          'hh:mm:ss',
          'hh:mm:ss.s',
          'hh:mm:ss.ss',
          'hh:mm:ss.sss',
          'hh:mm:ss.ssss',
        ],
      },
    ],
  };
  EmailField = {
    description: 'Used for storing email addresses.',
    options: null,
  };
  URLField = {
    description: 'Used for storing URLs.',
    options: null,
  };
  FileField = {
    description: 'Used for file uploads.',
    options: null,
  };
  ImageField = {
    description: 'Used for image uploads.',
    options: null,
  };
  ForeignKey = {
    description:
      'Used to create relationships between models. It represents a one-to-many or many-to-one relationship.',
    options: null,
  };
  ManyToManyField = {
    descriptio: 'Used to create many-to-many relationships between models.',
    options: null,
  };
  JSONField = {
    description: 'Used for storing JSON-encoded data.',
    options: null,
  };
  BinaryField = {
    description: 'Used for storing binary data, such as file contents.',
    options: null,
  };
  UUIDField = {
    description: 'Used for storing universally unique identifiers.',
    options: null,
  };
  DecimalField = {
    description:
      'Used for storing fixed-point decimal numbers with a specified number of decimal places.',
    options: [
      {
        precision: 2,
      },
    ],
  };
  Percentage = {
    description: 'Used for representing percentage values.',
    options: [
      {
        precisions: [
          '0 (No Decimal Places)',
          '1 (1 Decimal Place)',
          '2 (2 Decimal Places)',
          '3 (3 Decimal Places)',
          '4 (4 Decimal Places)',
        ],
        displayAsProgress: true,
      },
    ],
  };
  Currency = {
    description: 'Used for representing currency values.',
    options: [
      {
        currencyLocales: {
          'United States (en-US)': 'en-US',
          'United Kingdom (en-GB)': 'en-GB',
          'Eurozone (en-EU)': 'en-EU',
          'Canada (en-CA)': 'en-CA',
          'Australia (en-AU)': 'en-AU',
          'Japan (ja-JP)': 'ja-JP',
          'China (zh-CN)': 'zh-CN',
          'India (en-IN)': 'en-IN',
          'Brazil (pt-BR)': 'pt-BR',
          'South Africa (en-ZA)': 'en-ZA',
        },
        currencyCodes: {
          'US Dollar (USD)': 'USD',
          'British Pound (GBP)': 'GBP',
          'Euro (EUR)': 'EUR',
          'Canadian Dollar (CAD)': 'CAD',
          'Australian Dollar (AUD)': 'AUD',
          'Japanese Yen (JPY)': 'JPY',
          'Chinese Yuan (CNY)': 'CNY',
          'Indian Rupee (INR)': 'INR',
          'Brazilian Real (BRL)': 'BRL',
          'South African Rand (ZAR)': 'ZAR',
        },
      },
    ],
  };
  PhoneNumber = {
    description: 'Used for representing phone numbers.',
    options: null,
  };
}

export enum ColumnTypeEnum {
  CharField = 'CharField',
  FloatField = 'FloatField',
  TextField = 'TextField',
  IntegerField = 'IntegerField',
  BooleanField = 'BooleanField',
  DateField = 'DateField',
  DateTimeField = 'DateTimeField',
  Year = 'Year',
  TimeField = 'TimeField',
  DurationField = 'DurationField',
  EmailField = 'EmailField',
  URLField = 'URLField',
  FileField = 'FileField',
  ImageField = 'ImageField',
  ForeignKey = 'ForeignKey',
  ManyToManyField = 'ManyToManyField',
  JSONField = 'JSONField',
  BinaryField = 'BinaryField',
  UUIDField = 'UUIDField',
  DecimalField = 'DecimalField',
  Percentage = 'Percentage',
  Currency = 'Currency',
  PhoneNumber = 'PhoneNumber',
}
