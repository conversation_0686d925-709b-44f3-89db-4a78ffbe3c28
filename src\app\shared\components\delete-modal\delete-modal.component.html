<div
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[440px] h-[276px]">
    <div class="flex justify-between items-center object-center p-2">
      <h3 class="font-medium">Delete {{ modalTitle }}</h3>
      <button
        mat-icon-button
        (click)="cancel()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300"></div>

    <div class="p-4">
      <div>
        <mat-card-title
          >Are you sure you want to delete this
          {{ modalTitle }}?</mat-card-title
        >
        <p class="font-normal text-sm pt-4">
          Once deleted, your {{ modalTitle }} will be permanently lost and
          cannnot be recovered.
        </p>
      </div>
    </div>
    <div class="flex justify-between mt-8">
      <button mat-flat-button (click)="cancel()">No</button>
      <button mat-flat-button (click)="delete()">Ok</button>
    </div>
  </div>
</div>
