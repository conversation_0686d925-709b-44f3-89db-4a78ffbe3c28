import {
  AfterViewInit,
  Component,
  ElementRef,
  Inject,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { DataviewService } from '../../../services/data-view.service';
import { ToastrService } from 'ngx-toastr';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-images-statistics',
  templateUrl: './images-statistics.component.html',
  styleUrl: './images-statistics.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class ImagesStatisticsComponent implements OnInit, AfterViewInit {
  @Input() fileID: number | null = null;
  @Input() isStatisticsLoading = false;
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;

  graphData: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  isFullScreen: boolean | undefined;
  activeGraphIndex: number | null = null;
  totalPages = 0;
  currentPage = 1;
  pageSize = 2;

  constructor(
    private dataViewService: DataviewService,
    private toastrService: ToastrService,
    @Inject(DOCUMENT) private document: Document,
  ) {}
  ngOnInit(): void {
    this.graphData = [];
    this.currentPage = 1;
    this.isStatisticsLoading = true;
    this.getStatisticsData(this.fileID, this.currentPage, this.pageSize);
  }

  ngAfterViewInit(): void {
    if (this.scrollContainer) {
      this.scrollContainer.nativeElement.addEventListener(
        'scroll',
        this.onScroll.bind(this),
      );
      console.log(this.scrollContainer);
    }
  }
  getStatisticsData(
    fileID: number | null,
    plotPage: number,
    plotCount: number,
  ): void {
    this.isStatisticsLoading = true;

    this.dataViewService
      .getStatisticsData(fileID, plotPage, plotCount)
      .subscribe(
        response => {
          this.graphData.push(...Object.values(response.data));
          if (this.graphData) {
            this.isStatisticsLoading = false;
          }
          this.currentPage = response.pagination?.current_page ?? 0;
          this.totalPages = response.pagination?.total_pages ?? 0;
        },
        error => {
          console.error('Failed to load statistics data:', error);
          this.isStatisticsLoading = false;
        },
      );
  }
  downloadFile(response: Blob, fileName: string): void {
    const url = URL.createObjectURL(response);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
  }
  downloadSinglePlot(plotname: string): void {
    this.dataViewService.downloadSinglePlot(this.fileID, plotname).subscribe(
      response => {
        const fileName = `Image_${this.fileID}.png`;
        this.downloadFile(response, fileName);
      },
      error => {
        console.error('Download failed:', error);
      },
    );
  }
  openFullscreen(index: number): void {
    const elem = document.getElementById(`graph-${index}`);
    if (elem?.requestFullscreen) {
      elem.requestFullscreen();
    }
    this.isFullScreen = true;
    this.activeGraphIndex = index;
  }
  closeFullscreen(): void {
    if (this.document.exitFullscreen) {
      this.document.exitFullscreen();
    }
    this.isFullScreen = false;
    this.activeGraphIndex = null;
  }
  onScroll(): void {
    if (this.scrollContainer) {
      const container = this.scrollContainer.nativeElement;
      const scrollPosition = container.scrollTop + container.clientHeight;
      const threshold = container.scrollHeight;
      if (scrollPosition >= threshold - 100) {
        this.loadMoreData();
      }
    }
  }
  loadMoreData(): void {
    if (this.isStatisticsLoading || this.currentPage >= this.totalPages) {
      return;
    }
    this.currentPage++;
    this.getStatisticsData(this.fileID, this.currentPage, this.pageSize);
  }
}
