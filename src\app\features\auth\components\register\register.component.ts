import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { ToastrService } from 'ngx-toastr';
import { g_const } from '../../../../_utility/global_const';
import { AuthService } from '../../services/auth.service';
import { MatIcon } from '@angular/material/icon';
import { SharedModule } from '../../../../shared/shared.module';
import { MatButtonModule } from '@angular/material/button';
import { emailValidator } from '../../services/validators';

@Component({
  selector: 'app-register',
  imports: [
    CommonModule,
    MatIcon,
    MatButtonModule,
    ReactiveFormsModule,
    SharedModule,
  ],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css',
})
export class RegisterComponent {
  loading = false;
  g_const = g_const;
  signupForm: FormGroup;
  passwordFieldType = 'password';
  repeatPasswordFieldType = 'password';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private toastr: ToastrService,
  ) {
    this.signupForm = this.formBuilder.group(
      {
        email: ['', [Validators.required, Validators.email, emailValidator]],
        first_name: ['', Validators.required],
        last_name: ['', Validators.required],
        password1: ['', [Validators.required, Validators.minLength(8)]],
        password2: ['', [Validators.required, Validators.minLength(8)]],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  passwordMatchValidator(
    formGroup: FormGroup,
  ): null | { passwordMismatch: true } {
    const password1 = formGroup.get('password1')?.value;
    const password2 = formGroup.get('password2')?.value;
    return password1 === password2 ? null : { passwordMismatch: true };
  }

  togglePasswordVisibility(): void {
    this.passwordFieldType =
      this.passwordFieldType === 'password' ? 'text' : 'password';
  }

  toggleRepeatPasswordFieldType(): void {
    this.repeatPasswordFieldType =
      this.repeatPasswordFieldType === 'password' ? 'text' : 'password';
  }

  onSubmit(): void {
    if (this.signupForm.invalid) {
      this.signupForm.markAllAsTouched();
      return;
    }

    this.loading = true;
    this.authService.register(this.signupForm.value).subscribe({
      next: res => {
        this.toastr.success(res.message);
        this.loading = false;
        this.router.navigate(['/auth/login']);
      },
      error: err => {
        this.loading = false;
        this.toastr.error(err.error?.error || 'Registration failed.');
      },
    });
  }

  navigateToLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
