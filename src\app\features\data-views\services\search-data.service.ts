import { Injectable } from '@angular/core';
import { DataviewService } from './data-view.service';

@Injectable({
  providedIn: 'root',
})
export class SearchService {
  constructor(private dataViewService: DataviewService) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  processSearchResponse(data: any): { data: any[]; files: any[] } {
    if (data.status === 'success') {
      if (data.data.length > 0) {
        const processedData = data.data.map(
          (folder: {
            folder_name: string;
            files: unknown[];
            subfolders: unknown[];
          }) => ({
            folder_name: folder.folder_name,
            files: folder.files || [],
            subfolders: folder.subfolders || [],
          }),
        );

        const files = processedData.reduce(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (acc: string | any[], folder: { files: any }) =>
            acc.concat(folder.files),
          [],
        );
        return { data: processedData, files };
      } else {
        // No matching data case
        console.warn('No data matches the query.');
        return { data: [], files: [] };
      }
    } else {
      // Error case
      console.error('Error retrieving filtered data:', data.message);
      return { data: [], files: [] };
    }
  }
  handleSearchError(
    message: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    error: any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): { data: any[]; files: any[] } {
    console.error(message, error);

    // Return empty fallback values to avoid breaking the application.
    return { data: [], files: [] };
  }
}
