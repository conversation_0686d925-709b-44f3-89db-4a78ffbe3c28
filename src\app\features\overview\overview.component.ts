import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OverviewService } from '../../services/overview.service';
import { g_const } from '../../_utility/global_const';
import { SearchOptions } from '../../_models/common.model';
import {
  Hypothesis,
  HypothesisList,
  HypothesisPayload,
  NewHypothesisFormValues,
  ProjectData,
  ProjectDataDetails,
} from '../dashborad/models/project.model';
import { ToastrService } from 'ngx-toastr';
import { IntroService } from '../../introjs.service';
import { HypothesisFilter } from '../dashborad/models/project.model';
import { BackendResponse } from '../../_models/visual-data/visual-data.model';

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class OverviewComponent implements OnInit, AfterViewInit {
  projectData: ProjectDataDetails | null = null;
  g_const = g_const;
  hypothesis_data: Hypothesis[] = [];
  searchOptions: SearchOptions = {};
  formData: Hypothesis | null = null;
  hypothesisDeleteTitle = '';
  hypothesisPopup = false;
  hypothesisDeletePopup = false;
  loading = false;
  project_id: string | null = null;
  id: number | string | '' = '';
  showTooltip = false;
  @ViewChild('titleElement', { static: false }) titleElement!: ElementRef;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private overviewService: OverviewService,
    private toastrService: ToastrService,
    private introService: IntroService,
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.loading = true;
      this.project_id = params.get('id');
      if (this.project_id) {
        localStorage.setItem('project_id', this.project_id);

        this.fetchProjectData(this.project_id);
        this.getHypothesis(this.project_id);
      }
    });
    this.introService.startIntroIfNeeded('overview');
  }
  ngAfterViewInit() {
    setTimeout(() => {
      const element = this.titleElement.nativeElement;
      this.showTooltip = element.scrollWidth > 450;
    }, 500);
  }
  openEditForm(data: Hypothesis): void {
    this.formData = data;
    this.hypothesisPopup = true;
  }

  getHypothesis(project_id: string, filters?: HypothesisFilter): void {
    this.overviewService.getHypothesis(project_id, filters).subscribe(
      (data: HypothesisList) => {
        this.hypothesis_data = data.data || [];
      },
      error => {
        console.error('Error fetching hypotheses:', error);
      },
    );
  }

  onSearchPerformed(searchOptions: SearchOptions): void {
    const searchQuery = searchOptions.title || '';
    const minDate = searchOptions.minDate
      ? new Date(searchOptions.minDate)
      : null;
    const maxDate = searchOptions.maxDate
      ? new Date(searchOptions.maxDate)
      : null;

    this.overviewService
      .getHypothesis(this.project_id as string)
      .subscribe((data: HypothesisList) => {
        this.hypothesis_data = data.data.filter((hypothesis: Hypothesis) => {
          const hypothesisDate = new Date(String(hypothesis.created_at));
          const matchesQuery =
            hypothesis.hypothesis_type
              .toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            hypothesis.hypothesis
              .toLowerCase()
              .includes(searchQuery.toLowerCase());

          const matchesDateRange =
            (!minDate || hypothesisDate >= minDate) &&
            (!maxDate || hypothesisDate <= maxDate);

          return matchesQuery && matchesDateRange;
        });
      });
  }

  fetchProjectData(project_id: string): void {
    this.overviewService
      .getProjectsById(project_id)
      .subscribe((res: BackendResponse<ProjectData>) => {
        this.projectData = res;
        if (res.data.hypotheses) {
          localStorage.setItem('folder_id', res.data.folder_id.toString());
          this.hypothesis_data = res.data.hypotheses;
        }
        this.loading = false;
      });
  }

  editProject(id: number): void {
    const newProject: HypothesisPayload = {
      hypothesis_type: this.formData?.hypothesis_type || '',
      hypothesis: this.formData?.hypothesis || '',
      project: Number(this.project_id) || '',
    };

    this.overviewService.editProject(newProject, id).subscribe({
      next: response => {
        this.toastrService.success(`${response.message}`);

        this.fetchProjectData(String(newProject.project));
      },
      error: error => {
        console.error('Error updating project:', error);
        this.toastrService.error(`${error.error.error}`);
      },
    });
  }

  addHypothesis(project_id: number): void {
    const newProject: HypothesisPayload = {
      project: Number(project_id),
      hypothesis_type: this.formData?.hypothesis_type || '',
      hypothesis: this.formData?.hypothesis || '',
    };
    this.overviewService
      .createHypothesis(newProject, Number(project_id))
      .subscribe({
        next: response => {
          this.toastrService.success(`${response.message}`);

          this.fetchProjectData(String(project_id));
        },
        error: error => {
          console.error('Error creating Hypothesis:', error);
          this.toastrService.error(`${error.error.error}`);
        },
      });
  }

  saveHypothesis(data: NewHypothesisFormValues): void {
    if (this.project_id) {
      this.formData = {
        id: data.id,
        hypothesis: data.hypothesis,
        hypothesis_type: data.hypothesis_type,
        project: this.project_id,
      };
      this.hypothesisPopup = false;

      if (this.formData?.id) {
        this.editProject(this.formData.id);
      } else {
        this.addHypothesis(Number(this.project_id));
      }
    } else {
      console.error('Project ID is not available');
    }
  }

  openAddForm(): void {
    this.formData = null;
    this.hypothesisPopup = true;
  }

  deleteProject(id: string): void {
    if (id != null) {
      const newProject: HypothesisPayload = {
        hypothesis_type: this.formData?.hypothesis_type || '',
        hypothesis: this.formData?.hypothesis || '',
        project: Number(this.project_id) || '',
      };
      this.overviewService.deleteHypothesis(Number(id), newProject).subscribe({
        next: response => {
          if (response.message) {
            this.toastrService.success(`${response.message}`);

            this.fetchProjectData(String(newProject.project));
          }
        },
        error: error => {
          this.toastrService.error(`${error.error.error}`);
        },
      });

      this.closePopup();
    }
  }

  confirmDelete(id: number | string): void {
    if (id !== undefined && id !== null) {
      this.id = id;
      this.hypothesisDeletePopup = true;
    } else {
      console.error('Invalid hypothesis ID');
    }
  }

  closePopup(): void {
    this.hypothesisPopup = false;
    this.formData = null;
    this.hypothesisDeletePopup = false;
  }
}
