<div class="flex flex-row justify-between w-full h-26 px-6 py-[28px]">
  <div class="flex flex-col">
    <span class="text-base">Projects Name</span>
    <span class="text-4xl">Training</span>
  </div>
  <div class="flex items-center space-x-4">
    <div>
      <!-- <app-search-header></app-search-header> -->
    </div>
    <div>
      <button
        mat-flat-button
        class="custom-Height buttonBg text-white hover:bg-[#1d4d7a]"
        (click)="openModal()">
        <mat-icon>add</mat-icon>
        New Training
      </button>
    </div>
  </div>
</div>

<!-- Modal -->

<div
  *ngIf="isModalOpen"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[860px] h-[580px]">
    <section class="h-[444px]">
      <div class="flex justify-between items-center object-center p-4">
        <h6 class="font-medium">New Training</h6>
        <button
          mat-icon-button
          (click)="closeModal()"
          class="text-gray-400 hover:text-gray-600">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="flex flex-col">
        <h6>Name</h6>
        <div
          class="w-full bg-white h-[48px] border-1 rounded-xl flex flex-row items-center pl-4">
          <p class="font-normal leading-6 m-0">cauliflower-spicy-cat</p>
        </div>
      </div>
      <div class="flex flex-col mt-6">
        <h6>Training Data</h6>
        <div
          class="w-full bg-white h-[48px] border-1 rounded-xl flex flex-row items-center pl-4">
          <p class="font-normal leading-6 m-0">3 files selected</p>
        </div>
      </div>
    </section>
    <footer class="flex flex-row justify-between">
      <div class="flex justify-start mt-4">
        <button mat-flat-button class="bg-white text-txt-color">Cancel</button>
      </div>

      <div class="flex justify-start mt-4">
        <button mat-flat-button class="!bg-[#296197]" (click)="trainingModal()">
          Start Training
        </button>
      </div>
    </footer>
  </div>
</div>

<main
  *ngIf="isTrainingModal"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="bg-[#F1F3F9] rounded-xl shadow-lg relative w-[860px] h-[580px] px-4 py-4">
    <div class="flex justify-between items-center object-center my-4">
      <h6 class="font-medium">New Training</h6>
      <!-- <p class="mt-auto">cauliflower-spicy-cat</p> -->
      <button
        mat-icon-button
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <section class="h-[412px]">
      <mat-tab-group
        animationDuration="0ms"
        (selectedTabChange)="onTabChange($event)"
        [selectedIndex]="activeTabIndex">
        <mat-tab label="Analysis Goal">
          <div class="mt-6">
            <h5 class="m-0">Analysis Goal</h5>
            <p class="m-0">Choose your main goals of the analysis</p>
            <div class="flex flex-row items-center space-x-2 mt-4">
              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-normal font-sans">Task Overview</p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
            <div class="flex flex-row items-center space-x-2">
              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-normal font-sans">Task Overview</p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
          </div>
        </mat-tab>
        <mat-tab label="Target Selection">
          <div class="mt-6">
            <h5 class="m-0">Target Outputs</h5>
            <p>
              Based on the chosen analysis goal Object Detection, the model will
              predict
            </p>
            <p>
              The target output includes class labels and bounding box
              coordinates.
            </p>
            <p>These are the detected classes</p>
            <div class="flex flex-row justify-between">
              <button
                class="py-[6px] px-[80px] border-2 rounded-xl border-gray-400">
                Nucleous
              </button>
              <button
                class="py-[6px] px-[64px] border-2 rounded-xl border-gray-400">
                Mitochondrium
              </button>
              <button
                class="py-[6px] px-[80px] border-2 rounded-xl border-gray-400">
                Mitochondrium
              </button>
            </div>
            <p class="my-5">
              This is the detected bounding box format with an extracted example
            </p>
            <div class="flex flex-row">
              <div class="flex flex-row justify-between w-[392px]">
                <button
                  class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                  18
                </button>
                <button
                  class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                  1000
                </button>
                <button
                  class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                  18
                </button>
                <button
                  class="border-1 border-gray-400 gap-3 px-4 rounded-lg bg-white">
                  1000
                </button>
              </div>

              <div
                class="w-[380px] px-3 border-1 bg-white rounded-xl ml-auto flex flex-row justify-between">
                <p class="my-[14px]">xmin, ymin, xmax, ymax</p>
                <button class="ml-auto">
                  <mat-icon>chevron_right</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </mat-tab>
        <mat-tab label="Processing & Augmentation">
          <h5 class="mt-6">Processing & Augmentation</h5>
          <main>
            <p>Preprocessing</p>
            <div class="flex flex-row justify-between">
              <button class="custom-button">Nucleous</button>
              <button class="custom-button">Custom Sauce</button>
              <button class="custom-button">Mitochondrium</button>
              <button class="custom-button">Custom D</button>
              <button class="custom-button">Custom E</button>
            </div>
          </main>
          <main class="mt-4">
            <p>Preprocessing</p>
            <div class="flex flex-row justify-between">
              <button class="custom-button">Nucleous</button>
              <button class="custom-button">Custom Sauce</button>
              <button class="custom-button">Mitochondrium</button>
              <button class="custom-button">Custom D</button>
              <button class="custom-button">Custom E</button>
            </div>
          </main>

          <main class="mt-4">
            <p>Dataset Split</p>
            <div class="flex flex-row items-center justify-between">
              <p class="m-0">Train</p>
              <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
                1000
              </button>
              <p class="m-0">Test</p>
              <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
                18
              </button>
              <p class="m-0">Validation</p>
              <button class="w-[178px] border-1 bg-white rounded-lg py-[14px]">
                1000
              </button>
            </div>
          </main>
        </mat-tab>
        <mat-tab label="Recommendations">
          <div class="mt-6">
            <h5 class="m-0 font-sans">Recommendations</h5>
            <p>Select your recommended Machine Learning Models for the task</p>
            <div class="flex flex-row items-center space-x-2 mt-4">
              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-sans">YOLO (You Only Look Once)</p>

              <mat-icon color="material-symbols-outlined">info</mat-icon>
            </div>
            <div class="flex flex-row items-center space-x-2">
              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-sans">Faster R-CNN</p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
            <div class="flex flex-row items-center space-x-2">
              <mat-checkbox></mat-checkbox>

              <p class="mt-3 font-normal font-sans">
                SSD (Single Shot Multibox Detector)
              </p>

              <mat-icon color="material-symbols-outlined"> info</mat-icon>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </section>

    <footer class="flex flex-row justify-between">
      <div class="flex justify-start mt-4">
        <button
          mat-flat-button
          class="bg-white text-txt-color"
          (click)="previousStep()">
          back
        </button>
      </div>
      <div class="mt-4 flex items-center">
        <p class="m-0 font-sans font-medium">
          Step {{ activeTabIndex + 1 }} of {{ totalTabs }}
        </p>
      </div>
      <div class="flex justify-start mt-4">
        <button
          mat-flat-button
          class="!bg-[#296197]"
          (click)="trainingModal()"
          (click)="nextStep()">
          Next
        </button>
      </div>
    </footer>
  </div>
</main>
