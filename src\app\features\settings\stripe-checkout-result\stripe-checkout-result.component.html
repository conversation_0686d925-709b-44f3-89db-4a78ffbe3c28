<div
  class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-lg w-full rounded-lg mb-16 mr-8 relative z-10">
    @if (sessionId) {
      <!-- Success Case -->
      <div class="text-center space-y-6">
        <div class="flex justify-center">
          <svg
            class="w-16 h-16 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h1 class="text-3xl sm:text-4xl font-extrabold text-green-600">
          Payment Successful!
        </h1>
        <p class="text-gray-600 text-lg">
          Thank you for subscribing to our service. Your payment has been
          successfully processed.
        </p>

        <div
          class="bg-gray-50 p-4 rounded-lg shadow-inner text-left space-y-3 relative z-10">
          <p class="text-xl font-semibold text-gray-700">
            Your Subscription Details:
          </p>

          <p class="text-gray-800 text-lg">
            <strong class="font-medium">Status:</strong>
            @if (subscription?.is_active) {
              <span class="text-green-500 font-semibold ml-5">Active</span>
            }
          </p>

          <p class="text-gray-800 text-lg">
            <strong class="font-medium mr-5">Plan:</strong>
            @switch (subscription?.plan) {
              @case ('basic_monthly') {
                <span>Basic Plan (Monthly)</span>
              }
              @case ('impact_monthly') {
                <span>Impact Plan (Monthly)</span>
              }
              @case ('basic_yearly') {
                <span>Basic Plan (Annually)</span>
              }
              @case ('impact_yearly') {
                <span>Impact Plan (Annually)</span>
              }
            }
          </p>

          <p class="text-gray-800 text-lg">
            <strong class="font-medium mr-5">Start Date:</strong>
            <span>{{
              subscription?.current_period_start_at | date: 'yyyy-MM-dd'
            }}</span>
          </p>

          <p class="text-gray-800 text-lg">
            <strong class="font-medium mr-5">End Date:</strong>
            <span>{{
              subscription?.current_period_end_at | date: 'yyyy-MM-dd'
            }}</span>
          </p>
        </div>

        <div class="pt-4">
          <a
            routerLink="/dashboard"
            class="inline-block px-6 py-2 text-white bg-[#296197] hover:bg-[#1d4d7a] rounded-lg shadow-md transition duration-150 ease-in-out">
            Start Innovating
          </a>
        </div>
      </div>
    } @else {
      <!-- Cancel Case -->
      <div class="text-center space-y-6">
        <div class="flex justify-center">
          <svg
            class="w-16 h-16 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01M12 2a10 10 0 100 20 10 10 0 000-20z" />
          </svg>
        </div>
        <h1 class="text-2xl sm:text-3xl font-bold text-red-600">
          Something went wrong
        </h1>
        <p class="text-gray-600 text-base sm:text-lg">
          We've notified
          <a
            href="mailto:<EMAIL>"
            class="text-blue-600 underline hover:text-blue-800">
            support&#64;aicuflow.com
          </a>
          about the issue.
        </p>
        <div class="pt-2">
          <a
            routerLink="/settings"
            class="inline-block px-5 py-2 text-white bg-[#296197] hover:bg-[#1d4d7a] rounded-md shadow transition">
            Go to Settings
          </a>
        </div>
      </div>
    }
  </div>
</div>
