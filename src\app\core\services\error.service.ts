import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ErrorService {
  getErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      401: 'Your session has expired. Please log in again.',
      403: 'Access denied. You do not have permission.',
      404: 'The requested resource was not found.',
      500: 'An internal server error occurred.',
    };

    return messages[status] || 'An unexpected error occurred.';
  }
}
