@if (this.isLoading) {
  <div class="flex justify-center items-center p-5">
    <mat-spinner></mat-spinner>
  </div>
} @else {
  <form [formGroup]="this.filterPlotDataForm" (ngSubmit)="onSubmit()">
    <!-- HEADER -->
    <div
      class="flex items-center justify-between p-2 border-gray-200 dark:border-gray-700">
      <h1 mat-dialog-title>Filter Data</h1>
      <button
        mat-icon-button
        type="button"
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div formArrayName="columnFilters" mat-dialog-content>
      @for (
        filter of this.filterPlotDataForm.controls.columnFilters.controls;
        track columnFilterIndex;
        let columnFilterIndex = $index
      ) {
        <div class="flex flex-row" [formGroupName]="columnFilterIndex">
          <!-- LOGICAL OPERATOR : DONT SHOW FOR FIRST FILTER-->
          @if (columnFilterIndex !== 0) {
            <mat-form-field>
              <mat-label>Logic</mat-label>
              <mat-select formControlName="logicOperator">
                <mat-option [value]="'AND'"> AND </mat-option>
                <mat-option [value]="'OR'"> OR </mat-option>
              </mat-select>
            </mat-form-field>
          }

          <!-- COLUMN HEADER SELECT -->
          <mat-form-field>
            <mat-label>Column</mat-label>
            <mat-select formControlName="columnHeader">
              @for (choice of this.columnChoicesTransformed; track $index) {
                <!-- HERE choice[0] == category name and choice[1] == choice based on catergory -->
                @if (choice[1].length > 0) {
                  <mat-optgroup label="{{ choice[0] }}">
                    @for (option of choice[1]; track $index) {
                      <mat-option [value]="option">
                        {{ option }}
                      </mat-option>
                    }
                  </mat-optgroup>
                }
              }
            </mat-select>
          </mat-form-field>

          <!-- OPERATOR SELECT -->
          <mat-form-field>
            <mat-label>Operator</mat-label>
            <mat-select formControlName="operator">
              @let columnHeader = filter.get('columnHeader')?.value;
              @if (columnHeader !== null && columnHeader !== undefined) {
                @for (
                  operator of this.getOperatorBasedSelectedColumnHeader(
                    columnHeader
                  );
                  track $index
                ) {
                  <mat-option [value]="operator">
                    {{ operator }}
                  </mat-option>
                }
              }
            </mat-select>
          </mat-form-field>

          <!-- INPUT  -->
          <span [formGroupName]="'value'">
            <!-- INPUT TYPE : BETWEEN -->
            @if (filter.get('operator')?.value === 'between') {
              <div class="flex flex-row w-[200px]">
                <mat-form-field appearance="fill">
                  <mat-label>Min</mat-label>
                  <input matInput type="number" formControlName="minValue" />
                </mat-form-field>

                <mat-form-field appearance="fill">
                  <mat-label>Max</mat-label>
                  <input matInput type="number" formControlName="maxValue" />
                </mat-form-field>
              </div>
            } @else {
              <!-- INPUT TYPE : SINGLE VALUE -->
              <mat-form-field appearance="fill">
                <input
                  type="text"
                  matInput
                  formControlName="singleValue"
                  [matAutocomplete]="auto" />
                <mat-autocomplete #auto="matAutocomplete">
                  @if (filter.get('columnHeader')?.value) {
                    @for (
                      option of this._getOptionsforCatergoricalColumn(
                        filter.get('columnHeader')?.value || ''
                      );
                      track $index
                    ) {
                      <mat-option [value]="option">
                        {{ option }}
                      </mat-option>
                    }
                  }
                </mat-autocomplete>
              </mat-form-field>
            }
          </span>

          <!-- REMOVE FILTER BUTTON -->
          <button
            mat-fab
            [disabled]="filter.get('isExistingFilter')?.value"
            (click)="removeColumnFilter(columnFilterIndex)"
            type="button"
            class="w-8 flex justify-center items-center">
            <mat-icon>delete_outline</mat-icon>
          </button>
        </div>
      }
    </div>
    <div mat-dialog-actions class="flex justify-end gap-2 mt-4">
      <button
        mat-flat-button
        (click)="removeAllFiltersFromPlot()"
        [disabled]="this.data.filterInstanceId === null"
        type="button">
        Remove Filters
      </button>

      <button
        mat-flat-button
        (click)="addColumnFilter()"
        type="button"
        class="flex justify-end align-middle">
        <mat-icon>add</mat-icon>
      </button>

      <button mat-flat-button type="submit">Submit</button>
    </div>
  </form>
}
