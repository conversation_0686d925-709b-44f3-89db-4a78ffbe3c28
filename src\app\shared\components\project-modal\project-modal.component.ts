import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  Hypothesis,
  NewHypothesisFormValues,
  NewProjectFormValues,
  Project,
} from '../../../features/dashborad/models/project.model';

@Component({
  selector: 'app-project-modal',
  templateUrl: './project-modal.component.html',
  styleUrls: ['./project-modal.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class ProjectModalComponent implements OnInit {
  @Input() formData: Project | Hypothesis | null = null;
  @Input() entityName = '';
  @Input() labelName = '';
  @Input() labelDescription = '';
  @Input() placeholderTitle = '';
  @Input() placeholderDescription = '';
  @Input() modelType: 'project' | 'hypothesis' | null = null;

  @Output() cancelEvent = new EventEmitter<void>();
  @Output() saveHypothesisEvent = new EventEmitter<NewHypothesisFormValues>();
  @Output() saveProjectEvent = new EventEmitter<NewProjectFormValues>();

  entityForm!: FormGroup;
  errorMessage = '';
  entityType: 'project' | 'hypothesis' | undefined;

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {
    this.entityType = this.determineEntityType();
    this.initializeForm();
  }

  private determineEntityType(): 'project' | 'hypothesis' {
    if (this.modelType) {
      return this.modelType;
    }

    if (this.formData) {
      if ('title' in this.formData) {
        return 'project';
      } else if ('hypothesis_type' in this.formData) {
        return 'hypothesis';
      }
    }
    return 'project';
  }

  private initializeForm(): void {
    if (this.entityType === 'project') {
      this.entityForm = this.formBuilder.group({
        title: [
          this.isProject(this.formData) ? this.formData?.title : '',
          [Validators.required],
        ],
        description: [
          this.isProject(this.formData) ? this.formData?.description : '',
          [Validators.required],
        ],
      });
    } else if (this.entityType === 'hypothesis') {
      this.entityForm = this.formBuilder.group({
        hypothesis_type: [
          this.isHypothesis(this.formData)
            ? this.formData?.hypothesis_type
            : '',
          [Validators.required, Validators.maxLength(100)],
        ],
        hypothesis: [
          this.isHypothesis(this.formData) ? this.formData?.hypothesis : '',
          [Validators.required],
        ],
      });
    }
  }

  private isProject(data: Project | Hypothesis | null): data is Project {
    return data !== null && 'title' in data;
  }

  private isHypothesis(data: Project | Hypothesis | null): data is Hypothesis {
    return data !== null && 'hypothesis_type' in data;
  }

  cancel(): void {
    this.cancelEvent.emit();
  }

  save() {
    const formData = this.formData;
    if (
      this.entityType === 'project' &&
      this.isProject(this.entityForm.value)
    ) {
      this.saveProject(formData as Project);
    } else if (
      this.entityType === 'hypothesis' &&
      this.isHypothesis(this.entityForm.value)
    ) {
      this.saveHypothesis(formData as Hypothesis);
    }
  }

  saveProject(formData: Project | null): void {
    this.errorMessage = '';

    if (this.entityForm.invalid) {
      this.errorMessage = 'All fields are required!';
      return;
    }

    const saveData: NewProjectFormValues = this.entityForm.value;

    if (
      saveData.title === formData?.title &&
      saveData.description === formData?.description
    ) {
      this.errorMessage = 'No values are changed!';
      return;
    }

    if (this.formData && 'id' in this.formData) {
      saveData.id = this.formData.id;
    }

    this.saveProjectEvent.emit(saveData); // Emitting the specific type
  }

  saveHypothesis(formData: Hypothesis | null): void {
    this.errorMessage = '';

    if (
      this.entityForm.invalid &&
      this.entityForm.controls['hypothesis_type']?.errors?.['maxlength']
    ) {
      this.errorMessage = 'Hypothesis Type must not exceed 100 characters.';
      return;
    }

    if (this.entityForm.invalid) {
      this.errorMessage = 'All fields are required!';
      return;
    }
    const saveData: NewHypothesisFormValues = this.entityForm.value;

    if (
      saveData.hypothesis_type === formData?.hypothesis_type &&
      saveData.hypothesis === formData?.hypothesis
    ) {
      this.errorMessage = 'No values are changed!';
      return;
    }

    if (this.formData && 'id' in this.formData) {
      saveData.id = this.formData.id;
    }
    this.saveHypothesisEvent.emit(saveData); // Emitting the specific type
  }
}
