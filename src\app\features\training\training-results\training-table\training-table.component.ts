import { LiveAnnouncer } from '@angular/cdk/a11y';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnChanges,
} from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { TrainingTableData } from '../../models/trainings.model';

export interface PeriodicElement {
  name: string;
  position: number;
  weight: number;
  symbol: string;
}

@Component({
  selector: 'app-training-table',
  imports: [MatTableModule, TitleCasePipe, CommonModule],
  templateUrl: './training-table.component.html',
  styleUrl: './training-table.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingTableComponent implements OnChanges {
  private _liveAnnouncer = inject(LiveAnnouncer);
  @Input() trainingId = '';
  @Input() currentTab = '';
  @Input() tableData!: TrainingTableData;

  displayedColumns: string[] = [];
  dataSource!: {
    metric?: string;
    value: string | number;
    name?: string;
  }[];

  constructor(private cd: ChangeDetectorRef) {}

  ngOnChanges(): void {
    this.dataSource = this.tableData.displayData;
    this.displayedColumns = this.tableData.displayColumns;
    this.cd.detectChanges();
  }
}
