import { Injectable } from '@angular/core';
import {
  <PERSON>ttp<PERSON>vent,
  <PERSON>ttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Observable, tap } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements HttpInterceptor {
  intercept(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    req: HttpRequest<any>,
    next: HttpHandler,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Observable<HttpEvent<any>> {
    // console.log(`HTTP Request: ${req.method} ${req.url}`);
    // console.log('Request Headers:', req.headers);

    return next.handle(req).pipe(
      tap({
        error: error => {
          console.error('HTTP Error:', error);
        },
      }),
    );
  }
}
