/**
 * Recursively find a subfolder by its ID in a folder hierarchy.
 * @param folders The array of folders to search.
 * @param subfolderId The ID of the subfolder to find.
 * @returns The subfolder data or null if not found.
 */

export function findSubfolderById(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  folders: any[],
  subfolderId: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any | null {
  for (const folder of folders) {
    if (folder.folder_id === subfolderId) {
      return folder;
    }
    if (folder.subfolders && folder.subfolders.length > 0) {
      const foundSubfolder = findSubfolderById(folder.subfolders, subfolderId);
      if (foundSubfolder) {
        return foundSubfolder;
      }
    }
  }
  return null;
}
