import { createReducer, on } from '@ngrx/store';
import { changeTab } from '../actions/tab.action';
import { TabState } from '../states/tab.state';

const initialState: TabState = {
  tab: {
    selectedTab: '',
    showSidebar: false,
  },
};

export const tabReducer = createReducer(
  initialState,
  on(changeTab, (state, { selectedTab }) => {
    const hideSidebarTabs = ['projects', 'settings'];
    const shouldShowSidebar = !hideSidebarTabs.includes(selectedTab);

    return {
      ...state,
      tab: {
        selectedTab: selectedTab,
        showSidebar: shouldShowSidebar,
      },
    };
  }),
);
