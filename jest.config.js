module.exports = {
  preset: 'jest-preset-angular', // Ensures Angular-specific presets are used
  testEnvironment: 'jsdom', // Simulates a browser environment for tests
  setupFilesAfterEnv: ['<rootDir>/src/jest.setup.ts'], // Ensure proper Angular test environment
  testMatch: ['**/+(*.)+(spec).+(ts)'], // Match test files with `.spec.ts`,
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/'],
  transform: {
    '^.+\\.(ts|mjs|js|html)$': [
      'jest-preset-angular',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
        stringifyContentPathRegex: '\\.(html|svg)$',
      },
    ],
  },
  transformIgnorePatterns: ['node_modules/(?!.*\\.mjs$)'], // Support ES module dependencies in `node_modules`
  moduleNameMapper: {
    '^@app/(.*)$': '<rootDir>/src/app/$1', // Alias for app paths
    '^@env/(.*)$': '<rootDir>/src/environments/$1', // Alias for environment paths
    '\\.(css|scss|sass|less)$': 'identity-obj-proxy', // Mock CSS and preprocessor imports
  },
  moduleFileExtensions: ['ts', 'html', 'js', 'json'], // Extensions Jest should process
  coverageDirectory: '<rootDir>/coverage', // Directory for coverage reports
  collectCoverageFrom: [
    'src/**/*.ts', // Collect coverage for all TypeScript files
    '!src/main.ts', // Exclude the main entry file
    '!src/polyfills.ts', // Exclude polyfills
    '!src/**/*.module.ts', // Exclude Angular module definitions
    '!src/**/*.array.ts', // Exclude specific array files
  ],
};
