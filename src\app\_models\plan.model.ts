export type PlanType = 'basic' | 'impact'; // Can be either 'basic' or 'impact'

export type SubscriptionType = 'monthly' | 'yearly'; // monthly or yearly

export interface PlanSubscription {
  is_active: boolean;
  plan: string;
  status: string;
  current_period_end_at: string;
  current_period_start_at: string;
  next_page: string;
}
export interface StandardPlan {
  status?: string;
  message?: string;
  data: Plan[];
}
export interface Plan {
  id: number;
  name: PlanType;
  category: string;
  stripe_price_id: string;
  monthly_discount: string;
  monthly_price: string;
  original_monthly_price: string;
  original_yearly_price: string;
  yearly_discount: string | number;
  yearly_price: string;
  features: string[];
  selectedBillingCycle?: SubscriptionType;
}
