import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared/shared.module';
import { TrainingRoutingModule } from './training-routing.module';
import { TrainingComponent } from './training/training.component';
import { TrainingHeaderComponent } from './training-header/training-header.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule, MatNavList } from '@angular/material/list';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatRadioButton } from '@angular/material/radio';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { SelectFileFolderComponent } from './training-dialog/select-file-folder/select-file-folder.component';

@NgModule({
  declarations: [TrainingComponent],
  imports: [
    CommonModule,
    TrainingRoutingModule,
    SharedModule,
    TrainingHeaderComponent,
    MatButtonModule,
    MatIconModule,
    MatNavList,
    MatListModule,
    MatTabsModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatRadioButton,
    MatProgressBarModule,
    SelectFileFolderComponent,
  ],
  exports: [TrainingComponent, TrainingHeaderComponent],
})
export class TrainingModule {}
