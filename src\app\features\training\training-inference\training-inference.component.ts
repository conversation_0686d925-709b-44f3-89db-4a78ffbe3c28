import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { StartInferenceDialogComponent } from './start-inference-dialog/start-inference-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { TrainingService } from '../service/training-service.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-training-inference',
  imports: [MatButtonModule, CommonModule],
  templateUrl: './training-inference.component.html',
  styleUrl: './training-inference.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingInferenceComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  showStartButton = true;
  running_inference_id = 0;
  @Input() trainingId = '';
  inferenceType = '';
  constructor(
    private cdr: ChangeDetectorRef,
    private trainingService: TrainingService,
    private toasterService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.trainingService
      .checkRunningInference(Number(this.trainingId))
      .subscribe({
        next: response => {
          this.checkRunningInferenceStatus(response);
          this.running_inference_id = response.data.id;
          this.toasterService.success(response.message);
        },
        error: error => {
          console.log('error', error);
        },
      });
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  checkRunningInferenceStatus(response: any) {
    if (Object.keys(response.data).length !== 0) {
      this.showStartButton = false;
      this.inferenceType = 'update';
    } else {
      this.showStartButton = true;
      this.inferenceType = 'new';
    }
    this.cdr.detectChanges();
  }

  openInferenceModal() {
    const dialogRef = this.dialog.open(StartInferenceDialogComponent, {
      ...StartInferenceDialogComponent.viewConfig,
      data: { trainingId: this.trainingId, type: this.inferenceType },
    });
    dialogRef.afterClosed().subscribe((inferenceId: number) => {
      this.running_inference_id = inferenceId;
      this.trainingService
        .checkRunningInference(Number(this.trainingId))
        .subscribe({
          next: response => {
            this.checkRunningInferenceStatus(response);
          },
        });
      this.cdr.markForCheck();
    });
  }

  stopInference() {
    this.trainingService.stopMlInference(this.running_inference_id).subscribe({
      next: response => {
        this.toasterService.success(response.message);
        this.showStartButton = true;
        this.inferenceType = 'new';
        this.cdr.detectChanges();
      },
      error: error => {
        this.toasterService.error(error.message);
      },
    });
  }
}
