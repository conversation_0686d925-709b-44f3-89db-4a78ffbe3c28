import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DataviewService } from '../../data-views/services/data-view.service';
import { PlotService } from '../../../services/plot.services';
import {
  ColumnValues,
  FilterDataRow,
  FilterOperators,
} from '../../data-views/models/data-view.model';
import { MatSelectChange } from '@angular/material/select';

@Component({
  selector: 'app-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class FilterModalComponent implements OnInit {
  @Output() closeModal = new EventEmitter<boolean>();
  @Input() plotId = 0;
  @Input() fileID = 0;
  @Input({ required: true }) filter_active!: boolean;
  @Input({ required: true }) filter_instance_id!: number | null;

  showDropdown = false;

  isFilterDataModalOpen = false;
  filterDataRows: FilterDataRow[] = [
    {
      logicOperator: '',
      selectedColumn: '',
      selectedOperator: '',
      isBetweenOperator: false,
      columnType: '',
      availableOperators: [],
      availableValues: [],
      selectedValue: '',
      minValue: null,
      maxValue: null,
      singleValue: null,
      minPlaceholder: '',
      maxPlaceholder: '',
    },
  ];
  categories: string[] = [];
  numerical: string[] = [];
  availableOperators: FilterOperators | null = null;
  availableValues: ColumnValues | null = null;

  constructor(
    private dataViewService: DataviewService,
    private plotService: PlotService,
  ) {}

  ngOnInit(): void {
    console.log(this.filter_active, this.filter_instance_id);
    if ((this.filter_active, this.filter_instance_id)) {
      this.loadExistingFilter();
    }
    this.dataViewService.getColumnChoice(this.fileID).subscribe(
      response => {
        this.categories = response.data.data.Categories;
        this.numerical = response.data.data.Numerical;

        this.getColumnValues();
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }
  hideDropdownWithDelay() {
    setTimeout(() => {
      this.showDropdown = false;
    }, 200); // Delay to allow clicking on options
  }

  selectOption(option: string) {
    this.filterDataRows[0].selectedValue = option;
    this.showDropdown = false;
  }
  loadExistingFilter() {
    if (this.filter_instance_id) {
      this.dataViewService.GetFilter(this.filter_instance_id).subscribe(
        response => {
          if (response.status === 'success') {
            const filterOperations = response.data.filter_operations;

            this.filterDataRows = filterOperations.map(
              (operation: {
                filter_operator: string;
                filter_value: string;
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                logic: any;
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                filter_column: any;
              }) => {
                const isBetweenOperator =
                  operation.filter_operator === 'between';
                let minValue = null;
                let maxValue = null;
                let selectedValue = null;

                // Handle 'between' operator case
                if (isBetweenOperator) {
                  const values = operation.filter_value
                    .split(',')
                    .map(val => val.trim());
                  minValue = values[0] || null;
                  maxValue = values[1] || null;
                } else {
                  selectedValue = operation.filter_value || null;
                }
                console.log(operation.filter_value);
                return {
                  logicOperator: operation.logic || '',
                  selectedColumn: operation.filter_column || '',
                  selectedOperator: operation.filter_operator || '',
                  isBetweenOperator: isBetweenOperator,
                  columnType: 'Categories',
                  availableOperators: [operation.filter_operator],
                  selectedValue: operation.filter_value,
                  minValue: minValue,
                  maxValue: maxValue,
                  singleValue: !isBetweenOperator ? selectedValue : null,
                  minPlaceholder: 'Min',
                  maxPlaceholder: 'Max',
                };
              },
            );

            console.log('filter rows', this.filterDataRows);
          }
        },
        error => {
          console.error('Error fetching column values:', error);
        },
      );
    }
  }

  getColumnValues(): void {
    this.dataViewService.getColumnValues(this.fileID).subscribe(
      response => {
        const { values, operators } = response.data;
        this.availableValues = values;
        this.availableOperators = operators;
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }

  addRow() {
    this.filterDataRows.push({
      logicOperator: '',
      selectedColumn: '',
      selectedOperator: '',
      isBetweenOperator: false,
      selectedValue: '',
    });
  }

  removeRow(index: number) {
    this.filterDataRows.splice(index, 1);
  }

  onColumnChange(event: MatSelectChange, row: FilterDataRow) {
    const selectedColumn = event.value;
    row.selectedColumn = selectedColumn;

    if (this.numerical.includes(selectedColumn)) {
      row.columnType = 'Numerical';
      const columnData = this.availableValues?.Numerical[selectedColumn];

      row.minPlaceholder = columnData?.min.toString() || '';
      row.maxPlaceholder = columnData?.max.toString() || '';
    } else if (this.categories.includes(selectedColumn)) {
      row.columnType = 'Categories';
      const matchingCategory = this.availableValues?.Categories.find(
        category => category.name === selectedColumn,
      );

      row.availableValues = matchingCategory ? matchingCategory.options : [];
    }

    row.availableOperators =
      row.columnType &&
      this.availableOperators &&
      this.availableOperators[row.columnType]
        ? Object.keys(this.availableOperators[row.columnType])
        : [];
  }

  onOperatorChange(event: MatSelectChange, row: FilterDataRow) {
    row.selectedOperator = event.value;
    row.isBetweenOperator = row.selectedOperator === 'between';
  }

  isFormValid(): boolean {
    return this.filterDataRows.every(
      row => row.selectedColumn && row.selectedOperator,
    );
  }

  filterDataSave() {
    const filters = this.filterDataRows.map((row, index) => {
      const logicOperator = index === 0 ? 'AND' : row.logicOperator;
      const selectedColumn = row.selectedColumn;
      const operator = row.selectedOperator;
      const value =
        row.columnType === 'Categories'
          ? row.selectedValue
          : row.isBetweenOperator
            ? `${row.minValue}, ${row.maxValue}`
            : row.singleValue;

      return {
        filter_column: selectedColumn,
        filter_operator: operator,
        filter_value: value,
        logic: logicOperator,
      };
    });
    const filterData = JSON.stringify({ filters }, null, 2);

    this.plotService
      .UserPlotAssociateFilter(this.plotId, filterData)
      .subscribe({
        next: response => {
          if (response.status === 'success') {
            location.reload();
          }
        },
        error: error => {
          console.error('Error sending filters:', error);
        },
      });

    console.log('Selected Filters:', filters);
    // console.log('Saving filter data:', this.filterDataRows);
    this.closeFilterDataModal();
  }

  closeFilterDataModal() {
    this.isFilterDataModalOpen = false;
    this.closeModal.emit(false);
  }
  closeFilterModal() {
    this.isFilterDataModalOpen = false;
  }
  removeFilter() {
    this.filterDataRows = [];
    this.dataViewService.RemoveFilter(this.filter_instance_id).subscribe({
      next: response => {
        if (response.status === 'success') {
          console.log(response);
          this.closeModal.emit(true);
          location.reload();
        }
      },
      error: error => {
        console.error('Error sending filters:', error);
      },
    });

    this.closeFilterDataModal();
  }
}
