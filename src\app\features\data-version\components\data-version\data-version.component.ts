import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import { g_const } from '../../../../_utility/global_const';
import { ToastrService } from 'ngx-toastr';
import { DataversionService } from '../../../../services/dataversion.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ResponseData, SearchOptions } from '../../../../_models/common.model';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { catchError, concatMap, forkJoin, Observable, of, tap } from 'rxjs';
import {
  createUpdatePipelineStepPayload,
  DatasetVersion,
  DatasetVersionResponse,
  ParamOption,
  PipelineByIdResponse,
  PipelineStep,
} from '../../models/data-version.model';
import { isAnArray, safeJoin } from '../../../../_utility/array-utils';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

// Interface to track the selected step
interface SelectedStep {
  selectedStep: PipelineStep | null;
  selectedStepId: number | null;
  selectedParams: Record<number, string | number | [number, number] | null>;
}

// Interface for pipeline chip
interface PipelineChip {
  name: string;
  id: number;
  is_active: boolean;
}

@Component({
  selector: 'app-data-version',
  templateUrl: './data-version.component.html',
  styleUrls: ['./data-version.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class DataVersionComponent implements OnInit {
  // Assign the utility function to use in the template
  isAnArray = isAnArray;
  safeJoin = safeJoin;

  // refer the const variables, used to show the tabs name (Tables, Images)
  g_const = g_const;

  // to show the version name selected
  versionName = '';

  // hold the name of the new/exisiting pipeline
  pipelineName = '';

  // to set the active version item id
  activeItem: number | null = null;

  // hold the selected project id
  projectId: string | null = localStorage.getItem('project_id');

  // flag to open/close modal to add/edit data version
  isdataVersionModal = false;

  searchTerm = '';

  // to show/hide screen for the dataset table
  showSideModel = false;

  // flag to show/hide the add/edit pipeline modal
  isNewDataPipelineModal = false;

  // to hold the data versions
  dataVersions: DatasetVersion[] = [];

  searchOptions: SearchOptions = {};

  // data version form group
  dataVersionForm!: FormGroup;

  // flag to open/close the delete dialog
  isfileDeletePopup = false;

  // dataset version id
  datasetVersionId: number | string | '' = '';

  // to hold dataversion id to check whether the modal open in edit mode or not
  EditfileID: number | string | '' = '';

  // to set the selected tab (tables or images)
  currentTab = 'table';

  // hold the step type mode ('Pre-Processing' or 'Augmentation')
  currentMode = '';

  // hold the step type mode description
  currentModeDescription = '';

  // hold the pipeline id
  pipelineId = 0;

  // pipeline steps data
  stepsData: PipelineStep[] = [];

  // to track the selected steps
  selectedSteps: SelectedStep[] = [];

  //dataset size options, showing only for the augmentation
  datasetSize: SelectedStep = {
    selectedStep: null,
    selectedStepId: null,
    selectedParams: {},
  };

  // to maintain the dataset size values
  datasetSizeMapping: Record<number, string> = {
    500: '1x',
    1000: '2x',
    1500: '3x',
    2000: '4x',
    2500: '5x',
  };
  // hold augmentation image data
  augmentationImageData: PipelineChip[] = [];
  // hold Pre-Processing image data
  preprocessingImageChips: PipelineChip[] = [];
  // hold Pre-Processing table data
  preprocessingSteps: PipelineChip[] = [];
  // hold augmentation table data
  augmentationTableData: PipelineChip[] = [];

  // flag to check pipeline is mark to be set as active.
  isPipelineMarkAsActive = false;

  // flag to show/hide loader
  loading = false;

  // to maintain the display of the tables and images tab with the pipeline chips
  dataVersionTabs: Record<
    string,
    {
      title: string;
      description: string;
      steps: PipelineChip[];
    }[]
  > = {
    [g_const.tables]: [
      {
        title: 'Pre-Processing',
        description:
          'Increase performance and decrease training time by applying transformations to all rows in this dataset.',
        steps: [],
      },
      {
        title: 'Augmentation',
        description: `Increase performance and robustness by creating new training examples
          for your model to learn from by generating augmented versions of each row in your training set.`,
        steps: [],
      },
    ],
    [g_const.images]: [
      {
        title: 'Pre-Processing',
        description:
          'Increase performance and decrease training time by applying transformations to all images in this dataset.',
        steps: [],
      },
      {
        title: 'Augmentation',
        description: `Increase performance and robustness by creating new training examples
         for your model to learn from by generating augmented versions of each image in your training set.`,
        steps: [],
      },
    ],
  };

  // reference to cancel the subscriptions
  private destroyRef = inject(DestroyRef);

  constructor(
    private fb: FormBuilder,
    private DataversionService: DataversionService,
    private toastrService: ToastrService,
    private cdr: ChangeDetectorRef,
  ) {
    this.dataVersionForm = this.fb.group({
      name: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.getDataVersionData();
  }

  /**
   * Open modal to add/edit the data version.
   * @returns
   */
  openDataversionModal(): void {
    if (this.dataVersions.length >= 5) {
      this.toastrService.error(
        'Limit exceeded: You can only add up to 5 data Versions.',
      );
      return;
    }
    this.isdataVersionModal = true;
  }

  /**
   * Set the current Tab.
   * @param event - tab change event
   */
  onTabChange(event: MatTabChangeEvent): void {
    this.currentTab = event.index === 0 ? 'table' : 'image';
    this.resetFilteredData(this.currentTab);
  }

  /**
   * Remove particular step in the pipeline
   * @param index - index of the step to be removed
   */
  removeStep(index: number): void {
    this.selectedSteps.splice(index, 1);
  }

  /**
   * Open modal to add pipeline.
   * @param tab - the dataset type (tables or images)
   * @param mode - the step type Pre-Processing or Augmentation
   * @param description - the step type description
   */
  openAddPipelineModal(tab: string, mode: string, description: string): void {
    this.currentTab = tab;
    this.currentMode = mode;
    this.currentModeDescription = description;
    this.isNewDataPipelineModal = true;
    this.getPipelineSteps()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  /**
   * Update the selectedSteps object with the selected step option.
   * @param stepId - the selected step id
   * @param index - index of the selected step
   */
  onStepChange(stepId: number | null, index: number): void {
    const selectedStep = this.stepsData.find(step => step.id === stepId);

    if (selectedStep) {
      this.selectedSteps[index].selectedStep = selectedStep;
      this.selectedSteps[index].selectedParams =
        this.initializeSelectedParams(selectedStep);
    }
  }

  /**
   * Fetching the pipeline steps option to add new step.
   */
  addNewStep(): void {
    const previousSelectedSteps = [...this.selectedSteps];

    const newStep: {
      selectedStep: PipelineStep | null;
      selectedStepId: number | null;
      selectedParams: Record<number, string | number | [number, number]>;
    } = {
      selectedStep: null,
      selectedStepId: null,
      selectedParams: {},
    };

    this.selectedSteps = [...previousSelectedSteps, newStep];
  }

  /**
   * To fetch the pipeline steps data.
   */
  getPipelineSteps(): Observable<ResponseData<PipelineStep[]>> {
    const modeKey =
      this.currentMode === 'Pre-Processing' ? 'preprocessing' : 'augmentation';
    return this.DataversionService.getPipeLineSteps(
      modeKey,
      this.currentTab,
      4,
    ).pipe(
      tap((data: ResponseData<PipelineStep[]>) => {
        if (modeKey === 'augmentation') {
          const excludeFields = [
            'Dataset Image Multiplier',
            'Dataset Multiplier',
          ];
          // Extract stepsData excluding specific names
          this.stepsData = data.data.filter(
            (step: PipelineStep) => !excludeFields.includes(step.name),
          );

          // Extract the step with specific names
          const datasetStep =
            data.data.find((step: PipelineStep) =>
              excludeFields.includes(step.name),
            ) || null;
          if (datasetStep) {
            this.datasetSize = {
              selectedStep: datasetStep,
              selectedStepId: datasetStep.id,
              selectedParams: this.initializeSelectedParams(datasetStep),
            };
          }
        } else {
          this.stepsData = data.data;
          this.datasetSize = {
            selectedStep: null,
            selectedStepId: null,
            selectedParams: {},
          };
        }
        this.cdr.detectChanges();
      }),
      catchError(error => {
        this.toastrService.error(
          'An error occured while fetching pipeline steps data. Please try again later.',
        );
        console.error('HTTP error fetching pipeline steps data:', error);
        return of();
      }),
    );
  }

  /**
   * Close the pipeline modal and reset the values.
   */
  closePipelineModal(): void {
    this.isNewDataPipelineModal = false;
    this.clearSelections();
    this.pipelineName = '';
    this.pipelineId = 0;
    this.isPipelineMarkAsActive = false;
  }

  /**
   * Close data version modal.
   */
  closeDataVersionModal(): void {
    this.dataVersionForm.reset();
    this.isdataVersionModal = false;
    this.EditfileID = '';
  }

  /**
   * To fetch the pipeline data for the specific dataset version
   * @param versionId - version id
   * @param versionName - version name
   */
  onVersionClick(versionId: number, versionName: string): void {
    this.loading = true;
    this.versionName = versionName;
    this.activeItem = versionId;
    this.showSideModel = true;
    this.resetData();
    const currentTabData$ = this.DataversionService.getDataVersionById(
      versionId,
      this.currentTab,
      'preprocessing',
    );
    const otherTabData$ = this.DataversionService.getDataVersionById(
      versionId,
      this.currentTab === 'table' ? 'image' : 'table',
      'preprocessing',
    );
    const currentTabArguments$ = this.DataversionService.getDataVersionById(
      versionId,
      this.currentTab,
      'augmentation',
    );
    const otherTabArguments$ = this.DataversionService.getDataVersionById(
      versionId,
      this.currentTab === 'table' ? 'image' : 'table',
      'augmentation',
    );

    forkJoin([
      currentTabData$,
      otherTabData$,
      currentTabArguments$,
      otherTabArguments$,
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: ([
          currentTabResponse,
          otherTabResponse,
          currentTabArgs,
          otherTabArgs,
        ]) => {
          this.loading = false;
          if (this.currentTab === 'table') {
            this.preprocessingSteps = this.mapUserPipelineData(
              currentTabResponse.data?.user_pipeline,
            );
            this.augmentationTableData = this.mapUserPipelineData(
              currentTabArgs.data?.user_pipeline,
            );
            this.preprocessingImageChips = this.mapUserPipelineData(
              otherTabResponse.data?.user_pipeline,
            );
            this.augmentationImageData = this.mapUserPipelineData(
              otherTabArgs.data?.user_pipeline,
            );

            this.dataVersionTabs[g_const.tables][0].steps = [
              ...this.preprocessingSteps,
            ];
            this.dataVersionTabs[g_const.tables][1].steps = [
              ...this.augmentationTableData,
            ];
            this.dataVersionTabs[g_const.images][0].steps = [];
            this.dataVersionTabs[g_const.images][1].steps = [];
          }
          if (this.currentTab === 'image') {
            this.preprocessingImageChips = this.mapUserPipelineData(
              currentTabResponse.data?.user_pipeline,
            );
            this.augmentationImageData = this.mapUserPipelineData(
              currentTabArgs.data?.user_pipeline,
            );
            this.preprocessingSteps = this.mapUserPipelineData(
              otherTabResponse.data?.user_pipeline,
            );
            this.augmentationTableData = this.mapUserPipelineData(
              otherTabArgs.data?.user_pipeline,
            );

            this.dataVersionTabs[g_const.images][0].steps = [
              ...this.preprocessingImageChips,
            ];
            this.dataVersionTabs[g_const.images][1].steps = [
              ...this.augmentationImageData,
            ];

            this.dataVersionTabs[g_const.tables][0].steps = [];
            this.dataVersionTabs[g_const.tables][1].steps = [];
          }

          this.cdr.markForCheck();
        },
        error: error => {
          this.loading = false;
          this.toastrService.error(
            'An error occured while fetching data version data. Please try again later.',
          );
          console.error('HTTP error fetching data version data:', error);
        },
      });

    this.cdr.detectChanges();
  }

  /**
   * On search for the search term.
   * @param searchOptions - options to be filtered.
   */
  onSearchPerformed(searchOptions: SearchOptions): void {
    this.searchTerm = searchOptions.title?.trim() || '';
    this.filterData();
  }

  /**
   * Filter the pipeline names.
   * @returns
   */
  filterData(): void {
    if (!this.searchTerm) {
      this.resetFilteredData(this.currentTab);
      return;
    }

    if (this.currentTab === 'table') {
      this.dataVersionTabs[g_const.tables][0].steps =
        this.preprocessingSteps.filter((step: PipelineChip) =>
          step.name.toLowerCase().includes(this.searchTerm.toLowerCase()),
        );
      this.dataVersionTabs[g_const.tables][1].steps =
        this.augmentationTableData.filter((step: PipelineChip) =>
          step.name.toLowerCase().includes(this.searchTerm.toLowerCase()),
        );
    } else if (this.currentTab === 'image') {
      this.dataVersionTabs[g_const.images][0].steps =
        this.preprocessingImageChips.filter((step: PipelineChip) =>
          step.name.toLowerCase().includes(this.searchTerm.toLowerCase()),
        );
      this.dataVersionTabs[g_const.images][1].steps =
        this.augmentationImageData.filter((step: PipelineChip) =>
          step.name.toLowerCase().includes(this.searchTerm.toLowerCase()),
        );
    }
    this.cdr.markForCheck();
  }

  /**
   * Reset pipeline names data.
   */
  resetData(): void {
    this.preprocessingSteps = [];
    this.augmentationTableData = [];
    this.preprocessingImageChips = [];
    this.augmentationImageData = [];
    this.dataVersionTabs[g_const.tables][0].steps = [];
    this.dataVersionTabs[g_const.tables][1].steps = [];
    this.dataVersionTabs[g_const.images][0].steps = [];
    this.dataVersionTabs[g_const.images][1].steps = [];
  }

  /**
   * Reset the filtered data.
   * @param currentTab - the current tab
   */
  resetFilteredData(currentTab?: string): void {
    if (currentTab === 'table') {
      this.dataVersionTabs[g_const.tables][0].steps = [
        ...this.preprocessingSteps,
      ];
      this.dataVersionTabs[g_const.tables][1].steps = [
        ...this.augmentationTableData,
      ];
    } else if (currentTab === 'image') {
      this.dataVersionTabs[g_const.images][0].steps = [
        ...this.preprocessingImageChips,
      ];
      this.dataVersionTabs[g_const.images][1].steps = [
        ...this.augmentationImageData,
      ];
    }
  }

  /**
   * Open user pipeline modal in edit mode.
   * @param pipelineId - pipeline id
   * @param mode - step type
   * @param description - the step type description
   */
  openEditPipelineModal(pipelineId: number, mode: string, description: string) {
    this.pipelineId = pipelineId;
    this.isNewDataPipelineModal = true;
    this.currentMode = mode;
    // eslint-disable-next-line no-self-assign
    this.currentTab = this.currentTab;
    this.currentModeDescription = description;
    this.isPipelineMarkAsActive = false;
    this.fetchUserPipelineById(pipelineId);
  }

  /**
   * Fetch user pipeline by id.
   * @param pipelineId - pipeline id
   */
  fetchUserPipelineById(pipelineId: number): void {
    this.loading = true;
    this.getPipelineSteps()
      .pipe(
        concatMap(() => {
          // Once getPipelineSteps is complete, call fetchUserPipelineById
          return this.DataversionService.getUserPipeLineDataById(pipelineId);
        }),
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: ResponseData<PipelineByIdResponse>) => {
          if (response.status === 'success') {
            this.loading = false;
            const stepsKey =
              this.currentMode === 'Pre-Processing'
                ? 'preprocessing_steps'
                : 'augmentation_steps';
            const stepsData = response.data[stepsKey];
            this.selectedSteps = stepsData.map((step: PipelineStep) => {
              return {
                selectedStepId: step.id,
                selectedStep: step,
                selectedParams: this.initializeSelectedParams(step),
              };
            });
            // to set the data size values
            const datasetParams: SelectedStep['selectedParams'] = {};
            this.datasetSize.selectedStep?.params.forEach(
              (param: ParamOption) => {
                datasetParams[param.id] =
                  this.datasetSizeMapping[response.data.dataset_size];
              },
            );
            this.datasetSize.selectedParams = datasetParams;
            this.pipelineName = response.data.name || '';
            this.isPipelineMarkAsActive = response.data.is_active;
            this.cdr.detectChanges();
          } else {
            this.loading = false;
            this.toastrService.error(
              'Failed to fetch user pipeline. Please try again later.',
            );
          }
        },
        error: error => {
          this.loading = false;
          this.toastrService.error(
            'An error occured while fetching user pipeline. Please try again later.',
          );
          console.error('HTTP error fetching user pipeline:', error);
        },
      });
  }

  /**
   * Initialize the selected step params
   * @param step - selected step
   * @returns
   */
  private initializeSelectedParams(
    step: PipelineStep,
  ): SelectedStep['selectedParams'] {
    const params: SelectedStep['selectedParams'] = {};
    if (step.params) {
      step.params.forEach((param: ParamOption) => {
        if (!(param.options.length === 0 && !param.value)) {
          params[param.id] = this.pipelineId ? param.value : null;
        }
      });
    }
    return params;
  }

  /**
   * Save the pipeline with the selected steps.
   * @returns
   */
  saveSelections(): void {
    if (!this.pipelineName.trim()) {
      this.toastrService.error(
        'Please enter a valid pipeline name and complete all fields before saving.',
      );
      return;
    }
    if (this.currentMode === 'Augmentation') {
      const datasetSizeValid = Object.values(
        this.datasetSize.selectedParams,
      ).every(paramValue => {
        return (
          paramValue !== null && paramValue !== undefined && paramValue !== ''
        );
      });
      if (!datasetSizeValid) {
        this.toastrService.error('Please select the data size.');
        return;
      }
    }

    if (this.selectedSteps.length === 0) {
      this.toastrService.error('Please add atleast one step.');
      return;
    }

    if (!this.isSelectionsValid()) {
      this.toastrService.error('Please complete all the fields before saving.');
      return;
    }

    this.saveDataVersionPipeLines();
    this.closePipelineModal();
  }

  /**
   * Add data version pipeline.
   */
  saveDataVersionPipeLines(): void {
    const selections = this.selectedSteps.map(stepData => {
      return {
        stepId: stepData.selectedStepId,
        params: stepData.selectedParams,
      };
    });

    const formattedSelections: createUpdatePipelineStepPayload['steps'] =
      selections
        .map((selection, index) => {
          const stepData = this.selectedSteps[index].selectedStep;

          if (!stepData) return null;

          const params = Object.entries(selection.params).map(
            ([paramId, value]) => {
              return {
                id: Number(paramId),
                value: value as string | number | [number, number],
              };
            },
          );

          return {
            id: stepData.id,
            order: index + 1,
            step_type: stepData.step_type,
            dataset_type: stepData.dataset_type,
            params,
          };
        })
        .filter(step => step !== null);

    if (this.currentMode === 'Augmentation') {
      const params = Object.entries(this.datasetSize.selectedParams).map(
        ([paramId, value]) => {
          return {
            id: Number(paramId),
            value: value as string | number | [number, number],
          };
        },
      );
      formattedSelections.push({
        id: Number(this.datasetSize.selectedStepId),
        step_type: String(this.datasetSize.selectedStep?.step_type),
        dataset_type: String(this.datasetSize.selectedStep?.dataset_type),
        params,
      });
    }
    if (this.activeItem) {
      const payload: createUpdatePipelineStepPayload = {
        pipeline_name: this.pipelineName,
        is_active: this.isPipelineMarkAsActive,
        steps: formattedSelections as createUpdatePipelineStepPayload['steps'],
      };
      if (payload.steps.length) {
        if (!this.pipelineId) {
          this.DataversionService.addDataVersionPipeline(
            this.activeItem,
            payload,
          )
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
              next: () => {
                this.loading = false;
                this.toastrService.success('Saving Pipeline successfully.');
                this.clearSelections();
                if (this.activeItem) {
                  this.onVersionClick(this.activeItem, this.versionName);
                }
              },
              error: error => {
                this.toastrService.error(
                  'An error occured while saving pipeline. Please try again later.',
                );
                console.error('HTTP error saving pipeline:', error);
              },
            });
        } else {
          this.DataversionService.updatePipelineDataById(
            this.pipelineId,
            payload,
          )
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
              next: () => {
                this.loading = false;
                this.toastrService.success('Updated Pipeline successfully.');
                this.clearSelections();
                if (this.activeItem) {
                  this.onVersionClick(this.activeItem, this.versionName);
                }
              },
              error: error => {
                this.loading = false;
                this.toastrService.error(
                  'An error occured while updating pipeline. Please try again later.',
                );
                console.error('HTTP error updating pipeline:', error);
              },
            });
        }
      }
    } else {
      console.log('Version ID not present...');
    }
  }

  /**
   * clear the selected steps
   */
  clearSelections(): void {
    this.selectedSteps = [];
  }

  /**
   * To add new data version.
   */
  addDataVersionName(): void {
    if (this.dataVersionForm.invalid) {
      this.dataVersionForm.markAllAsTouched();
      return;
    }
    if (this.projectId) {
      const name = this.dataVersionForm.value.name;
      this.DataversionService.addDataVersionName(name, this.projectId)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: () => {
            this.loading = false;
            this.toastrService.success('Data Version Added');
            this.closeDataVersionModal();
            this.isNewDataPipelineModal = false;
            this.getDataVersionData();
          },
          error: error => {
            this.loading = false;
            this.toastrService.error(
              'An error occured while adding data version. Please try again later.',
            );
            console.error('HTTP error adding data version:', error);
          },
        });
    } else {
      this.toastrService.warning('Project ID is missing');
    }
  }

  /**
   * Get the dataset versions for the particular project.
   */
  private getDataVersionData(): void {
    if (!this.projectId) {
      this.toastrService.warning('Project ID not present. Please try again.');
      return;
    }
    this.loading = true;
    this.DataversionService.getDatasetVersion(this.projectId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: DatasetVersionResponse) => {
          this.loading = false;
          this.dataVersions = response.data;
          if (this.dataVersions.length > 0) {
            this.activeItem = this.dataVersions[0].id;
            this.versionName = this.dataVersions[0].name;
            if (this.activeItem) {
              this.loading = false;
              this.onVersionClick(this.dataVersions[0].id, this.versionName);
            }
          } else {
            this.showSideModel = false;
          }
          this.cdr.detectChanges();
        },
        error: error => {
          this.loading = false;
          this.toastrService.error(
            'An error occured while loading dataset versions. Please try again later.',
          );
          console.error('HTTP error dataset versions:', error);
        },
      });
  }

  /**
   * To open the confirm delete dialog to delet the version with specified id.
   * @param versionId - the id of the version to be deleted.
   */
  confirmDelete(versionId: number | string): void {
    if (versionId) {
      this.datasetVersionId = versionId;
      this.isfileDeletePopup = true;
    } else {
      console.error('Invalid File ID');
    }
  }

  /**
   * Delete the user pipeline Data
   */
  deleteUserPipelineData(): void {
    if (!this.pipelineId) {
      this.toastrService.warning('Pipeline ID not present. Please try again.');
      return;
    }
    this.DataversionService.deleteUserPipleLineSteps(this.pipelineId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: result => {
          if (result.status === 'success') {
            this.closePipelineModal();
            this.toastrService.success(result.message);
            if (this.activeItem) {
              this.onVersionClick(this.activeItem, this.versionName);
            }
          } else {
            this.toastrService.error(
              'Failed to delete user pipeline. Please try again later.',
            );
          }
        },
        error: error => {
          this.toastrService.error(
            'An error occured while deleting the user pipeline. Please try again later.',
          );
          console.error('HTTP error deleting user pipeline:', error);
        },
      });
  }

  /**
   * To Delete dataset version.
   * @param versionId - dataset version id to be delete
   */
  deleteDataVersion(versionId: number): void {
    this.DataversionService.deleteDataVersion(versionId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: DatasetVersionResponse) => {
          if (response.status === 'success') {
            this.loading = false;
            this.toastrService.success(`${response.message}`);
            this.closeDeletePopup();
            this.isfileDeletePopup = false;
            this.getDataVersionData();
            this.cdr.markForCheck();
          } else {
            console.error('Error deleting file:', response.message);
          }
        },
        error: error => {
          this.loading = false;
          this.isfileDeletePopup = false;
          this.toastrService.error(
            'An error occurred while deleting the dataset version. Please try again.',
          );
          console.error('HTTP error deleting dataset version:', error);
          this.cdr.markForCheck();
        },
      });
  }

  /**
   * To check the selected steps with params are valid or not
   * @returns boolean flag
   */
  private isSelectionsValid(): boolean {
    return this.selectedSteps.every(step => {
      const selectedStepId = step.selectedStepId;
      const selectedParams = step.selectedParams;
      if (!selectedStepId) return false;
      const paramsValid = Object.values(selectedParams).every(paramValue => {
        return (
          paramValue !== null && paramValue !== undefined && paramValue !== ''
        );
      });
      return paramsValid;
    });
  }

  /**
   * Closed delete dialog.
   */
  closeDeletePopup(): void {
    this.isfileDeletePopup = false;
    this.clearSelections();
  }

  /**
   * To Edit a specific dataset version.
   * @param fileId - the dataset version id
   */
  editDataVersion(fileId: number | string): void {
    if (!fileId) {
      this.toastrService.warning('Invalid file ID. Please try again.');
      return;
    }
    if (fileId) {
      const versionToEdit = this.dataVersions.find(
        version => version.id === fileId,
      );

      if (versionToEdit) {
        this.EditfileID = fileId;
        this.isdataVersionModal = true;

        this.dataVersionForm.patchValue({
          name: versionToEdit.name,
        });
      } else {
        console.log('No version found with the given ID');
      }
    }
  }

  /**
   * Action to update the data version name.
   */
  updateDataVersionName(): void {
    if (this.dataVersionForm.invalid) {
      this.dataVersionForm.markAllAsTouched();
      return;
    }
    const name = this.dataVersionForm.value.name;
    const versionId = Number(this.EditfileID);
    this.DataversionService.updateDataVersion(versionId, {
      name,
    }).subscribe({
      next: () => {
        this.toastrService.success('Data version updated successfully');
        const versionIndex = this.dataVersions.findIndex(
          version => version.id === this.EditfileID,
        );
        if (versionIndex !== -1) {
          this.dataVersions[versionIndex].name = name;
        }
        this.closeDataVersionModal();
      },
      error: error => {
        this.toastrService.error(
          'An error occured while updating data version. Please try again later.',
        );
        console.error('HTTP error updating data version:', error);
      },
    });
  }

  /**
   * Map the user pipeline data to the desired format.
   * @param userPipelineData - the user pipleline data.
   * @returns list of pipeline names chips
   */
  private mapUserPipelineData(
    userPipelineData: DatasetVersion['user_pipeline'],
  ): PipelineChip[] {
    return (
      userPipelineData?.map(step => ({
        name: step.name,
        id: step.id,
        is_active: step.is_active,
      })) || []
    );
  }
  // Getter to retrieve the keys of the dataVersionTabs object
  get tabKeys(): string[] {
    return Object.keys(this.dataVersionTabs);
  }
}
