import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  BackendResponse,
  //ColorPalettes,
  PlotResponseAll,
  UserPlot,
  // PlotStyle,
  // PlotStyles,
  // UserPlot,
} from '../../../_models/visual-data/visual-data.model';

import { environment } from '../../../env/env';
import { GridsterItem } from 'angular-gridster2';
import {
  LayoutResFormatFromSaveDisplayLayoutAPI,
  PlotDataUI,
  SetFavoritePlotResponse,
} from '../models/plot.model';
import { PlotLayoutPayload } from '../../../services/plot.services';
import {
  ColumnChoices,
  ColumnValuesData,
  DataViewBackendResponse,
  FilterData,
} from '../../data-views/models/data-view.model';
import {
  Folder,
  UserPlotRequest,
} from '../../../_models/visual-data/plot.model';
import { ProjectData } from '../../dashborad/models/project.model';

@Injectable({
  providedIn: 'root',
})
export class PlotService {
  constructor(private http: HttpClient) {}
  private dataviewUrl = `${environment.apiUrl}`;
  private overviewUrl = `${environment.apiUrl}projects/`;
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  getCustomPlots(
    project_id: number,
    payload: string,
  ): Observable<BackendResponse<PlotResponseAll>> {
    return this.http.get<BackendResponse<PlotResponseAll>>(
      `${environment.apiUrl}visualization/user-plot?project_id=${project_id}&show_fig=True&${payload}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  savePlotLayoutData(
    project_id: number,
    layout: PlotLayoutPayload[],
  ): Observable<BackendResponse<LayoutResFormatFromSaveDisplayLayoutAPI[]>> {
    return this.http.patch<
      BackendResponse<LayoutResFormatFromSaveDisplayLayoutAPI[]>
    >(
      `${environment.apiUrl}visualization/user-plot/display-layout/?project_id=${project_id}`,
      layout,
      {
        headers: this.getHeaders(),
      },
    );
  }

  transformPlotDataResponseToPlotDataUI(plotDataResponse: PlotResponseAll) {
    const { meta_data, plots } = plotDataResponse;

    // TRANSFORMING PLOT DATA
    const transformedPlotData = plots.map((plot: UserPlot): PlotDataUI => {
      const transformedPlotLayout = { ...plot.plot?.layout, autosize: true };

      const transformedGridLayout = {
        ...plot.display_layout,
        plot_id: plot.plot_id,
      };

      return {
        filter_active: plot.filter_active,
        filter_instance_id: plot.filter_instance_id,
        fileId: plot.file_id,
        id: plot.plot_id,
        name: plot.plot_name,
        plot_type_name: plot.plot_name,
        json_data: {
          data: plot.plot?.data,
          layout: transformedPlotLayout,
        },
        display_layout: transformedGridLayout as GridsterItem,
        file_name: plot.file_name,
        favorite: plot.favorite,
      };
    });

    // TRANFORMING META DATA
    const transformedMetaData = { ...meta_data };

    return {
      ...plotDataResponse,
      plots: transformedPlotData,
      meta_data: transformedMetaData,
    };
  }

  async markUnmarkPlotFavPlot(
    plot_id: number,
    favorite: boolean,
  ): Promise<BackendResponse<SetFavoritePlotResponse>> {
    const httpHeaders = this.getHeaders(); // Angular HttpHeaders
    const headersObj: Record<string, string> = {};

    httpHeaders.keys().forEach(key => {
      const value = httpHeaders.get(key);
      if (value !== null) {
        headersObj[key] = value;
      }
    });
    const response = await fetch(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/favorite/?favorite=${favorite}`,
      {
        method: 'POST',
        headers: headersObj,
        body: JSON.stringify({}), // sending an empty body to match the original call
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json(); // assuming the response follows the BackendResponse format
  }

  //TODO unknown for now need it from backend
  async removePlotFromDb(plotId: number): Promise<BackendResponse<unknown>> {
    const httpHeaders = this.getHeaders(); // Angular HttpHeaders
    const headersObj: Record<string, string> = {};

    httpHeaders.keys().forEach(key => {
      const value = httpHeaders.get(key);
      if (value !== null) {
        headersObj[key] = value;
      }
    });

    const response = await fetch(
      `${environment.apiUrl}visualization/UserPlot/${plotId}/`,
      {
        method: 'DELETE',
        headers: headersObj,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  userPlotAssociateFilterObservable(
    plot_id: number,
    filters: string,
  ): Observable<BackendResponse<UserPlotRequest>> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.post<BackendResponse<UserPlotRequest>>(
      `${environment.apiUrl}visualization/user-plot/${plot_id}/associate-filters/`,
      filters,
      { headers },
    );
  }

  async userPlotAssociateFilterAsync(
    plot_id: number,
    filters: string,
  ): Promise<BackendResponse<UserPlotRequest>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(
      `${environment.apiUrl}visualization/user-plot/${plot_id}/associate-filters/`,
      {
        method: 'POST',
        headers,
        body: filters,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  private convertHttpHeadersToFetch(
    headers: HttpHeaders,
  ): Record<string, string> {
    const headersObj: Record<string, string> = {};
    headers.keys().forEach(key => {
      const value = headers.get(key);
      if (value !== null) {
        headersObj[key] = value;
      }
    });
    return headersObj;
  }

  /*
  TO-DO : 
  This function is Async version of getColumnValues present src/app/features/data-views/services/data-view.service.ts
  Keeping this function here for now to avoid merge conflicts with Data View Refactoring from Sona.
  */
  async getColumnValuesAsync(
    file_id: number | null,
  ): Promise<DataViewBackendResponse<ColumnValuesData>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(
      `${this.dataviewUrl}dataview/column-values/${file_id}`,
      {
        method: 'GET',
        headers,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /*
  TO-DO : 
  This function is Async version of getColumnChoice present src/app/features/data-views/services/data-view.service.ts
  Keeping this function here for now to avoid merge conflicts with Data View Refactoring from Sona.
  */
  async getColumnChoiceAsync(
    file_id: number | null,
  ): Promise<DataViewBackendResponse<ColumnChoices>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(
      `${this.dataviewUrl}dataview/column-choices/${file_id}`,
      {
        method: 'GET',
        headers,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /*
  TO-DO : 
  This function is Async version of getFilter present src/app/features/data-views/services/data-view.service.ts
  Keeping this function here for now to avoid merge conflicts with Data View Refactoring from Sona.
  */
  async getFilterAsync(filterId: number): Promise<BackendResponse<FilterData>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(
      `${this.dataviewUrl}dataview/filter/file/${filterId}/`,
      {
        method: 'GET',
        headers,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /*
  TO-DO : 
  This function is Async version of RemoveFilter present src/app/features/data-views/services/data-view.service.ts
  Keeping this function here for now to avoid merge conflicts with Data View Refactoring from Sona.
  */
  async removeFilterAsync(
    filterId: number | null,
  ): Promise<DataViewBackendResponse<null>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(
      `${this.dataviewUrl}dataview/filter/${filterId}/`,
      {
        method: 'DELETE',
        headers,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /*
  TO-DO : 
  This function is Async version of getProjectsById present src/app/services/overview.service.ts
  Keeping this function here for now to avoid merge conflicts with Data View Refactoring from Sona.
  */

  async getProjectByIdAsync(
    project_id: string,
  ): Promise<BackendResponse<ProjectData>> {
    const headers = this.convertHttpHeadersToFetch(this.getHeaders());

    const response = await fetch(`${this.overviewUrl}projects/${project_id}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  plotFileList(project_id: number): Observable<BackendResponse<Folder[]>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<Folder[]>>(
      `${environment.apiUrl}projects/projects/${project_id}/files/filter/?search_files=true&file_type=non_image`,
      { headers },
    );
  }

  getNextPageByUrl(url: string): Observable<BackendResponse<Folder[]>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<Folder[]>>(url, { headers });
  }
}
