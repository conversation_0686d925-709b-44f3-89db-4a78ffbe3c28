import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
import { LoaderService } from '../../../services/loader.service';
import { SidebarPages } from '../../../_models/common.model';

@Component({
  selector: 'app-outer-layout',
  templateUrl: './outer-layout.component.html',
  styleUrl: './outer-layout.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class OuterLayoutComponent implements OnInit {
  currPage = 'projects';
  titleText = '';

  constructor(
    public loaderService: LoaderService,
    private router: Router,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.setHeaderValues(this.splitUrl(this.router.url));
    this.router.events
      .pipe(filter(e => e instanceof NavigationEnd))
      .subscribe(res => {
        if (res instanceof NavigationEnd) {
          this.setHeaderValues(this.splitUrl(res.url));
        }
      });
    this.loaderService.isLoading$.subscribe(() => {
      this.cdr.detectChanges();
    });
  }

  setHeaderValues(page: string): void {
    switch (page) {
      case SidebarPages.dashboard:
        this.currPage = 'projects';
        this.titleText = '';
        break;
      case SidebarPages.dataView:
        this.currPage = 'data-view';
        this.titleText = 'Tabular Data View';
        break;
      case SidebarPages.dataVersion:
        this.currPage = 'data-version';
        this.titleText = 'Data Version';
        break;
      case SidebarPages.exploreView:
        this.currPage = 'explore-view';
        this.titleText = 'Visual Data Exploration';
        break;
      case SidebarPages.training:
        this.currPage = 'training';
        this.titleText = 'ML Model Recommendation';
        break;
      case SidebarPages.results:
        this.currPage = 'results';
        this.titleText = 'Model Training Overview';
        break;

      default:
        break;
    }
  }

  splitUrl(url: string): string {
    return url.includes('?')
      ? url.split('?')[0].replace('/', '')
      : url.split('/')[1].replace('/', '');
  }
}
