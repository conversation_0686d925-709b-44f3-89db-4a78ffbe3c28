<div class="grid grid-cols-3 justify-between">
  <!-- Left Section -->
  <div class="flex items-center space-x-4">
    <button mat-icon-button>
      <mat-icon>arrow_back</mat-icon>
    </button>
    <span class="font-medium text-lg">Filename.csv</span>
  </div>

  <!-- Center Section Of Data Toggle -->
  <div class="flex items-center space-x-2 justify-center">
    <button
      mat-stroked-button
      class="!border-solid !border-[1px] !h-12 !border-gray-300 flex items-center space-x-1 !bg-white !text-txt-color"
      (click)="openAddColumnModal()">
      <mat-icon>add</mat-icon>
      <span>Add Column</span>
    </button>
    <button
      mat-stroked-button
      class="!border-solid !border-[1px] !h-12 !border-gray-300 flex items-center space-x-1 !bg-white !text-txt-color"
      (click)="openFilterDataModal()">
      <mat-icon>filter_list</mat-icon>
      <span>Filter Data</span>
    </button>
    <button
      class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-white !text-txt-color">
      <mat-icon>shuffle</mat-icon>
    </button>
  </div>

  <!-- Center Section Of Statistics Toggle -->
  <!-- <div class="flex justify-center gap-2">
        <div >
            <mat-button-toggle-group name="options" aria-label="Segmented button group" class="segmented-button-group ">
                <mat-button-toggle value="numericalColumns">Numerical Columns</mat-button-toggle>
                <mat-button-toggle value="categoricalColumns">Categorical Columns</mat-button-toggle>
            </mat-button-toggle-group>

        </div>
        <div>
            <mat-button-toggle-group name="options" aria-label="Segmented button group"
                class="segmented-button-group w-auto">
                <mat-button-toggle value="original">
                    <mat-icon class="!h-5 !w-5 mb-2 ">table_rows </mat-icon> Original</mat-button-toggle>
                <mat-button-toggle value="processed" class="flex items-center !gap-4"><mat-icon class="mb-2">auto_fix_normal</mat-icon>Processed</mat-button-toggle>
            </mat-button-toggle-group>
        </div>
    </div> -->

  <!-- Right Section -->

  <div class="flex items-center space-x-1 justify-end">
    <mat-button-toggle-group
      name="options"
      aria-label="Segmented button group"
      class="segmented-button-group">
      <mat-button-toggle value="data">Data</mat-button-toggle>
      <mat-button-toggle value="statistics">Statistics</mat-button-toggle>
    </mat-button-toggle-group>
  </div>
</div>

<!-- Add Column Modal -->
<div
  *ngIf="isAddColumnModalOpen"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[700px] h-auto">
    <div class="flex justify-between items-center object-center p-4">
      <h6 class="font-medium">Add Column</h6>
      <button
        mat-icon-button
        (click)="closeAddColumnModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300"></div>

    <div>
      <form>
        <mat-form-field class="w-full">
          <label
            for="choose-name"
            class="flex flex-col mb-4 font-medium text-base"
            >New Column</label
          >
          <input
            id="choose-name"
            placeholder="Column Name"
            class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md" />
        </mat-form-field>

        <mat-form-field>
          <label for="choose-column-1" class="mb-4 font-medium text-base"
            >Column 1</label
          >
          <input
            id="choose-column-1"
            placeholder="Choose Column"
            class="outline-none bg-transparent flex-1 border px-3 bg-white h-12 rounded-md" />
        </mat-form-field>
        <mat-form-field>
          <label for="choose-column-2" class="mb-4 font-medium text-base"
            >Operator</label
          >
          <input
            id="choose-column-2"
            placeholder="Choose Column"
            class="outline-none bg-transparent flex-1 border px-3 bg-white h-12 rounded-md" />
        </mat-form-field>
        <mat-form-field>
          <label for="choose-column-3" class="mb-4 font-medium text-base"
            >Column 2</label
          >
          <input
            id="choose-column-3"
            placeholder="Choose Column"
            class="outline-none bg-transparent flex-1 border px-3 bg-white h-12 rounded-md" />
        </mat-form-field>
      </form>
      <button
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-transparent !text-txt-color">
        <mat-icon>add</mat-icon>
      </button>
    </div>
    <div class="flex justify-start mt-3">
      <button mat-flat-button class="!bg-[#296197]" (click)="saveColumn()">
        Save
      </button>
    </div>
  </div>
</div>
<!-- Filter Column Modal -->
<div
  *ngIf="isFilterDataModalOpen"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-auto h-auto">
    <div class="flex justify-between items-center p-4">
      <h6 class="font-medium">Filter Data</h6>
      <button
        mat-icon-button
        (click)="closeFilterDataModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300 mb-4"></div>

    <div class="space-y-4">
      <div class="flex flex-row items-center justify-around gap-4">
        <p class="font-medium text-base flex-grow-2">Column</p>
        <p class="font-medium text-base flex-grow-1">Operator</p>
        <p class="flex font-medium text-base gap-3">Value</p>
      </div>

      <div class="flex flex-row items-center gap-4 mt-4">
        <mat-select
          class="outline-none bg-white flex-grow border px-3 py-2 shadow-sm w-96 h-12 rounded-md">
          <mat-option value="" disabled selected>Choose Column</mat-option>
          <mat-option value="column1">Column 1</mat-option>
          <mat-option value="column2">Column 2</mat-option>
        </mat-select>

        <select
          class="outline-none bg-white border px-3 py-2 shadow-sm h-12 rounded-md">
          <option value="" disabled selected>Select Operator</option>
          <option value="equals">Equals</option>
          <option value="not-equals">Not Equals</option>
        </select>

        <input
          placeholder="Enter Value"
          class="outline-none bg-transparent flex-grow-2 border px-3 py-2 shadow-sm bg-white h-12 rounded-md" />

        <button class="w-8 flex justify-center items-center">
          <mat-icon>delete_outline</mat-icon>
        </button>
      </div>
      <button
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-transparent !text-txt-color">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div class="flex justify-start mt-4">
      <button
        mat-flat-button
        class="!bg-[#296197] text-white"
        (click)="saveFilterData()">
        Save
      </button>
    </div>
  </div>
</div>
