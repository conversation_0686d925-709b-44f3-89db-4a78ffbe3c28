import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LoginComponent } from './login.component';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let authService: jest.Mocked<AuthService>;
  let router: jest.Mocked<Router>;
  let toastrService: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    const authServiceMock = {
      login: jest.fn(),
    } as unknown as jest.Mocked<AuthService>;
    const routerMock = {
      navigateByUrl: jest.fn(),
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, LoginComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should have a valid loginForm initialized', () => {
      expect(component.loginForm).toBeDefined();
      expect(component.loginForm.get('email')).toBeTruthy();
      expect(component.loginForm.get('password')).toBeTruthy();
      expect(component.loginForm.get('keep_me_logged_in')).toBeTruthy();
    });

    it('should have default values for the form controls', () => {
      expect(component.loginForm.get('email')?.value).toBe('');
      expect(component.loginForm.get('password')?.value).toBe('');
      expect(component.loginForm.get('keep_me_logged_in')?.value).toBe(false);
    });
  });

  describe('Form Validation', () => {
    it('should validate email as required and proper format', () => {
      const emailControl = component.loginForm.get('email');
      emailControl?.setValue('');
      expect(emailControl?.hasError('required')).toBe(true);

      emailControl?.setValue('invalid-email');
      expect(emailControl?.hasError('email')).toBe(true);

      emailControl?.setValue('<EMAIL>');
      expect(emailControl?.valid).toBe(true);
    });

    it('should validate password as required with minimum length', () => {
      const passwordControl = component.loginForm.get('password');
      passwordControl?.setValue('');
      expect(passwordControl?.hasError('required')).toBe(true);

      passwordControl?.setValue('short');
      expect(passwordControl?.hasError('minlength')).toBe(true);

      passwordControl?.setValue('validpassword');
      expect(passwordControl?.valid).toBe(true);
    });
  });

  describe('Navigation', () => {
    it('should navigate to sign-up page', () => {
      component.navigateTosignUp();
      expect(router.navigate).toHaveBeenCalledWith(['/auth/register']);
    });

    it('should navigate to forget-password page', () => {
      component.navigateToForgetPassword();
      expect(router.navigate).toHaveBeenCalledWith(['auth/forget-password']);
    });
  });

  describe('Login Submission', () => {
    beforeEach(() => {
      component.loginForm.get('email')?.setValue('<EMAIL>');
      component.loginForm.get('password')?.setValue('validpassword');
    });

    it('should call authService login with valid form data', () => {
      const mockResponse = {
        message: 'Login successful',
        accesstoken: 'abc123',
        refreshtoken: 'xyz456',
      };
      authService.login.mockReturnValue(of(mockResponse));

      component.onSubmit();

      expect(authService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'validpassword',
        keep_me_logged_in: false,
      });
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message);
    });

    it('should navigate to dashboard on successful login', () => {
      const mockResponse = {
        message: 'Login successful',
        accesstoken: 'abc123',
        refreshtoken: 'xyz456',
      };
      authService.login.mockReturnValue(of(mockResponse));

      component.onSubmit();

      expect(localStorage.getItem('access_token')).toEqual(
        mockResponse.accesstoken,
      );
      expect(router.navigateByUrl).toHaveBeenCalledWith('/dashboard/projects');
    });

    it('should store refresh token in localStorage if keep_me_logged_in is true', () => {
      const mockResponse = {
        message: 'Login successful',
        accesstoken: 'abc123',
        refreshtoken: 'xyz456',
      };
      authService.login.mockReturnValue(of(mockResponse));

      component.loginForm.get('keep_me_logged_in')?.setValue(true);
      component.onSubmit();

      expect(localStorage.getItem('refreshtoken')).toEqual(
        mockResponse.refreshtoken,
      );
    });

    it('should store refresh token in sessionStorage if keep_me_logged_in is false', () => {
      const mockResponse = {
        message: 'Login successful',
        accesstoken: 'abc123',
        refreshtoken: 'xyz456',
      };
      authService.login.mockReturnValue(of(mockResponse));

      component.loginForm.get('keep_me_logged_in')?.setValue(false);
      component.onSubmit();

      expect(sessionStorage.getItem('refreshtoken')).toEqual(
        mockResponse.refreshtoken,
      );
    });

    it('should show error message on login failure', () => {
      const mockError = { error: { error: 'Invalid credentials' } };
      authService.login.mockReturnValue(throwError(mockError));

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith(mockError.error.error);
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle passwordFieldType between "password" and "text"', () => {
      expect(component.passwordFieldType).toBe('password');

      component.togglePasswordVisibility();
      expect(component.passwordFieldType).toBe('text');

      component.togglePasswordVisibility();
      expect(component.passwordFieldType).toBe('password');
    });
  });
});
