<app-loader [loading]="loading"></app-loader>
<div class="flex flex-row justify-between w-full h-26 px-6 pb-6">
  <div class="flex flex-col">
    <span class="text-base">Projects Name / Training</span>
    <span class="text-4xl">{{ projectName }}</span>
  </div>
</div>
<div>
  <mat-drawer-container class="example-container h-[600px]">
    <mat-drawer mode="side" opened
      ><mat-nav-list>
        <mat-list-item
          (click)="OnMenuItemSelect('overview')"
          [activated]="currentTab === 'overview'"
          >Model Overview</mat-list-item
        >
        <mat-list-item
          (click)="OnMenuItemSelect('inference')"
          [activated]="currentTab === 'inference'"
          *ngIf="!errorMsg"
          >Inference</mat-list-item
        >
        <mat-list-item
          (click)="OnMenuItemSelect('metrics')"
          [activated]="currentTab === 'metrics'"
          *ngIf="!errorMsg"
          >Metrics</mat-list-item
        >
        <mat-list-item
          (click)="OnMenuItemSelect('report')"
          [activated]="currentTab === 'report'"
          *ngIf="isClassification && !errorMsg"
          >Classification Report</mat-list-item
        >
        <mat-list-item
          (click)="OnMenuItemSelect('confusion')"
          [activated]="currentTab === 'confusion'"
          *ngIf="isClassification && !errorMsg">
          Confusion Matrix</mat-list-item
        >
      </mat-nav-list></mat-drawer
    >
    <mat-drawer-content>
      <div *ngIf="showTable && currentTab !== 'inference'">
        <app-training-table
          [trainingId]="trainingId"
          [currentTab]="currentTab"
          [tableData]="tableData"></app-training-table>
      </div>
      <div *ngIf="currentTab === 'confusion'">
        <plotly-plot [data]="plotData" [layout]="plotLayout"> </plotly-plot>
      </div>
      <div>
        <app-training-inference
          [trainingId]="trainingId"
          *ngIf="currentTab === 'inference'"></app-training-inference>
      </div>
      <div class="error-message" *ngIf="errorMsg" [innerHTML]="errorMsg"></div>
    </mat-drawer-content>
  </mat-drawer-container>
</div>
