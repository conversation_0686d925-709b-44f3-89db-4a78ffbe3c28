import { Component } from '@angular/core';
import { IntroService } from '../../../introjs.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class HeaderComponent {
  constructor(
    private introService: IntroService,
    private toastService: ToastrService,
  ) {}

  notificationLists = [
    {
      initial: 'FR',
      bgColor: '#296197',
      title: 'Feature Request',
      message: 'Request & vote new features.',
      link: 'https://aicuflow.canny.io/feature-requests',
    },
    {
      initial: 'FB',
      bgColor: '#296197',
      title: 'Feedback',
      message: 'Share your thougts.',
      link: 'https://aicuflow.canny.io/feature-requests',
    },
    {
      initial: 'U',
      bgColor: '#D2BCFD',
      title: 'New Updates',
      message: 'See more details here.',
      link: 'https://aicuflow.canny.io/feature-requests',
    },
    {
      initial: 'ON',
      bgColor: '#D2BCFD',
      title: 'Onboarding',
      message: 'Restart the Onboarding Process',
      callFunction: this.startOnboarding.bind(this),
      link: window.location.origin,
    },
  ];

  startOnboarding() {
    document.cookie =
      'introjs-dontShowAgain=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
    this.introService.updateOnboardingStatus(false).subscribe({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      next: (res: any) => {
        console.log('Onboarding response:', res);
        window.location.href = window.location.origin;
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      error: (err: any) => {
        console.error('Onboarding failed:', err);
      },
    });
  }
  redirectToLink(url: string): void {
    window.open(url, '_blank'); // Opens the link in a new tab
  }
}
