import { PaginationInfo } from '../../../_models/visual-data/visual-data.model';

export interface MachineLearningModels {
  category?: string;
  created_at?: string;
  description?: string;
  name: string;
  id: number;
  is_active?: boolean;
  machine_learning_tasks: string;
  updated_at?: string;
}

export interface MachineLearningModelInfoInterface {
  data: MachineLearningModels[];
  errors: Error;
  message: string;
  pagination: PaginationInfo;
  status: string;
}

export interface ColumnInfoData {
  data: ColumnsInterface;
  errors: {
    error: string;
  };
  message: string;
  pagination: string;
  status: string;
}

export interface ColumnsInterface {
  features: ColumnInfo[];
  targets: ColumnInfo[];

  data_type_rows: number;
  delimiter: string;
  header: boolean;
  header_row: number;
  file_name: string;
  id: number;
}

export interface ColumnInfo {
  color?: string;
  id: number;
  name: string;
  options?: string[];
  type?: string;
  checked?: boolean;
}

export interface CreateMLmodelPayload {
  machine_learning_model: string;
  name: string;
  selected_target: TargetFeatureInterface;
  selected_features: TargetFeatureInterface[];
  test_size: number;
}

export interface CreateMlmodelResponse {
  data: {
    MLTraining_id: number;
  };
  errors: {
    error: string;
  };
  message: string;
  pagination: {
    limit: number;
    next_cursor: string;
  };
  status: string;
}

export interface MLTrainingProjectResponse {
  data: MLTrainingProjectInfo[];
  pagination: {
    next_cursor: number;
    limit: number;
    total: number;
  };
  success: boolean;
}

export interface MLTrainingProjectInfo {
  id: number;
  created_at: string;
  file: {
    id: number;
    file_name: string;
  };
  machine_learning_model: {
    id: string;
    name: string;
  };
  name: string;
  options: unknown;
  project: number;
  selected_features: TargetFeatureInterface[];
  selected_target: TargetFeatureInterface;
  status: string;
  updated_at: string;
  user: number;
  test_size: number;
}

export interface TargetFeatureInterface {
  id: number;
  name: string;
  type: string;
}

export interface TrainingOverviewInterface {
  success: boolean;
  data: {
    id: number;
    created_at: string;
    file: {
      id: number;
      file_name: string;
    };
    machine_learning_model: {
      id: string;
      name: string;
    };
    error_msg: string;
    name: string;
    options: unknown;
    project: number;
    selected_features: number[];
    selected_target: number;
    status: string;
    updated_at: string;
    user: number;
    test_size: number;
  };
}
export type MLModelInfoByDate = Record<string, MLTrainingProjectInfo>;

export interface MlProjectBasedOnTimeline {
  today: MLTrainingProjectInfo[];
  last_week: MLTrainingProjectInfo[];
  last_month: MLTrainingProjectInfo[];
  last_year: MLTrainingProjectInfo[];
}

export interface StartMlTrainingResponse {
  status: string;
  message: string;
  data: {
    ml_training_id: number;
    ECS_Response: string;
    ml_model: string;
  };
  errors: {
    error: string;
  };
  pagination: {
    next_cursor: number;
    limit: number;
  };
}

export const mlProjectInitialState = {
  today: [],
  last_week: [],
  last_month: [],
  last_year: [],
};

export interface TimelineInterface {
  today: {
    title: string;
    dateRange: string;
  };
  last_week: {
    title: string;
    dateRange: string;
  };
  last_month: {
    title: string;
    dateRange: string;
  };
  last_year: {
    title: string;
    dateRange: string;
  };
}
export interface TrainingStatusInterface {
  completed: TitleIcon;
  configured: TitleIcon;
  training: TitleIcon;
  failed: TitleIcon;
  stopped: TitleIcon;
}

export interface TitleIcon {
  title: string;
  icon: string;
}

export interface TrainingTableData {
  displayData: {
    metric?: string;
    value: string | number;
    name?: string;
  }[];
  displayColumns: string[];
}
