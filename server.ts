import { APP_BASE_HREF } from '@angular/common';
import express from 'express';
import helmet from 'helmet';
import { fileURLToPath } from 'node:url';
import { dirname, join, resolve } from 'node:path';
import bootstrap from './src/main.server';
import { renderApplication } from '@angular/platform-server';

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');
  const indexHtml = join(serverDistFolder, 'index.server.html');

  server.set('view engine', 'html');
  server.set('views', browserDistFolder);

  // Use Helmet to set security-related headers
  server.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: [
            "'self'",
            'https://cdn.plot.ly', // Allow Plotly CDN
            "'unsafe-eval'", // Allow eval() only if necessary
          ],
          styleSrc: ["'self'", 'https://cdn.plot.ly'], // Allow inline styles if needed
          imgSrc: ["'self'", 'data:'],
          connectSrc: ["'self'", 'https://cdn.plot.ly'],
          fontSrc: ["'self'", 'https://fonts.gstatic.com'],
          objectSrc: ["'none'"],
        },
      },
    }),
  );

  // Serve static files from /browser
  server.get(
    '*.*',
    express.static(browserDistFolder, {
      maxAge: '1y',
    }),
  );

  // All regular routes use the Angular engine
  server.get('**', async (req, res, next) => {
    try {
      const { protocol, originalUrl, baseUrl, headers } = req;

      const html = await renderApplication(() => bootstrap(), {
        document: indexHtml,
        url: `${protocol}://${headers.host}${originalUrl}`,
        platformProviders: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
      });

      res.status(200).send(html);
    } catch (error) {
      console.error('Error rendering application:', error);
      next(error);
    }
  });

  return server;
}

function run(): void {
  const port = process.env['PORT'] || 4000;

  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

run();
