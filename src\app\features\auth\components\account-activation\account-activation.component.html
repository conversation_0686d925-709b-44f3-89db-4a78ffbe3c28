<app-loader [loading]="loading"></app-loader>

<div class="flex w-full h-screen justify-center">
  <div class="h-full w-full flex flex-col justify-center items-center">
    <div class="">
      <img src="../../assets/Logo.png" alt="Side Image" class="size-16 mb-5" />
    </div>
    <div *ngIf="!isActivationFailed">
      <h1 class="flex justify-center">{{ processing }}</h1>
    </div>
    <div *ngIf="isActivationFailed" class="w-1/3">
      <div class="text-3xl mb-12 flex justify-center">
        Acccount Activation Needs a Retry
      </div>
      <form #emailForm="ngForm">
        <div class="font-medium text-lg">Email</div>
        <div>
          <input
            type="email"
            id="email"
            name="emailForm"
            placeholder="E-Mail"
            [(ngModel)]="email"
            class="form-input"
            required
            email
            #emailInput="ngModel" />
          <div
            *ngIf="emailInput.invalid && emailInput.touched"
            class="text-red-500 text-sm mt-1">
            <div *ngIf="emailInput.errors?.['required']">
              E-mail is required.
            </div>
            <div *ngIf="emailInput.errors?.['email']">
              Please enter a valid email address.
            </div>
          </div>
        </div>
        <div>
          <button
            class="cursor-pointer mt-4 w-full"
            mat-flat-button
            (click)="resendActivationLink()">
            Resend Activation
          </button>
          <span class="block text-sm text-gray-400 mt-4">
            {{ g_const.dontHaveAcc }}
            <a
              href="javascript:void(0)"
              class="text-txt-color no-underline"
              (click)="navigateTosignUp()"
              >{{ g_const.signUp }}</a
            >
          </span>
        </div>
      </form>
    </div>
  </div>
</div>
