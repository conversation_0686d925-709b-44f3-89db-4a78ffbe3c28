/* eslint-disable @typescript-eslint/no-explicit-any */

export interface ProjectPayload {
  title: string;
  description: string;
}

//TODO GET /projects/
export interface ProjectList {
  results: Project[];
  data: Project[];
  pagination: {
    count: number;
    next: string | null;
    previous: string | null;
  };
}

//TODO POST /projects/
//TODO PUT /projects/${projectId}/
//TDOD GET /projects/${finalString}
export interface Project {
  id: number;
  owner: number;
  title: string;
  description: string;
  created_at: string | Date;
  updated_at?: string | Date;
  file_count?: number;
  files_size?: string;
  plot_count?: number;
  model_count?: number;
  train_runs: number;
  folder_key: string;
  folder_id: number;
  uploadedFiles: UploadedFile[];
  unknownFiles: any[] | undefined;
}

//TODO DELETE projects/hypothesis/${id}/
export interface HypothesisPayload {
  project: string | number;
  id?: string;
  hypothesis_type: string;
  hypothesis: string;
}

//TODO GET projects/${project_id}/hypothesis/
export interface HypothesisList {
  results: Hypothesis;
  count: number;
  next: string | null;
  previous: string | null;
  data: Hypothesis[];
}

//TODO PUT projects/hypothesis/${id}/
//TODO POST projects/${project_id}/hypothesis/
export interface Hypothesis {
  id?: number;
  project: string;
  folder_id?: number | string;
  hypothesis_type: string;
  hypothesis: string;
  created_at?: string | Date;
  updated_at?: string | Date;
}

//TODO GET projects/${project_id}
//TODO GET projects/projects/${project_id}/
export interface ProjectData {
  id: number;
  owner: number;
  title: string;
  description: string;
  field_plot_styles: number;
  created_at: string;
  updated_at: string;
  file_count: number;
  files_size: string;
  plot_count: number;
  model_count: number;
  train_runs: number;
  folder_key: string;
  folder_id: number;
  hypotheses: Hypothesis[];
}

export interface ProjectDataDetails {
  status: string;
  message: string;
  data: ProjectData;
  errors: any | null;
  pagination: any | null;
}

export interface FileType {
  id?: number;
  columns?: [];
  processed?: boolean;
  name: string;
  processing: boolean;
  fileKey: string;
}

//TODO GET /files/project/${id}
export interface UploadedFile {
  id: number;
  name?: string;
  file_key: string;
  user: number;
  project: number;
  file_name: string;
  created_at: string | Date;
  updated_at: string | Date;
  file_size?: string;
  original_file_id: number | null;
  processed_file_id: number | null;
  status: string;
  id_column: string;
  processed?: boolean;
  folder: number;
}

//TODO POST /files/upload-url/${folder_id}
export interface SignedUrlResponse {
  message: string;
  data: {
    signed_url: string;
    file_key: string;
  };
}

export interface ColumnType {
  name: string;
  type: string;
  options: unknown[];
  operatorType?: string;
}

//TODO GET /ColumnInfo/${file_id}/
export interface ColumnNameType {
  columns: ColumnType[];
}

export interface DirectoryEntry {
  id: number;
  type: 'directory';
  name: string;
  children: (DirectoryEntry | FileEntry)[];
}

export interface FileEntry {
  id: number;
  type: 'file';
  name: string;
  path: string;
}

export interface NewHypothesisFormValues {
  id?: number;
  hypothesis_type: string;
  hypothesis: string;
}

export interface NewProjectFormValues {
  id?: number;
  title: string;
  description: string;
}

//TODO min_date & max_date might be Date
export interface HypothesisFilter {
  hypothesis_type?: string;
  minDate: string;
  maxDate: string;
}
