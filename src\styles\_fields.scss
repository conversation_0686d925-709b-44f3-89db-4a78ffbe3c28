@use '@angular/material' as mat;
@use './color.scss' as custom-color;

.mat-mdc-select-panel {
  margin-top: 18px;
}

.dropdown-btn {
  background-color: custom-color.$sys-select-border;
  border-radius: 4px !important;
  padding: 8px !important;
  height: 48px !important;
  border: 1px solid #181c20 !important;
  font-size: 10px !important;
}
.plot-form mat-select {
  background-color: custom-color.$sys-select-border;
  border-radius: 4px;
  padding: 8px;
  height: 48px;
  border: 1px solid #181c20;
  width: 100%;

  .mat-select-arrow {
    color: custom-color.$sys-select-border;
  }

  .mat-select-value {
    color: custom-color.$sys-select-border;
    font-size: 16px;
  }

  &:hover {
    background-color: custom-color.$sys-select-border;
  }
}

.mat-placeholder {
  color: custom-color.$sys-select-border;
}

.mat-option {
  &:hover {
    background-color: custom-color.$sys-select-border;
  }

  &.mat-selected {
    background-color: custom-color.$sys-select-border;
    color: white;
  }
}
