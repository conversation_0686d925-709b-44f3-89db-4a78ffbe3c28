.container {
  border: 1;

  border-radius: 12px;
  height: 68px;
}

/* training-result */
.custom-button {
  padding: 6px 0;
  /* Use your desired gray color */
  border-radius: 8px; /* Same as rounded-xl */
  color: white;
  background-color: #296197;
}

.custom-button-2 {
  width: 210px;
  padding: 6px 0;
  border: 2px solid #ccc; /* Use your desired gray color */
  border-radius: 1rem; /* Same as rounded-xl */
  border-color: #ccc;
  transition:
    background-color 0.1s ease,
    color 0.1s ease,
    border-color 0.1s ease;
}
.custom-button-2:hover {
  background-color: #296197;
  color: white;
  border-color: #296197;
}

.mat-icon-1 {
  background-color: white; /* Set background to white */
  border-radius: 50%; /* Make the background circular */
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 40px; /* Adjust the size */
  height: 40px; /* Adjust the size */
  font-size: 24px; /* Adjust the icon size */
  color: black; /* Set the icon color to black */
}

.custom-button-3 {
  /* width: 210px; */
  padding: 6px 0;
  border: 2px solid #ccc; /* Use your desired gray color */
  border-radius: 1rem; /* Same as rounded-xl */
  border-color: #ccc;
  transition:
    background-color 0.1s ease,
    color 0.1s ease,
    border-color 0.1s ease;
}
