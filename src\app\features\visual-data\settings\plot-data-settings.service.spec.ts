import { TestBed } from '@angular/core/testing';

import { PlotDataSettingsService } from './plot-data-settings.service';
import { PlotStyleOption } from '../../../_models/visual-data/visual-data.model';
import { PlotStyleOptionBuilder } from '../../../_models/PlotStyleOptionBuilder';

describe('PlotDataSettingsService', () => {
  let service: PlotDataSettingsService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(PlotDataSettingsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getFormGroups', () => {
    it('should transform a PlotstyleOption Array with no children layer elements only to a formgroup object', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });

    it('should transform a PlotstyleOption Array with one children layer elements only to a formgroup object', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });

    it('should transform a PlotstyleOption Array with two children layer elements only to a formgroup object', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });

    it('should transform a PlotstyleOption Array with data_type boolean children layer elements only to a formgroup object', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });
  });

  describe('convertFormToPlotStyle', () => {
    it('should convert empty plotstyleForm to paylod', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });

    it('should convert plotstyleForm with one boolean change to paylod', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });

    it('should convert plotstyleForm with multiple different changes to paylod', () => {
      const plotStyleOptionMock: PlotStyleOption = new PlotStyleOptionBuilder();

      const result = service.getFormGroups(plotStyleOptionMock);
      console.log(result);
    });
  });
});
