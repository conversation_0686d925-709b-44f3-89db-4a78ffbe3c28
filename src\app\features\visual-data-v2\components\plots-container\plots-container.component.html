<div class="h-full">
  <!-- GRIDSTER CONTAINER -->
  <gridster [options]="gridsterConfig()" class="rounded-lg">
    @let allPlotsData = visualDataStore.loadedPlotsData();
    @if (visualDataStore.isLoading() === true) {
      <div class="flex justify-center items-center p-5 w-full h-full">
        <mat-spinner></mat-spinner>
      </div>
    } @else if (allPlotsData !== null) {
      @for (plotData of allPlotsData['plots']; track plotData.id) {
        <!-- GRIDSTER ITEM -->
        <gridster-item
          [item]="plotData['display_layout'] ?? $any({})"
          class="rounded-lg drop-shadow-md border border-gray-200 dark:border-gray-500">
          <!-- PLOT CARD -->
          <app-plot-card [plotData]="plotData" />
          <div class="relative pr-4">
            <button
              class="absolute bottom-1 right-1 drag-handler"
              mat-icon-button>
              <mat-icon class="ml-auto rotate-90 cursor-move"
                >open_with
              </mat-icon>
            </button>
          </div>
        </gridster-item>
      }
    }
  </gridster>
</div>
