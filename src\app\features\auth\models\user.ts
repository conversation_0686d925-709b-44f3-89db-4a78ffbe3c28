export interface RegisterPayload {
  email: string;
  first_name: string;
  last_name: string;
  password1: string;
  password2: string;
}

export interface LoginPayload {
  email: string;
  password: string;
  keep_me_logged_in: boolean;
}
export interface LoginSuccessful {
  accesstoken: string;
  refreshtoken: string;
  message: string;
}
export interface ForgetPasswordPayload {
  email: string;
}

export interface ActivateUserParams {
  uid: string;
  token: string;
}

//TODO POST forgot-password/
export interface RecoverSuccessful {
  reset_url: string;
  message: string;
}

export interface NewPasswordPayload {
  password_reset_token: string;
  password: string;
}

//TODO POST reset-password/
export interface NewPasswordSuccessful {
  message: string;
}

//TODO POST activate/${user.uid}/${user.token}/
export interface AccountActivationResponse {
  message?: string;
  error?: string;
}
