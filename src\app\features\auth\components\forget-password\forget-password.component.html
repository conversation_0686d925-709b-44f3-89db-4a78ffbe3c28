<div class="flex flex-col h-screen md:flex-row">
  <div class="relative">
    <img
      src="../../assets/Welcome.png"
      alt="Side Image"
      class="w-full h-[30vh] md:h-[100vh] md:w-[40vw]" />

    <div
      class="absolute inset-y-0 left-0 flex items-center justify-center ml-4 sm:ml-8">
      <div class="text-white">
        <h1 class="text-white m-0 welcome">Welcome</h1>
        <p class="mt-3">Please Enter Your Details.</p>
      </div>
    </div>
  </div>
  <div
    class="flex-1 p-5 rounded-lg flex flex-col justify-center items-center lg:ml-[-15px]">
    <form
      class="w-full max-w-xs lg:max-w-sm"
      [formGroup]="forgotPassword"
      (ngSubmit)="onSubmit()">
      <div class="mb-[60px] flex flex-col">
        <img
          src="../../assets/Logo.png"
          alt="Side Image"
          class="h-20 w-20 mb-6" />
        <h3 class="text-lg font-semibold">Forgot Password?</h3>
      </div>

      <div class="w-full mb-6">
        <label for="email" class="block mb-2 text-sm font-medium">{{
          g_const.email
        }}</label>
        <input
          type="email"
          id="email"
          placeholder="E-Mail"
          formControlName="email"
          class="w-full p-3 rounded-lg border border-gray-300 box-border leading-6" />
        <!-- Email validation error messages -->
        <div
          *ngIf="
            forgotPassword.get('email')?.touched &&
            forgotPassword.get('email')?.invalid
          "
          class="text-red-500 text-sm mt-1">
          <div *ngIf="forgotPassword.get('email')?.errors?.['required']">
            {{ g_const.emailRequired }}
          </div>
          <div *ngIf="forgotPassword.get('email')?.errors?.['email']">
            {{ g_const.emailInvalid }}
          </div>
        </div>
      </div>
      <span class="block text-sm text-gray-400">
        By proceeding, you agree to our
        <a
          href="https://www.aicuflow.com/privacy-policy"
          target="_blank"
          class="text-txt-color no-underline">
          data privacy policy</a
        >
      </span>

      <button
        mat-flat-button
        [disabled]="forgotPassword.invalid"
        class="w-full mt-4 p-3">
        {{ g_const.reset }}
      </button>

      <span class="block mt-4 text-sm text-gray-400">
        {{ g_const.dontHaveAcc }}
        <a
          href="javascript:void(0)"
          class="text-txt-color no-underline"
          (click)="navigateTosignUp()"
          >{{ g_const.signUp }}</a
        >
      </span>
    </form>
  </div>
</div>
