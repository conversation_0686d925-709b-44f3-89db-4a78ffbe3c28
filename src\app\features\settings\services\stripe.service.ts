import { Injectable } from '@angular/core';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { environment } from '../../../env/env';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import {
  PlanSubscription,
  PlanType,
  StandardPlan,
  SubscriptionType,
} from '../../../_models/plan.model';
import { jwtDecode } from 'jwt-decode';

@Injectable({
  providedIn: 'root',
})
export class StripeService {
  // stripe initialize reference
  stripe!: Stripe | null;

  // the backend api endpoint
  private url = `${environment.apiUrl}`;

  constructor(private http: HttpClient) {}

  /**
   * Create the Http header to set in the api request.
   * @returns Http headers
   */
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  // Initialize Stripe
  async initializeStripe(): Promise<void> {
    try {
      // Load the Stripe.js library
      this.stripe = await loadStripe(environment.stripe.publicKey);
      if (!this.stripe) {
        throw new Error('Stripe initialization failed');
      }
    } catch (error) {
      console.error('Error initializing Stripe:', error);
      throw new Error(
        'There was an issue initializing Stripe. Please try again later.',
      );
    }
  }

  // Handle Stripe Checkout session (plan purchase)
  async purchasePlan(
    planId: PlanType,
    billingCycle: SubscriptionType,
  ): Promise<void> {
    if (!this.stripe) {
      throw new Error('Stripe is not initialized');
    }

    try {
      // Call the backend to create a Checkout session
      const session = await firstValueFrom(
        this.http.post<{ checkout_url: string; session_id: string }>(
          `${this.url}subscriptions/create-checkout-session/`,
          {
            plan_type: planId,
            billing_cycle: billingCycle,
          },
          { headers: this.getHeaders() },
        ),
      );

      if (session && session.checkout_url) {
        window.open(session.checkout_url, '_blank');
        return;
      }
      if (!session || !session.session_id) {
        return Promise.reject(new Error('Invalid session ID'));
      }

      // Redirect to Stripe Checkout page
      await this.stripe.redirectToCheckout({ sessionId: session.session_id });
    } catch (error) {
      throw new Error(
        'There was an issue processing your payment. Please try again later:' +
          error,
      );
    }
  }

  /**
   * To verify the subscription status
   * @returns
   */
  async verifySubscriptionStatus(): Promise<PlanSubscription> {
    try {
      // Call the backend to verify the subscription status
      const status = await firstValueFrom(
        this.http.get<PlanSubscription>(
          `${this.url}subscriptions/subscription-status/`,
          { headers: this.getHeaders() },
        ),
      );

      return status;
    } catch (error) {
      console.error('Error verifying subscription status:', error);
      throw new Error(
        'There was an issue verifying your subscription status. Please try again later.',
      );
    }
  }

  /**
   * To get all the purchase plans.
   * @returns
   */
  async getAllPurchasePlan(): Promise<StandardPlan> {
    try {
      // Call the backend to verify the subscription status
      const subscriptionPlans = await firstValueFrom(
        this.http.get<StandardPlan>(
          `${this.url}subscriptions/plans/?standard=true`,
          {
            headers: this.getHeaders(),
          },
        ),
      );
      return subscriptionPlans;
    } catch (error) {
      console.error('Error fetching purchase plans:', error);
      throw new Error(
        'There was an issue fetching purchase plan. Please try again later.',
      );
    }
  }

  /**
   * Redirect user to the Billing Portal (Stripe's subscription management page)
   */
  async goToBillingPortal(): Promise<void> {
    try {
      const session = await firstValueFrom(
        this.http.post<{ portal_url: string; session_id: string }>(
          `${this.url}subscriptions/create-billing-portal-session/`,
          null,
          { headers: this.getHeaders() },
        ),
      );

      if (!session || !session.portal_url) {
        return Promise.reject(new Error('Invalid session url'));
      }
      // Redirect the user to the Billing Portal in new tab
      window.open(session.portal_url, '_blank');
    } catch (error) {
      console.error('Error while redirecting to billing portal:', error);
      throw new Error(
        'There was an issue redirecting to billing portal. Please try again later.',
      );
    }
  }

  /**
   * Report issue to support.
   */
  async reportToSupport(): Promise<void> {
    try {
      const token = localStorage.getItem('access_token');
      const userEmail = localStorage.getItem('email');
      const decodedToken: { user_id: number } = jwtDecode(token!);
      await firstValueFrom(
        this.http.post<string>(
          `${this.url}subscriptions/create-checkout-session/`,
          { user_id: decodedToken?.user_id, user_email: userEmail },
          { headers: this.getHeaders() },
        ),
      );
    } catch (error) {
      throw new Error(
        'There was an issue in reporting to support. Please try again later:' +
          error,
      );
    }
  }
}
