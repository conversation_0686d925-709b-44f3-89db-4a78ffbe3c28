/* eslint-disable @typescript-eslint/no-unused-vars */
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { Router } from '@angular/router';
import { ChangeDetectorRef } from '@angular/core';
import { OuterLayoutComponent } from './outer-layout.component';
import { LoaderService } from '../../../services/loader.service';

describe('OuterLayoutComponent', () => {
  let component: OuterLayoutComponent;
  let fixture: ComponentFixture<OuterLayoutComponent>;
  let _router: Router;
  let _loaderService: LoaderService;
  let _cdr: ChangeDetectorRef;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OuterLayoutComponent],
      imports: [RouterTestingModule],
      providers: [LoaderService, ChangeDetectorRef],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OuterLayoutComponent);
    component = fixture.componentInstance;
    _router = TestBed.inject(Router);
    _loaderService = TestBed.inject(LoaderService);
    _cdr = TestBed.inject(ChangeDetectorRef);
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currPage).toEqual('projects');
    expect(component.titleText).toEqual('');
  });

  it('should set header values based on dashboard route', () => {
    const testUrl = '/dashboard';
    component.setHeaderValues(component.splitUrl(testUrl));
    expect(component.currPage).toEqual('projects');
    expect(component.titleText).toEqual('');
  });

  it('should set header values based on explore-view route', () => {
    const testUrl = '/explore-view';
    component.setHeaderValues(component.splitUrl(testUrl));
    expect(component.currPage).toEqual('explore-view');
    expect(component.titleText).toEqual('Visual Data Exploration');
  });

  it('should set header values based on data-view route', () => {
    const testUrl = '/data-view';
    component.setHeaderValues(component.splitUrl(testUrl));
    expect(component.currPage).toEqual('data-view');
    expect(component.titleText).toEqual('Tabular Data View');
  });

  it('should set header values based on training route', () => {
    const testUrl = '/training';
    component.setHeaderValues(component.splitUrl(testUrl));
    expect(component.currPage).toEqual('training');
    expect(component.titleText).toEqual('ML Model Recommendation');
  });

  it('should set header values based on results route', () => {
    const testUrl = '/results';
    component.setHeaderValues(component.splitUrl(testUrl));
    expect(component.currPage).toEqual('results');
    expect(component.titleText).toEqual('Model Training Overview');
  });

  it('should correctly split URL', () => {
    const testUrl1 = '/explore-view';
    const testUrl2 = '/data-view?param=value';
    expect(component.splitUrl(testUrl1)).toEqual('explore-view');
    expect(component.splitUrl(testUrl2)).toEqual('data-view');
  });
});
