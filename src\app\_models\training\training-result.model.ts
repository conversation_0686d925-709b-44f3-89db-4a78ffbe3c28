export interface TrainingResultRes {
  header_options: string[];
  message: string;
}

export interface DataSetInfoRes {
  message: string;
  data: DataInfo[];
  total_pages: number;
}

export interface DataInfo {
  file_name: string;
  columns: string[];
}

export interface PreprocessingRes {
  message: string;
  //TODO Change
  preprocessing: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  data: PreProcessingData;
}

export interface PreProcessingData {
  'train_split_%': number;
  train_split_rows: number;
  'test_split_%': number;
  test_split_rows: number;
}

export interface AdvancedPlotsResult {
  message: string;
  //TODO Change
  sidebar_options: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

export interface ImportantFeaturesResult {
  message: string;
  plot_type: string;
  image_base64: string;
  interpretation: Interpretation;
  class?: number;
  total_pages?: number;
}

export interface Interpretation {
  info_summary_plot: string;
  info_SHAP?: string;
  info_SHAP_advantage?: string[];
  info_summary_plot_explanation?: string[];
}

export interface DependencePlotClassesResult {
  class: number[];
  data_type: string;
  axis_options: string[];
}
