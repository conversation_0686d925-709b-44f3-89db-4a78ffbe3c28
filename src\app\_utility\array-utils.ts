/**
 * Check whether the value is an instance of array.
 * @param value - the value to be checked for is array or not.
 * @returns
 */
export function isAnArray(value: unknown): boolean {
  return Array.isArray(value);
}

/**
 * To join the values within an array with the separator.
 * @param value - the value on which to perform the join.
 * @param separator - separator used to join the value.
 * @returns the formatted value.
 */
//TODO change type
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function safeJoin(value: any, separator = '-'): string {
  if (isAnArray(value)) {
    return value.join(separator); // Join array values with the separator
  }
  return value; // Return the original value if it's not an array
}
