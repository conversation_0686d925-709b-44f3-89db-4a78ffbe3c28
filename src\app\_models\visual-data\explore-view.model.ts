export interface PlotDescriptionResponse {
  success: string;
  data: {
    description: string;
  };
}

export interface PlotTypeValue {
  category: string;
  icon: string;
  plot_name: string;
}

export interface Aggregation {
  aggregation_description: string;
  aggregation_type: string;
}

export interface OptionSet {
  aggregations: boolean;
  option_description: string;
  option_name: string;
  required: boolean;
  multiple: boolean;
  smart_bucketing: boolean;
  datatype: string[];
}

export interface CreatePlotPayload {
  plot_name: string;
  selected_options: OptionSetPayload[];
  selected_settings: SettingsSetPayload[];
  selected_smart_bucketing: BucketingSetPayload[];
}

export interface OptionSetPayload {
  option_name: string;
  selected_aggregation: string;
  selected_column_name: string;
}

export interface SettingsSetPayload {
  selected_setting_value: boolean;
  setting_name: string;
}

export interface BucketingSetPayload {
  smart_bucketing_name: string;
  smart_bucketing_value: string;
}

export interface PlotOption {
  option_name: string;
  selected_column_name: string;
  selected_aggregation: string | null;
  selected_smart_bucketing: string | null;
}

export interface PlotSetting {
  setting_name: string;
  selected_setting_value: boolean;
}

export interface PlotData {
  plot_name: string;
  selected_options: PlotOption[];
  selected_settings: PlotSetting[];
  description: string | null;
  interpretation: string | null;
  plot_style: number;
}

export interface PlotEditInfo {
  status: string;
  message: string;
  data: PlotData;
  errors: string;
  description: string;
}
