import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { StorageService } from '../services/storage.service';
@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private storageService: StorageService,
  ) {}

  canActivate(): boolean {
    try {
      const token = this.storageService.getItem('access_token');

      if (!token) {
        this.redirectToLogin();
        return false;
      }

      const decodedToken: { exp: number } = jwtDecode(token);

      // Check if the token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (decodedToken.exp < currentTime) {
        this.clearStorageAndRedirect();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating token:', error);
      this.clearStorageAndRedirect();
      return false;
    }
  }

  private redirectToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  private clearStorageAndRedirect(): void {
    this.storageService.clear();
    this.redirectToLogin();
  }
}
