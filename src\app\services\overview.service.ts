import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../env/env';
import { Observable } from 'rxjs';
import {
  Hypothesis,
  HypothesisList,
  HypothesisPayload,
  ProjectData,
} from '../features/dashborad/models/project.model';
import { Message, ResponseData } from '../_models/common.model';
import { HypothesisFilter } from '../features/dashborad/models/project.model';
import { BackendResponse } from '../_models/visual-data/visual-data.model';

@Injectable({
  providedIn: 'root',
})
export class OverviewService {
  constructor(private http: HttpClient) {}

  private overviewUrl = `${environment.apiUrl}projects/`;

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  deleteProject(id: string): Observable<Message> {
    return this.http.delete<Message>(`${this.overviewUrl}/projects/${id}/`, {
      headers: this.getHeaders(),
    });
  }

  getProjectsById(
    project_id: string,
  ): Observable<BackendResponse<ProjectData>> {
    return this.http.get<BackendResponse<ProjectData>>(
      `${this.overviewUrl}projects/${project_id}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  createHypothesis(
    data: HypothesisPayload,
    project_id: number,
  ): Observable<ResponseData<Hypothesis>> {
    return this.http.post<ResponseData<Hypothesis>>(
      `${this.overviewUrl}projects/${project_id}/hypothesis/`,
      data,
      { headers: this.getHeaders() },
    );
  }

  deleteHypothesis(id: number, data: HypothesisPayload): Observable<Message> {
    return this.http.delete<Message>(
      `${this.overviewUrl}projects/hypothesis/${id}/`,
      {
        headers: this.getHeaders(),
        body: data,
      },
    );
  }

  getHypothesis(
    project_id: string,
    filters?: HypothesisFilter,
  ): Observable<HypothesisList> {
    let params = new HttpParams();

    if (filters) {
      if (filters.hypothesis_type) {
        params = params.set('hypothesis_type', filters.hypothesis_type);
      }
      if (filters.minDate) {
        params = params.set('min_date', filters.minDate);
      }
      if (filters.maxDate) {
        params = params.set('max_date', filters.maxDate);
      }
    }

    return this.http.get<HypothesisList>(
      `${this.overviewUrl}projects/${project_id}/hypothesis/`,
      {
        headers: this.getHeaders(),
        params: params,
      },
    );
  }

  editProject(
    data: HypothesisPayload,
    id: number,
  ): Observable<ResponseData<Hypothesis>> {
    return this.http.put<ResponseData<Hypothesis>>(
      `${this.overviewUrl}projects/hypothesis/${id}/`,
      data,
      { headers: this.getHeaders() },
    );
  }
}
