<app-loader [loading]="loading"></app-loader>
<mat-dialog-content class="p-6">
  <!-- Mo<PERSON> Header -->
  <div
    mat-dialog-title
    class="flex justify-between items-center border-b p-0 pb-6 content-none upgrade-header">
    <h3>Upgrade Plan</h3>
    <button mat-icon-button (click)="closeModal()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Modal Body with Plans -->
  <div class="flex w-full gap-[10px] py-4">
    @for (plan of plans; track plan.id) {
      <mat-card class="flex-1">
        <mat-card-header class="px-5 py-3 border-b flex-1 w-full">
          <mat-card-title class="flex gap-2 items-center justify-between">
            <div class="flex gap-2 items-center">
              <mat-icon>{{ 'download_done' }}</mat-icon>
              <span class="font-medium text-base">{{ plan.name }}</span>
            </div>
            <mat-icon>navigate_next</mat-icon>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content class="px-6 pt-3 pb-0">
          <div class="flex items-baseline">
            <span class="text-5xl font-bold">{{
              plan.selectedBillingCycle === 'yearly'
                ? plan.yearly_price
                : plan.original_monthly_price
            }}</span>
            @if (plan.selectedBillingCycle === 'yearly') {
              <span class="line-through ml-2 text-2xl font-bold">{{
                plan.original_yearly_price
              }}</span>
            }
          </div>
          <p class="text-sm">
            Choose Annual and Save {{ plan.yearly_discount }}%
          </p>
          <mat-button-toggle-group
            name="{{ plan.id }}options"
            class="segmented-button-group mt-3"
            [(ngModel)]="plan.selectedBillingCycle">
            <mat-button-toggle value="monthly">Monthly</mat-button-toggle>
            <mat-button-toggle value="yearly"
              >Annually
              <span class="ml-1"> (Save {{ plan.yearly_discount }}%) </span>
            </mat-button-toggle>
          </mat-button-toggle-group>
          <button mat-flat-button class="w-full mt-3" (click)="buyPlan(plan)">
            Buy Plan
          </button>
          <mat-list>
            @for (feature of plan.features; track feature) {
              <mat-list-item class="px-0 [&:not(:last-child)]:border-b">
                <div class="flex gap-2 items-center">
                  <mat-icon mat-list-icon>download_done</mat-icon>
                  <p mat-line>{{ feature }}</p>
                </div>
              </mat-list-item>
            }
          </mat-list>
        </mat-card-content>
      </mat-card>
    }
  </div>

  <!-- Modal Footer -->
  <mat-dialog-actions align="center" class="mt-6">
    <p>
      Need help deciding?
      <a href="/settings" class="text-primary">Talk to Sales.</a>
    </p>
  </mat-dialog-actions>
</mat-dialog-content>
