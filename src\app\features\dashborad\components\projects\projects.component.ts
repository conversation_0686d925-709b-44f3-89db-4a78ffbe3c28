import {
  After<PERSON>iewInit,
  <PERSON>mponent,
  <PERSON>ement<PERSON>ef,
  HostListener,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { SearchOptions } from '../../../../_models/common.model';
import {
  NewProjectFormValues,
  Project,
  ProjectList,
  ProjectPayload,
} from '../../models/project.model';
import { ProjectService } from '../../services/project.service';
import { g_const } from '../../../../_utility/global_const';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { IntroService } from '../../../../introjs.service';
import { PaginationService } from '../../services/pagination.service';
import { Store } from '@ngrx/store';
import { changeTab } from '../../../../core/store/actions/tab.action';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class ProjectsComponent implements OnInit, AfterViewInit, OnDestroy {
  // Global Constants
  g_const = g_const;

  // State Variables
  projects: Project[] = [];
  searchOptions: SearchOptions = {};
  formData: Project | null = null;
  currentPage = 1;
  pageSize = 9;
  hasMore = true;
  loading = false;
  nextUrl: string | null = null;

  // UI Control Variables
  projectPopup = false;
  projectDeletePopup = false;
  projectDeleteTitle = '';
  projectId!: number;
  paginationList: number[] = [];

  // Screen Information
  screenWidth: number | null = null;

  // Subscriptions
  private searchSubscription: Subscription = new Subscription();
  @ViewChild('scrollContainer') scrollContainer?: ElementRef;

  constructor(
    private projectService: ProjectService,
    private toastrService: ToastrService,
    private router: Router,
    private introService: IntroService,
    private paginationService: PaginationService,
    private store: Store,
  ) {
    this.updateScreenWidth();
  }

  // Lifecycle Hooks
  ngOnInit(): void {
    this.getProjects();
    this.introService.startIntroIfNeeded('projects');
  }

  ngAfterViewInit(): void {
    this.scrollContainer?.nativeElement.addEventListener(
      'scroll',
      this.onScroll.bind(this),
    );
  }

  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
  }

  // HostListener for screen resizing
  @HostListener('window:resize')
  updateScreenWidth(): void {
    this.screenWidth = window.innerWidth;
  }

  // Project Data Fetching
  getProjects(): void {
    if (this.loading || !this.hasMore) return;

    this.loading = true;
    this.projectService
      .getProjects(this.paginationService.calculatePageSize())
      .subscribe({
        next: (data: ProjectList) => {
          this.projects = data.data || [];
          this.hasMore = !!data.pagination.next;
          this.nextUrl = data.pagination.next || null;
          this.setPagination(this.projects.length);
          this.loading = false;
        },
        error: () => (this.loading = false),
      });
  }

  getProjectsBySearch(): void {
    this.projectService.getProjectsBySearch(this.searchOptions).subscribe({
      next: response => {
        this.projects = response.data || [];
        this.setPagination(this.projects.length);
      },
      error: err => {
        console.error('Error searching projects:', err);
      },
    });
  }

  loadMoreProjects(): void {
    if (this.loading || !this.hasMore || !this.nextUrl) return;
    this.loading = true;
    this.projectService.getProjectsByUrl(this.nextUrl).subscribe(
      (data: ProjectList) => {
        if (data.data.length > 0) {
          this.projects = [...this.projects, ...data.data];
          this.hasMore = !!data.pagination.next;
          this.nextUrl = data.pagination.next;
          this.setPagination(this.projects.length);
        } else {
          this.hasMore = false;
        }
        this.loading = false;
      },
      () => {
        this.loading = false;
      },
    );
  }

  // Search Handling
  hasSearchOptions(): boolean {
    return Object.values(this.searchOptions).some(value => !!value);
  }

  onSearchPerformed(searchOptions: SearchOptions): void {
    this.searchOptions = searchOptions;
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    this.hasSearchOptions() ? this.getProjectsBySearch() : this.getProjects();
  }

  // Pagination Helpers
  setPagination(dataLength: number): void {
    this.paginationList = this.paginationService.setPagination(
      dataLength,
      this.pageSize,
    );
  }

  // CRUD Operations
  addProject(): void {
    if (!this.formData) return;

    const newProject: ProjectPayload = {
      title: this.formData.title,
      description: this.formData.description,
    };

    this.projectService.createProject(newProject).subscribe({
      next: () => {
        this.toastrService.success('Project added successfully!');
        this.refreshProjects();
      },
      error: () => this.toastrService.error('Failed to add project'),
    });
  }

  editProject(id: number): void {
    if (!this.formData) return;

    const updatedProject: ProjectPayload = {
      title: this.formData.title,
      description: this.formData.description,
    };

    this.projectService.editProject(updatedProject, id).subscribe({
      next: () => {
        this.toastrService.success('Project updated successfully!');
        this.refreshProjects();
      },
      error: () => this.toastrService.error('Failed to edit project'),
    });
  }

  deleteProject(): void {
    this.projectService.deleteProject(this.projectId).subscribe({
      next: () => {
        this.toastrService.success('Project deleted successfully!');
        this.refreshProjects();
      },
      error: () => this.toastrService.error('Failed to delete project'),
    });
  }

  refreshProjects(): void {
    this.hasMore = true;
    this.getProjects();
  }

  // UI Helpers
  saveProject(data: NewProjectFormValues): void {
    this.projectPopup = false;

    if (data.id) {
      this.formData = { ...this.formData, ...data } as Project;
      this.editProject(data.id);
    } else {
      this.formData = {
        title: data.title,
        description: data.description,
      } as Project;
      this.addProject();
    }
  }

  confirmDelete(id: number, title: string): void {
    this.projectId = id;
    this.projectDeleteTitle = title;
    this.projectDeletePopup = true;
  }

  closePopup(): void {
    this.projectPopup = false;
    this.projectDeletePopup = false;
    this.formData = null;
  }

  getProjectTitle(title: string): string {
    return this.screenWidth && this.screenWidth >= 1750
      ? title
      : title.length > 30
        ? `${title.slice(0, 30)}...`
        : title;
  }

  get paginatedProjects(): Project[] {
    if (!this.projects || this.projects.length === 0) {
      return [];
    }
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.max(startIndex + this.pageSize, this.projects.length);
    return this.projects.slice(startIndex, endIndex);
  }

  selectProject(id: number): void {
    this.router.navigate(['/project', id, 'Overview']);
    this.store.dispatch(changeTab({ selectedTab: 'Overview' }));
    this.introService.goToNextStep();
  }

  openEditForm(data: Project): void {
    this.formData = data;
    this.projectPopup = true;
  }

  openAddForm(): void {
    this.formData = null;
    this.projectPopup = true;
  }

  // Scroll Handling
  @HostListener('window:scroll', ['$event'])
  onWindowScroll(): void {
    const scrollPosition = window.scrollY + window.innerHeight;
    const threshold = document.documentElement.scrollHeight;

    if (scrollPosition >= threshold && this.hasMore && !this.loading) {
      this.loadMoreProjects();
    }
  }

  onScroll(): void {
    if (this.scrollContainer) {
      const container = this.scrollContainer.nativeElement;
      const atBottom =
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 50;
      if (atBottom && this.hasMore && !this.loading) this.loadMoreProjects();
    }
  }
}
