import { Component, Input, TemplateRef, ViewChild } from '@angular/core';

interface ProjectModalContext {
  title: string;
  description: string;
  //TODO: Add any other properties expected in the context
}

@Component({
  selector: 'app-cards',
  templateUrl: './cards.component.html',
  styleUrl: './cards.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class CardsComponent {
  @ViewChild('projectModal') projectModal!: TemplateRef<ProjectModalContext>;

  @Input() title = 'Cirrhosis Prediction';
  @Input() description =
    'Develop image-based algorithms to identify histologically confirmed skin cancer cases with single-lesion crops from 3D total body photos (TBP). The image quality resembles close-up smartphone photos, which are regularly submitted for telehealth purposes. Improve care and triage for early skin cancer detection';
  @Input() files = 0;
  @Input() dataSize = 0;
  get truncatedDescription(): string {
    const words = this.description.split(' ');
    return words.length > 17
      ? words.slice(0, 20).join(' ') + '...'
      : this.description;
  }
  isEditModalOpen = false;
  isDeleteModalOpen = false;

  openEditModal() {
    this.isEditModalOpen = !this.isEditModalOpen;
  }
  openDeleteModal() {
    this.isDeleteModalOpen = !this.isDeleteModalOpen;
  }
}
