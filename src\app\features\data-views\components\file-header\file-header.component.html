<div class="grid grid-cols-1 lg:grid-cols-3 gap-4 justify-evenly">
  <!-- Left Section -->
  <div class="flex items-center space-x-4">
    <button mat-icon-button (click)="goBack()">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <span class="font-medium text-lg">{{
      capitalizeFirstLetter(filename)
    }}</span>
  </div>

  <!-- Center Section Of Data Toggle -->
  <div
    *ngIf="activeSection === 'data'"
    class="flex items-center space-x-2 justify-center mt-4 md:mt-0">
    <button
      mat-stroked-button
      class="border border-gray-300 h-12 flex items-center space-x-1 min-w-[120px] md:min-w-max stroke-btn"
      (click)="openAddColumnModal()">
      <mat-icon>add</mat-icon>
      <span class="whitespace-nowrap text-ellipsis">Add Column</span>
    </button>

    <button
      mat-stroked-button
      class="border border-gray-300 h-12 flex items-center space-x-1 min-w-[120px] md:min-w-max stroke-btn"
      (click)="toggleFilterDataModal()">
      <div class="flex justify-center items-center gap-2">
        <div class="relative">
          <mat-icon>filter_list</mat-icon>
          <span
            *ngIf="filter_active"
            class="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-red-600 text-white text-xs w-3 h-3 rounded-full flex items-center justify-center">
          </span>
        </div>
        <span>Filter Data</span>
      </div>
    </button>

    <button
      (click)="onButtonClick()"
      class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 stroke-btn !text-txt-color">
      <mat-icon>shuffle</mat-icon>
    </button>
  </div>

  <!-- Center Section Of Statistics Toggle -->
  <div
    *ngIf="activeSection === 'statistics'"
    class="flex flex-col xl:flex-row w-full gap-4 mt-4 md:mt-0 min-w-[410px] xl:min-w-[610px] max-w-[610px] items-center">
    <mat-button-toggle-group
      name="options"
      aria-label="Segmented button group"
      [hideSingleSelectionIndicator]="true"
      class="rounded-md bg-white border-gray-100 max-w-[330px]"
      (change)="changeTypeSection($event.value)">
      <mat-button-toggle
        checked
        value="numericalColumns"
        class="whitespace-nowrap text-ellipsis">
        Numerical Columns
      </mat-button-toggle>
      <mat-button-toggle
        value="categoricalColumns"
        class="whitespace-nowrap text-ellipsis">
        Categorical Columns
      </mat-button-toggle>
    </mat-button-toggle-group>

    <mat-button-toggle-group
      name="options"
      aria-label="Segmented button group"
      [hideSingleSelectionIndicator]="true"
      class="rounded-md bg-white border-gray-100 max-w-[280px]"
      (change)="changeProcessedSection($event.value)">
      <mat-button-toggle value="original" checked>
        <img
          src="../../../../assets/icons/table_rows.png"
          alt=""
          class="h-6 w-6 mt-2 mx-2" />
        Original
      </mat-button-toggle>
      <mat-button-toggle value="processed" class="p-0 w-auto">
        <mat-icon class="mt-1 mx-2 text-xl">auto_fix_normal</mat-icon>
        Processed
      </mat-button-toggle>
    </mat-button-toggle-group>
  </div>

  <!-- Right Section -->
  <div class="flex items-center space-x-1 justify-end min-w-[100px]">
    <mat-button-toggle-group
      name="options"
      (change)="changeSection($event.value)"
      aria-label="Segmented button group"
      [hideSingleSelectionIndicator]="true"
      class="rounded-md bg-white border-gray-100 h-[48px]">
      <mat-button-toggle checked value="data" class="min-w-[90px]"
        >Data</mat-button-toggle
      >
      <mat-button-toggle value="statistics" class="min-w-[90px]"
        >Statistics</mat-button-toggle
      >
    </mat-button-toggle-group>
  </div>
</div>
