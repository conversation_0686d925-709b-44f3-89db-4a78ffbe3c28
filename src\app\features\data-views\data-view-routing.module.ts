import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DataViewComponent } from './components/data-view/data-view.component';
import { FilesComponent } from './components/files/files.component';

const routes: Routes = [
  {
    path: 'data-view',
    component: DataViewComponent,
  },
  {
    path: 'data-view/:fileID/file',
    component: FilesComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DataViewRoutingModule {}
