.span.mdc-list-item__primary-text {
  display: inline-block;
}
.area_line-toogle {
  margin-top: 28px;
  margin-left: 20px;
}
label {
  color: #181c20;
}

.custom-ng-select {
  width: 600px;
  border: 1px solid #181c20;
  padding: 8px;
  height: 48px;
}

.plot-form {
  max-height: 80vh;
  overflow-y: auto;
}

.file-select-container {
  border: 1px solid #ddd;
  padding: 10px;
  background-color: #f7f9ff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.folder-group,
.subfolder-group,
.nested-subfolder-group {
  margin-left: 10px;
  padding: 5px;
}

.folder-name {
  font-weight: bold;
  padding: 5px 0;
}

.file-list {
  padding-left: 15px;
}

.file-select-option {
  padding: 5px 0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.file-select-option:hover {
  background-color: #e0e7ff;
}
