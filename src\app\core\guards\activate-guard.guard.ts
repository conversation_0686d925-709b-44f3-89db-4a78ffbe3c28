import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { StorageService } from '../services/storage.service';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root',
})
export class ActivateGuardGuard implements CanActivate {
  constructor(
    private router: Router,
    private storageService: StorageService,
    private toaster: ToastrService,
  ) {}

  canActivate(): boolean {
    try {
      // Retrieve activation status from storage
      const activated = this.storageService.getItem('activated');

      if (activated === 'true') {
        // If the user is already activated, redirect to the login page
        this.toaster.success('Account already activated, Please Login!');
        this.router.navigate(['/auth/login']);
        return false;
      }

      return true; // Allow access if not activated
    } catch (error) {
      console.error('Error validating activation status:', error);
      return false;
    }
  }
}
